{"name": "jnxproject-v2", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vue-tsc -b && vite build", "preview": "vite preview"}, "dependencies": {"@tailwindcss/vite": "^4.1.4", "axios": "^1.9.0", "md5": "^2.3.0", "pinia": "^3.0.2", "sweetalert2": "^11.22.0", "tailwindcss": "^4.1.4", "vue": "^3.5.13", "vue-router": "^4.5.1", "vue-toastification": "^2.0.0-rc.5"}, "devDependencies": {"@types/md5": "^2.3.5", "@vitejs/plugin-vue": "^5.2.2", "@vue/tsconfig": "^0.7.0", "typescript": "~5.7.2", "vite": "^6.3.1", "vue-tsc": "^2.2.8"}}