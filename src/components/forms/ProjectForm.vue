<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import type { Project } from '../../services/projectService';
import SearchableSelect from './SearchableSelect.vue';
import CustomerName from '../CustomerName.vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  customers: {
    type: Array as () => any[],
    default: () => []
  },
  project: {
    type: Object as () => Project | null,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);
const changeCustomer = ref(false);

const formData = ref({
  name: '',
  description: '',
  startDate: '',
  endDate: '',
  stage: 'in-progress',
  salesperson: '',
  customer: '',
  type: 'normal'
});

// If editing a project, populate the form with project data
onMounted(() => {
  if (props.project) {
    populateFormData();
  }
});

// Watch for changes in the project prop
watch(() => props.project, () => {
  if (props.project) {
    populateFormData();
  }
}, { deep: true });

const populateFormData = () => {
  if (props.project) {
    formData.value = {
      name: props.project.name || '',
      description: props.project.description || '',
      startDate: props.project.startDate || '',
      endDate: props.project.endDate || '',
      stage: props.project.stage || 'planning',
      salesperson: props.project.salesperson || '',
      customer: props.project.customer || '',
      type: props.project.type || 'normal'
    };
  }
};

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    startDate: '',
    endDate: '',
    stage: 'planning',
    salesperson: '',
    customer: '',
    type: 'normal',
  };
};

const closeForm = () => {
  if (!props.isEdit) {
    resetForm();
  }
  emit('close');
};

const saveProject = () => {
  // Validate form
  if (!formData.value.name.trim()) {
    alert('Project name is required');
    return;
  }

  // Format the data for saving
  const projectData = { ...formData.value };

  emit('save', projectData);
  closeForm();
};

const handleSearch = (query: string) => {
  console.log('Search query:', query);
};

const handleChange = (option: any) => {
  console.log('Selected option:', option);
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">{{ isEdit ? 'Edit Project' : 'Create New Project' }}</h2>
          <button @click="closeForm" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="saveProject" class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Project Type</label>
            <select v-model="formData.type" class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm">
              <option value="normal">Normal</option>
              <option value="hifi">HiFi Infra Set Up</option>
            </select>
          </div>

          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Project Name</label>
            <input
              type="text"
              id="name"
              v-model="formData.name"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            >
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="3"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            ></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="stage" class="block text-sm font-medium text-gray-700">Stage</label>
              <select
                id="stage"
                v-model="formData.stage"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="planning">Planning</option>
                <option value="in-progress">In Progress</option>
                <option value="completed">Completed</option>
              </select>
            </div>
          </div>

          <div v-if="formData.type === 'normal'" class="grid grid-cols-1 gap-4">
            <div>
              <label for="customer" class="block text-sm font-medium text-gray-700">Customer</label>
              <CustomerName v-if="project && project.customer && !changeCustomer" :customerId="project.customer" />
              <SearchableSelect v-if="changeCustomer"
                v-model="formData.customer"
                :options="customers"
                placeholder="Choose a customer"
                required
                @change="handleChange"
                @search="handleSearch"
              />
              <button @click.prevent="changeCustomer = !changeCustomer" class="text-xs ml-2 text-blue-600">
                {{ !changeCustomer ? 'Change Client' : 'Cancel'}}
              </button>
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEdit ? 'Update' : 'Create' }} Project
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
