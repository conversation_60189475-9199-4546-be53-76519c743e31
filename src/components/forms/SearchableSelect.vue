<script setup lang="ts">
import { ref, computed, watch, onMounted, onBeforeUnmount } from 'vue';

interface Option {
  id: string | number;
  name: string;
  [key: string]: any;
}

const props = defineProps({
  modelValue: {
    type: [String, Number, Object],
    default: null
  },
  options: {
    type: Array as () => Option[],
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Select an option'
  },
  label: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  searchable: {
    type: Boolean,
    default: true
  },
  valueKey: {
    type: String,
    default: 'id'
  },
  labelKey: {
    type: String,
    default: 'name'
  },
  clearable: {
    type: Boolean,
    default: true
  },
  error: {
    type: String,
    default: ''
  }
});

const emit = defineEmits(['update:modelValue', 'change', 'search']);

const isOpen = ref(false);
const searchQuery = ref('');
const selectedOption = ref<Option | null>(null);
const dropdownRef = ref<HTMLElement | null>(null);
const inputRef = ref<HTMLElement | null>(null);

// Find the selected option based on modelValue
const findSelectedOption = () => {
  if (!props.modelValue) {
    selectedOption.value = null;
    return;
  }

  if (typeof props.modelValue === 'object') {
    selectedOption.value = props.modelValue as Option;
    return;
  }

  const option = props.options.find(opt => opt[props.valueKey] === props.modelValue);
  selectedOption.value = option || null;
};

// Watch for changes in modelValue or options
watch(() => props.modelValue, findSelectedOption, { immediate: true });
watch(() => props.options, findSelectedOption);

// Filtered options based on search query
const filteredOptions = computed(() => {
  if (!searchQuery.value) return props.options;
  
  const query = searchQuery.value.toLowerCase();
  return props.options.filter(option => 
    option[props.labelKey].toLowerCase().includes(query)
  );
});

// Display value for the input
const displayValue = computed(() => {
  if (selectedOption.value) {
    return selectedOption.value[props.labelKey];
  }
  return '';
});

// Toggle dropdown
const toggleDropdown = () => {
  if (props.disabled) return;
  isOpen.value = !isOpen.value;
  
  if (isOpen.value) {
    searchQuery.value = '';
    setTimeout(() => {
      if (inputRef.value && props.searchable) {
        inputRef.value.focus();
      }
    }, 100);
  }
};

// Select an option
const selectOption = (option: Option) => {
  selectedOption.value = option;
  emit('update:modelValue', option[props.valueKey]);
  emit('change', option);
  isOpen.value = false;
  searchQuery.value = '';
};

// Clear selection
const clearSelection = (event: Event) => {
  event.stopPropagation();
  selectedOption.value = null;
  emit('update:modelValue', null);
  emit('change', null);
};

// Handle search input
const handleSearch = (event: Event) => {
  const target = event.target as HTMLInputElement;
  searchQuery.value = target.value;
  emit('search', searchQuery.value);
};

// Close dropdown when clicking outside
const handleClickOutside = (event: MouseEvent) => {
  if (dropdownRef.value && !dropdownRef.value.contains(event.target as Node)) {
    isOpen.value = false;
  }
};

// Handle keyboard navigation
const handleKeydown = (event: KeyboardEvent) => {
  if (!isOpen.value) return;
  
  switch (event.key) {
    case 'Escape':
      isOpen.value = false;
      break;
    case 'Enter':
      if (filteredOptions.value.length > 0) {
        selectOption(filteredOptions.value[0]);
      }
      break;
  }
};

// Setup event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  document.addEventListener('keydown', handleKeydown);
});

onBeforeUnmount(() => {
  document.removeEventListener('click', handleClickOutside);
  document.removeEventListener('keydown', handleKeydown);
});
</script>

<template>
  <div class="relative w-full" ref="dropdownRef">
    <!-- Label -->
    <label v-if="label" class="block text-sm font-medium text-gray-700 mb-1">
      {{ label }} <span v-if="required" class="text-red-500">*</span>
    </label>
    
    <!-- Select input -->
    <div
      @click="toggleDropdown"
      class="mt-1 relative w-full cursor-pointer bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
      :class="{
        'border-red-500': error,
        'border-gray-300': !error,
        'bg-gray-100': disabled,
        'cursor-not-allowed': disabled
      }"
    >
      <div class="flex items-center">
        <span v-if="selectedOption" class="block truncate">{{ displayValue }}</span>
        <span v-else class="block truncate text-gray-500">{{ placeholder }}</span>
        
        <!-- Clear button -->
        <button
          v-if="selectedOption && clearable && !disabled"
          type="button"
          @click="clearSelection"
          class="absolute inset-y-0 right-8 flex items-center pr-2 text-gray-400 hover:text-gray-500"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <!-- Dropdown indicator -->
        <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clip-rule="evenodd" />
          </svg>
        </span>
      </div>
    </div>
    
    <!-- Error message -->
    <div v-if="error" class="text-red-500 text-xs mt-1">{{ error }}</div>
    
    <!-- Dropdown menu -->
    <div
      v-if="isOpen"
      class="absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"
    >
      <!-- Search input -->
      <div v-if="searchable" class="px-3 py-2 sticky top-0 bg-white border-b border-gray-200">
        <input
          ref="inputRef"
          type="text"
          class="block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          placeholder="Search..."
          v-model="searchQuery"
          @input="handleSearch"
          @keydown.stop
        />
      </div>
      
      <!-- Loading state -->
      <div v-if="loading" class="px-3 py-2 text-center text-gray-500">
        Loading...
      </div>
      
      <!-- No results -->
      <div v-else-if="filteredOptions.length === 0" class="px-3 py-2 text-center text-gray-500">
        No results found
      </div>
      
      <!-- Options list -->
      <ul v-else class="py-1">
        <li
          v-for="option in filteredOptions"
          :key="option[valueKey]"
          @click="selectOption(option)"
          class="px-3 py-2 cursor-pointer hover:bg-gray-100"
          :class="{ 'bg-blue-100': selectedOption && selectedOption[valueKey] === option[valueKey] }"
        >
          {{ option[labelKey] }}
        </li>
      </ul>
    </div>
  </div>
</template>
