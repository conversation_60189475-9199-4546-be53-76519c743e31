<script setup lang="ts">
import { ref, computed, onMounted } from 'vue';
import SearchableSelect from './SearchableSelect.vue';
import { useCustomerStore } from '../../stores/customerStore';

const customerStore = useCustomerStore();
const selectedCustomerId = ref('');
const isLoading = ref(true);
const error = ref('');

// Transform customers to options format
const customerOptions = computed(() => {
  return customerStore.customers.map(customer => ({
    ...customer,
    id: customer.id || customer._id || '',
    name: customer.name
  }));
});

// Get selected customer
const selectedCustomer = computed(() => {
  if (!selectedCustomerId.value) return null;
  return customerStore.customers.find(c =>
    (c.id && c.id.toString() === selectedCustomerId.value) ||
    (c._id && c._id === selectedCustomerId.value)
  );
});

// Fetch customers on component mount
onMounted(async () => {
  try {
    isLoading.value = true;
    await customerStore.fetchCustomers({
      $limit: 100 // Fetch a reasonable number of customers
    });
    isLoading.value = false;
  } catch (err) {
    console.error('Error fetching customers:', err);
    error.value = 'Failed to load customers. Please try again.';
    isLoading.value = false;
  }
});

// Handle customer selection
const handleCustomerChange = (customer: any) => {
  console.log('Selected customer:', customer);
  // You can perform additional actions here when a customer is selected
};

// Handle search input
const handleSearch = async (query: string) => {
  if (query.length >= 2) {
    try {
      isLoading.value = true;
      await customerStore.fetchCustomers({
        $limit: 10,
        $keywords: query
      });
      isLoading.value = false;
    } catch (err) {
      console.error('Error searching customers:', err);
      isLoading.value = false;
    }
  }
};
</script>

<template>
  <div class="bg-white p-6 rounded-lg shadow">
    <h2 class="text-lg font-medium text-gray-900 mb-4">Customer Selection with API Integration</h2>
    
    <div class="mb-4">
      <SearchableSelect
        v-model="selectedCustomerId"
        :options="customerOptions"
        :loading="isLoading"
        label="Select a Customer"
        placeholder="Search for a customer..."
        valueKey="id"
        labelKey="name"
        required
        :error="error"
        @change="handleCustomerChange"
        @search="handleSearch"
      />
    </div>
    
    <div v-if="selectedCustomerId" class="mt-4 p-4 bg-gray-50 rounded-md">
      <h3 class="text-md font-medium text-gray-900 mb-2">Selected Customer Details</h3>
      <div v-if="selectedCustomer" class="text-sm text-gray-600">
        <p><span class="font-medium">ID:</span> {{ selectedCustomer.id || selectedCustomer._id }}</p>
        <p><span class="font-medium">Name:</span> {{ selectedCustomer.name }}</p>
        <p><span class="font-medium">Status:</span> {{ selectedCustomer.status }}</p>
      </div>
    </div>
  </div>
</template>
