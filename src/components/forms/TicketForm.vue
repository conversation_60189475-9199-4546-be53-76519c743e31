<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  projects: {
    type: Array as () => any[],
    default: () => []
  },
  teamMembers: {
    type: Array as () => any[],
    default: () => []
  },
  ticket: {
    type: Object,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);

const formData = ref({
  id: null,
  title: '',
  description: '',
  status: 'open',
  priority: 'medium',
  project: '',
  assignedTo: '',
  dueDate: '',
  createdBy: '',
  createdAt: '',
  updatedAt: ''
});

// Watch for changes in the ticket prop to update the form data
watch(() => props.ticket, (newTicket) => {
  if (newTicket) {
    populateFormData(newTicket);
  }
}, { immediate: true });

// Watch for changes in the show prop to reset the form when it's closed
watch(() => props.show, (newShow) => {
  if (newShow && props.ticket) {
    populateFormData(props.ticket);
  } else if (!newShow && !props.isEdit) {
    resetForm();
  }
});

// Function to populate form data from a ticket
const populateFormData = (ticket: any) => {
  if (!ticket) return;

  formData.value = {
    id: ticket.id || null,
    title: ticket.title || '',
    description: ticket.description || '',
    status: ticket.status || 'open',
    priority: ticket.priority || 'medium',
    project: ticket.project || '',
    assignedTo: ticket.assignedTo || '',
    dueDate: ticket.dueDate || '',
    createdBy: ticket.createdBy || '',
    createdAt: ticket.createdAt || '',
    updatedAt: ticket.updatedAt || ''
  };
};

// Initialize form data if a ticket is provided
onMounted(() => {
  if (props.ticket) {
    populateFormData(props.ticket);
  }
});

const resetForm = () => {
  formData.value = {
    id: null,
    title: '',
    description: '',
    status: 'open',
    priority: 'medium',
    project: '',
    assignedTo: '',
    dueDate: '',
    createdBy: '',
    createdAt: '',
    updatedAt: ''
  };
};

const closeForm = () => {
  if (!props.isEdit) {
    resetForm();
  }
  emit('close');
};

const saveTicket = () => {
  // Create a new object with the form data
  const ticketData = { ...formData.value };

  // Add or update timestamps
  if (!props.isEdit) {
    ticketData.createdAt = new Date().toISOString();
  }
  ticketData.updatedAt = new Date().toISOString();

  emit('save', ticketData);
  closeForm();
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">{{ isEdit ? 'Edit Ticket' : 'Create New Ticket' }}</h2>
          <button @click="closeForm" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="saveTicket" class="space-y-4">
          <div>
            <label for="title" class="block text-sm font-medium text-gray-700">Ticket Title</label>
            <input
              type="text"
              id="title"
              v-model="formData.title"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            >
          </div>

          <div>
            <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
            <textarea
              id="description"
              v-model="formData.description"
              rows="4"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            ></textarea>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
              <select
                id="status"
                v-model="formData.status"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="open">Open</option>
                <option value="in-progress">In Progress</option>
                <option value="resolved">Resolved</option>
                <option value="closed">Closed</option>
              </select>
            </div>
            <div>
              <label for="priority" class="block text-sm font-medium text-gray-700">Priority</label>
              <select
                id="priority"
                v-model="formData.priority"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="low">Low</option>
                <option value="medium">Medium</option>
                <option value="high">High</option>
                <option value="urgent">Urgent</option>
              </select>
            </div>
          </div>

          <div>
            <label for="project" class="block text-sm font-medium text-gray-700">Related Project</label>
            <select
              id="project"
              v-model="formData.project"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            >
              <option value="">Select a project</option>
              <option v-for="project in projects" :key="project.id" :value="project.id">
                {{ project.name }}
              </option>
            </select>
          </div>

          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label for="assignedTo" class="block text-sm font-medium text-gray-700">Assign To</label>
              <select
                id="assignedTo"
                v-model="formData.assignedTo"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="">Unassigned</option>
                <option v-for="member in teamMembers" :key="member.id" :value="member.id">
                  {{ member.firstName }} {{ member.lastName }}
                </option>
              </select>
            </div>
            <div>
              <label for="dueDate" class="block text-sm font-medium text-gray-700">Due Date</label>
              <input
                type="date"
                id="dueDate"
                v-model="formData.dueDate"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
            </div>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEdit ? 'Update Ticket' : 'Create Ticket' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
