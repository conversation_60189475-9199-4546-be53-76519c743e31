<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  department: {
    type: Object as () => Record<string, any> | null,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);

const formData = ref({
  name: '',
  code: '',
  description: '',
  manager: '',
  parentDepartment: '',
  status: 'active',
  budget: '',
  location: '',
  email: '',
  phone: ''
});

// Watch for changes in the department prop to update the form data
watch(() => props.department, (newDepartment) => {
  if (newDepartment) {
    populateFormData(newDepartment);
  }
}, { immediate: true });

// Watch for changes in the show prop to reset the form when it's closed
watch(() => props.show, (newShow) => {
  if (newShow && props.department) {
    populateFormData(props.department);
  } else if (!newShow && !props.isEdit) {
    resetForm();
  }
});

// Function to populate form data from a department
const populateFormData = (department: any) => {
  if (!department) return;

  formData.value = {
    name: department.name || '',
    code: department.code || '',
    description: department.description || '',
    manager: department.manager || '',
    parentDepartment: department.parentDepartment || '',
    status: department.status || 'active',
    budget: department.budget || '',
    location: department.location || '',
    email: department.email || '',
    phone: department.phone || ''
  };
};

// Initialize form data if a department is provided
onMounted(() => {
  if (props.department) {
    populateFormData(props.department);
  }
});

const resetForm = () => {
  formData.value = {
    name: '',
    code: '',
    description: '',
    manager: '',
    parentDepartment: '',
    status: 'active',
    budget: '',
    location: '',
    email: '',
    phone: ''
  };
};

const closeForm = () => {
  if (!props.isEdit) {
    resetForm();
  }
  emit('close');
};

const saveDepartment = () => {
  // Validate form
  if (!formData.value.name) {
    alert('Department name is required');
    return;
  }

  if (!formData.value.code) {
    alert('Department code is required');
    return;
  }

  // Create department object to save
  const departmentData = {
    ...formData.value
  };

  emit('save', departmentData);
  closeForm();
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">{{ isEdit ? 'Edit Department' : 'Add New Department' }}</h2>
          <button @click="closeForm" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="saveDepartment" class="space-y-4">
          <!-- Basic Information Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Department Name</label>
                <input
                  type="text"
                  id="name"
                  v-model="formData.name"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                >
              </div>
              <div>
                <label for="code" class="block text-sm font-medium text-gray-700">Department Code</label>
                <input
                  type="text"
                  id="code"
                  v-model="formData.code"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                >
              </div>
            </div>

            <div class="mt-4">
              <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea
                id="description"
                v-model="formData.description"
                rows="2"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              ></textarea>
            </div>

            <div class="mt-4">
              <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
              <select
                id="status"
                v-model="formData.status"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>
          </div>

          <!-- Organization Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Organization</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="manager" class="block text-sm font-medium text-gray-700">Department Manager</label>
                <input
                  type="text"
                  id="manager"
                  v-model="formData.manager"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
              </div>
              <div>
                <label for="parentDepartment" class="block text-sm font-medium text-gray-700">Parent Department</label>
                <input
                  type="text"
                  id="parentDepartment"
                  v-model="formData.parentDepartment"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
              </div>
            </div>

            <div class="mt-4">
              <label for="budget" class="block text-sm font-medium text-gray-700">Annual Budget</label>
              <div class="mt-1 relative rounded-md shadow-sm">
                <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                  <span class="text-gray-500 sm:text-sm">$</span>
                </div>
                <input
                  type="number"
                  id="budget"
                  v-model="formData.budget"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 pl-7 pr-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="0.00"
                  min="0"
                  step="0.01"
                >
              </div>
            </div>
          </div>

          <!-- Contact Information Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Contact Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="location" class="block text-sm font-medium text-gray-700">Location</label>
                <input
                  type="text"
                  id="location"
                  v-model="formData.location"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
              </div>
              <div>
                <label for="email" class="block text-sm font-medium text-gray-700">Email</label>
                <input
                  type="email"
                  id="email"
                  v-model="formData.email"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                >
              </div>
            </div>

            <div class="mt-4">
              <label for="phone" class="block text-sm font-medium text-gray-700">Phone</label>
              <input
                type="tel"
                id="phone"
                v-model="formData.phone"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              >
            </div>
          </div>

          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEdit ? 'Update Department' : 'Save Department' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
