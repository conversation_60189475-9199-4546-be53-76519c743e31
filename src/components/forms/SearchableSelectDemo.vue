<script setup lang="ts">
import { ref, onMounted } from 'vue';
import SearchableSelect from './SearchableSelect.vue';

// Sample data for demonstration
const countries = ref([
  { id: 'us', name: 'United States' },
  { id: 'ca', name: 'Canada' },
  { id: 'mx', name: 'Mexico' },
  { id: 'uk', name: 'United Kingdom' },
  { id: 'fr', name: 'France' },
  { id: 'de', name: 'Germany' },
  { id: 'jp', name: 'Japan' },
  { id: 'cn', name: 'China' },
  { id: 'in', name: 'India' },
  { id: 'br', name: 'Brazil' },
  { id: 'au', name: 'Australia' }
]);

const users = ref([
  { id: 1, firstName: '<PERSON>', lastName: 'Doe', email: '<EMAIL>', name: '<PERSON>' },
  { id: 2, firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', name: '<PERSON>' },
  { id: 3, firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', name: '<PERSON>' },
  { id: 4, firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', name: '<PERSON>' },
  { id: 5, firstName: '<PERSON>', lastName: '<PERSON>', email: '<EMAIL>', name: 'Charlie Brown' }
]);

// Selected values
const selectedCountry = ref('');
const selectedUser = ref<string | number | Record<string, any> | undefined>(undefined);
const selectedCustomUser = ref<string | number | Record<string, any> | undefined>(undefined);
const isLoading = ref(false);

// Custom label formatter for users (if needed)
// const formatUserLabel = (user: any) => {
//   return `${user.firstName} ${user.lastName} (${user.email})`;
// };

// Simulate loading data
const loadingOptions = ref<any[]>([]);
const loadData = () => {
  isLoading.value = true;
  
  // Simulate API call delay
  setTimeout(() => {
    loadingOptions.value = [
      { id: 1, name: 'Option 1' },
      { id: 2, name: 'Option 2' },
      { id: 3, name: 'Option 3' }
    ];
    isLoading.value = false;
  }, 1500);
};

// Load data on mount
onMounted(() => {
  loadData();
});

// Handle search event
const handleSearch = (query: string) => {
  console.log('Search query:', query);
};

// Handle change event
const handleChange = (option: any) => {
  console.log('Selected option:', option);
};
</script>

<template>
  <div class="p-6 space-y-8">
    <h1 class="text-2xl font-bold text-gray-900">Searchable Select Component Demo</h1>
    
    <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
      <!-- Basic example -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Basic Example</h2>
        <SearchableSelect
          v-model="selectedCountry"
          :options="countries"
          label="Select a country"
          placeholder="Choose a country"
          required
          @change="handleChange"
          @search="handleSearch"
        />
        <div class="mt-2 text-sm text-gray-600">
          Selected value: {{ selectedCountry }}
        </div>
      </div>
      
      <!-- Custom label and value keys -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Custom Label and Value Keys</h2>
        <SearchableSelect
          v-model="selectedUser"
          :options="users"
          label="Select a user"
          placeholder="Choose a user"
          labelKey="firstName"
          @change="handleChange"
        />
        <div class="mt-2 text-sm text-gray-600">
          Selected value: {{ selectedUser }}
        </div>
      </div>
      
      <!-- Loading state -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Loading State</h2>
        <SearchableSelect
          v-model="selectedCustomUser"
          :options="loadingOptions"
          :loading="isLoading"
          label="Loading example"
          placeholder="Options are loading..."
        />
        <button 
          @click="loadData" 
          class="mt-2 inline-flex items-center px-3 py-2 border border-transparent text-sm leading-4 font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
        >
          Reload data
        </button>
      </div>
      
      <!-- Disabled state -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Disabled State</h2>
        <SearchableSelect
          v-model="selectedCountry"
          :options="countries"
          label="Disabled select"
          placeholder="This select is disabled"
          disabled
        />
      </div>
      
      <!-- With error -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">With Error</h2>
        <SearchableSelect
          v-model="selectedCountry"
          :options="countries"
          label="Select with error"
          placeholder="Choose a country"
          error="This field is required"
        />
      </div>
      
      <!-- Non-searchable -->
      <div class="bg-white p-6 rounded-lg shadow">
        <h2 class="text-lg font-medium text-gray-900 mb-4">Non-searchable</h2>
        <SearchableSelect
          v-model="selectedCountry"
          :options="countries"
          label="Non-searchable select"
          placeholder="Choose a country"
          :searchable="false"
        />
      </div>
    </div>
  </div>
</template>
