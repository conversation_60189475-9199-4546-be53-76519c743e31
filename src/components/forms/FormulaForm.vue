<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, computed } from 'vue';

interface FormulaVariable {
  name: string;
  description: string;
  type: string;
  defaultValue: string;
}

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  formula: {
    type: Object,
    default: () => null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);

const formData = ref({
  name: '',
  description: '',
  category: '',
  expression: '',
  variables: [] as FormulaVariable[],
  isActive: true,
  sampleInput: '',
  sampleOutput: ''
});

// For managing variables
const newVariable = ref<FormulaVariable>({
  name: '',
  description: '',
  type: 'number',
  defaultValue: ''
});

// Available variable types
const variableTypes = [
  { id: 'number', name: 'Number' },
  { id: 'string', name: 'Text' },
  { id: 'boolean', name: '<PERSON>ole<PERSON>' },
  { id: 'date', name: 'Date' }
];

// Available formula categories
const categories = [
  { id: 'financial', name: 'Financial' },
  { id: 'statistical', name: 'Statistical' },
  { id: 'mathematical', name: 'Mathematical' },
  { id: 'logical', name: 'Logical' },
  { id: 'text', name: 'Text Processing' },
  { id: 'date', name: 'Date & Time' },
  { id: 'custom', name: 'Custom' }
];

// If editing a formula, populate the form with formula data
onMounted(() => {
  if (props.formula) {
    formData.value = {
      ...formData.value,
      name: props.formula.name || '',
      description: props.formula.description || '',
      category: props.formula.category || '',
      expression: props.formula.expression || '',
      variables: props.formula.variables ? [...props.formula.variables] : [],
      isActive: props.formula.isActive !== undefined ? props.formula.isActive : true,
      sampleInput: props.formula.sampleInput || '',
      sampleOutput: props.formula.sampleOutput || ''
    };
  }
});

const resetForm = () => {
  formData.value = {
    name: '',
    description: '',
    category: '',
    expression: '',
    variables: [],
    isActive: true,
    sampleInput: '',
    sampleOutput: ''
  };
  newVariable.value = {
    name: '',
    description: '',
    type: 'number',
    defaultValue: ''
  };
};

const closeForm = () => {
  resetForm();
  emit('close');
};

const addVariable = () => {
  // Validate variable name
  if (!newVariable.value.name) {
    alert('Variable name is required');
    return;
  }
  
  // Check for duplicate variable names
  if (formData.value.variables.some(v => v.name === newVariable.value.name)) {
    alert('Variable name already exists');
    return;
  }
  
  // Add the variable to the list
  formData.value.variables.push({ ...newVariable.value });
  
  // Reset the new variable form
  newVariable.value = {
    name: '',
    description: '',
    type: 'number',
    defaultValue: ''
  };
};

const removeVariable = (index: number) => {
  formData.value.variables.splice(index, 1);
};

const saveFormula = () => {
  // Validate form
  if (!formData.value.name) {
    alert('Formula name is required');
    return;
  }
  
  if (!formData.value.expression) {
    alert('Formula expression is required');
    return;
  }
  
  // Create formula object to save
  const formulaData = {
    ...formData.value
  };
  
  emit('save', formulaData);
  closeForm();
};

// For testing the formula
const testResult = ref('');
const testInputs = ref<Record<string, any>>({});

// Initialize test inputs with default values from variables
const initializeTestInputs = () => {
  const inputs: Record<string, any> = {};
  formData.value.variables.forEach(variable => {
    inputs[variable.name] = variable.defaultValue || '';
  });
  testInputs.value = inputs;
};

// Test the formula with the current inputs
const testFormula = () => {
  try {
    // Create a function from the formula expression
    const variableNames = formData.value.variables.map(v => v.name);
    const functionBody = `return ${formData.value.expression};`;
    const formulaFunction = new Function(...variableNames, functionBody);
    
    // Get the values for each variable
    const values = formData.value.variables.map(v => {
      const value = testInputs.value[v.name];
      
      // Convert value based on type
      if (v.type === 'number') {
        return parseFloat(value);
      } else if (v.type === 'boolean') {
        return value === 'true' || value === true;
      } else {
        return value;
      }
    });
    
    // Execute the formula
    const result = formulaFunction(...values);
    testResult.value = result;
  } catch (error: any) {
    testResult.value = `Error: ${error.message || 'Unknown error'}`;
  }
};

// Format the expression with syntax highlighting (simplified version)
const formattedExpression = computed(() => {
  if (!formData.value.expression) return '';
  
  let formatted = formData.value.expression;
  
  // Highlight variables
  formData.value.variables.forEach(variable => {
    const regex = new RegExp(variable.name, 'g');
    formatted = formatted.replace(regex, `<span class="text-blue-600">${variable.name}</span>`);
  });
  
  // Highlight operators
  const operators = ['+', '-', '*', '/', '(', ')', '=', '>', '<', '>=', '<=', '&&', '||', '!'];
  operators.forEach(op => {
    const regex = new RegExp('\\' + op, 'g');
    formatted = formatted.replace(regex, `<span class="text-red-600">${op}</span>`);
  });
  
  return formatted;
});
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">{{ isEdit ? 'Edit Formula' : 'Add New Formula' }}</h2>
          <button @click="closeForm" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>
        
        <form @submit.prevent="saveFormula" class="space-y-6">
          <!-- Basic Information Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Basic Information</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="name" class="block text-sm font-medium text-gray-700">Formula Name</label>
                <input 
                  type="text" 
                  id="name" 
                  v-model="formData.name" 
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                >
              </div>
              <div>
                <label for="category" class="block text-sm font-medium text-gray-700">Category</label>
                <select 
                  id="category" 
                  v-model="formData.category" 
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  required
                >
                  <option value="">Select Category</option>
                  <option v-for="category in categories" :key="category.id" :value="category.id">
                    {{ category.name }}
                  </option>
                </select>
              </div>
            </div>
            
            <div class="mt-4">
              <label for="description" class="block text-sm font-medium text-gray-700">Description</label>
              <textarea 
                id="description" 
                v-model="formData.description" 
                rows="2"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              ></textarea>
            </div>
            
            <div class="mt-4 flex items-center">
              <input 
                type="checkbox" 
                id="isActive" 
                v-model="formData.isActive" 
                class="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
              >
              <label for="isActive" class="ml-2 block text-sm text-gray-900">
                Active
              </label>
            </div>
          </div>
          
          <!-- Variables Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Variables</h3>
            
            <!-- Add new variable form -->
            <div class="bg-gray-50 p-4 rounded-lg mb-4">
              <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                <div>
                  <label for="varName" class="block text-sm font-medium text-gray-700">Name</label>
                  <input 
                    type="text" 
                    id="varName" 
                    v-model="newVariable.name" 
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="e.g. amount"
                  >
                </div>
                <div>
                  <label for="varType" class="block text-sm font-medium text-gray-700">Type</label>
                  <select 
                    id="varType" 
                    v-model="newVariable.type" 
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  >
                    <option v-for="type in variableTypes" :key="type.id" :value="type.id">
                      {{ type.name }}
                    </option>
                  </select>
                </div>
                <div>
                  <label for="varDefault" class="block text-sm font-medium text-gray-700">Default Value</label>
                  <input 
                    type="text" 
                    id="varDefault" 
                    v-model="newVariable.defaultValue" 
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    placeholder="e.g. 0"
                  >
                </div>
                <div class="flex items-end">
                  <button 
                    type="button"
                    @click="addVariable"
                    class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500 w-full"
                  >
                    Add Variable
                  </button>
                </div>
              </div>
              <div class="mt-2">
                <label for="varDescription" class="block text-sm font-medium text-gray-700">Description</label>
                <input 
                  type="text" 
                  id="varDescription" 
                  v-model="newVariable.description" 
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="e.g. The principal amount for calculation"
                >
              </div>
            </div>
            
            <!-- Variables list -->
            <div class="bg-white border border-gray-200 rounded-lg overflow-hidden">
              <table class="min-w-full divide-y divide-gray-200">
                <thead class="bg-gray-50">
                  <tr>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Name
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Type
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Default Value
                    </th>
                    <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                      Description
                    </th>
                    <th scope="col" class="relative px-6 py-3">
                      <span class="sr-only">Actions</span>
                    </th>
                  </tr>
                </thead>
                <tbody class="bg-white divide-y divide-gray-200">
                  <tr v-for="(variable, index) in formData.variables" :key="index" class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                      {{ variable.name }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ variableTypes.find(t => t.id === variable.type)?.name || variable.type }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ variable.defaultValue || '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                      {{ variable.description || '-' }}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                      <button 
                        type="button"
                        @click="removeVariable(index)" 
                        class="text-red-600 hover:text-red-900"
                      >
                        Remove
                      </button>
                    </td>
                  </tr>
                  <tr v-if="formData.variables.length === 0">
                    <td colspan="5" class="px-6 py-4 text-center text-sm text-gray-500">
                      No variables added yet. Add variables to use in your formula.
                    </td>
                  </tr>
                </tbody>
              </table>
            </div>
          </div>
          
          <!-- Formula Expression Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Formula Expression</h3>
            <div class="mb-4">
              <label for="expression" class="block text-sm font-medium text-gray-700">Expression</label>
              <textarea 
                id="expression" 
                v-model="formData.expression" 
                rows="3"
                class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm font-mono"
                placeholder="e.g. amount * (1 + rate/100) ^ years"
                required
              ></textarea>
              <p class="mt-2 text-sm text-gray-500">
                Use JavaScript syntax. Available variables: 
                <span v-for="(variable, index) in formData.variables" :key="index" class="font-mono text-blue-600">
                  {{ variable.name }}{{ index < formData.variables.length - 1 ? ', ' : '' }}
                </span>
              </p>
            </div>
            
            <div v-if="formData.expression" class="bg-gray-50 p-4 rounded-lg mb-4">
              <h4 class="text-sm font-medium text-gray-700 mb-2">Preview:</h4>
              <div class="font-mono text-sm p-2 bg-white border border-gray-200 rounded" v-html="formattedExpression"></div>
            </div>
          </div>
          
          <!-- Test Formula Section -->
          <div v-if="formData.variables.length > 0 && formData.expression">
            <h3 class="text-lg font-medium text-gray-900 mb-4">Test Formula</h3>
            <div class="bg-gray-50 p-4 rounded-lg">
              <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
                <div v-for="(variable, index) in formData.variables" :key="index">
                  <label :for="`test-${variable.name}`" class="block text-sm font-medium text-gray-700">
                    {{ variable.name }}
                  </label>
                  <input 
                    :type="variable.type === 'number' ? 'number' : 'text'" 
                    :id="`test-${variable.name}`" 
                    v-model="testInputs[variable.name]" 
                    class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                    :placeholder="variable.defaultValue || ''"
                  >
                </div>
              </div>
              
              <div class="flex items-center space-x-4">
                <button 
                  type="button"
                  @click="initializeTestInputs(); testFormula()"
                  class="inline-flex justify-center py-2 px-4 border border-gray-300 shadow-sm text-sm font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Reset & Test
                </button>
                <button 
                  type="button"
                  @click="testFormula"
                  class="inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                  Test Formula
                </button>
                
                <div class="ml-4 flex items-center">
                  <span class="text-sm font-medium text-gray-700 mr-2">Result:</span>
                  <span class="font-mono text-sm bg-white px-3 py-1 border border-gray-200 rounded">
                    {{ testResult !== '' ? testResult : '...' }}
                  </span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Sample Usage Section -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-4">Sample Usage</h3>
            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <label for="sampleInput" class="block text-sm font-medium text-gray-700">Sample Input</label>
                <textarea 
                  id="sampleInput" 
                  v-model="formData.sampleInput" 
                  rows="3"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="e.g. amount = 1000, rate = 5, years = 3"
                ></textarea>
              </div>
              <div>
                <label for="sampleOutput" class="block text-sm font-medium text-gray-700">Sample Output</label>
                <textarea 
                  id="sampleOutput" 
                  v-model="formData.sampleOutput" 
                  rows="3"
                  class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
                  placeholder="e.g. 1157.63"
                ></textarea>
              </div>
            </div>
          </div>
          
          <!-- Form Actions -->
          <div class="flex justify-end space-x-3 pt-4">
            <button 
              type="button" 
              @click="closeForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button 
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEdit ? 'Update Formula' : 'Save Formula' }}
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
