<script setup lang="ts">
import { ref, defineProps, defineEmits, onMounted, watch } from 'vue';
import type { Customer } from '../../services/customerService';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  customer: {
    type: Object as () => Customer | null,
    default: null
  },
  isEdit: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);

const formData = ref({
  name: '',
  status: true
});

// If editing a customer, populate the form with customer data
onMounted(() => {
  if (props.customer) {
    populateFormData();
  }
});

// Watch for changes in the customer prop
watch(() => props.customer, () => {
  if (props.customer) {
    populateFormData();
  }
}, { deep: true });

const populateFormData = () => {
  if (props.customer) {
    formData.value = {
      name: props.customer.name || '',
      status: props.customer.status === 'active'
    };
  }
};

const resetForm = () => {
  formData.value = {
    name: '',
    status: true
  };
};

const closeForm = () => {
  if (!props.isEdit) {
    resetForm();
  }
  emit('close');
};

const saveCustomer = () => {
  // Validate form
  if (!formData.value.name.trim()) {
    alert('Customer name is required');
    return;
  }

  emit('save', { ...formData.value });
  closeForm();
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <div class="flex justify-between items-center mb-4">
          <h2 class="text-xl font-semibold text-gray-900">{{ isEdit ? 'Edit Customer' : 'Add New Customer' }}</h2>
          <button @click="closeForm" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <form @submit.prevent="saveCustomer" class="space-y-4">
          <div>
            <label for="name" class="block text-sm font-medium text-gray-700">Customer Name</label>
            <input
              type="text"
              id="name"
              v-model="formData.name"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              required
            >
          </div>

          <div>
            <label for="status" class="block text-sm font-medium text-gray-700">Status</label>
            <select
              id="status"
              v-model="formData.status"
              class="mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
            >
              <option :value=true>Active</option>
              <option :value=false>Inactive</option>
            </select>
          </div>

          <div class="flex justify-end space-x-3 pt-4">
            <button
              type="button"
              @click="closeForm"
              class="px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              Cancel
            </button>
            <button
              type="submit"
              class="px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
            >
              {{ isEdit ? 'Update' : 'Save' }} Customer
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
</template>
