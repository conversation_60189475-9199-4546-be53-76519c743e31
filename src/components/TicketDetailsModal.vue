<script setup lang="ts">
import { defineProps, defineEmits, computed } from 'vue';

const props = defineProps({
  show: {
    type: Boolean,
    required: true
  },
  ticket: {
    type: Object,
    default: null
  },
  projects: {
    type: Array,
    default: () => []
  },
  teamMembers: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['close']);

const statusColor = computed(() => {
  const colors: Record<string, string> = {
    'open': 'bg-blue-100 text-blue-800',
    'close': 'bg-gray-100 text-gray-800'
  };
  return colors[props.ticket?.statustxt || ''] || 'bg-gray-100 text-gray-800';
});

const formatDate = (dateString: any) => {
  if (!dateString) return 'Not set';
  return new Date(dateString).toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

const formatDateTime = (dateString: any) => {
  if (!dateString) return 'Not set';
  return new Date(dateString).toLocaleString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const closeModal = () => {
  emit('close');
};
</script>

<template>
  <div v-if="show" class="fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50">
    <div class="bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
      <div class="p-6">
        <!-- Header -->
        <div class="flex justify-between items-start mb-6">
          <div>
            <h2 class="text-xl font-bold text-gray-900 mb-2">{{ `${ticket?.ticketno} - ${ticket?.title}` }}</h2>
            <div class="flex items-center space-x-3">
              <span :class="statusColor" class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize">
                {{ ticket?.statustxt || 'Unknown' }}
              </span>
              <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize"
                :class="{
                    'bg-green-100 text-green-800': ticket.level === '1',
                    'bg-yellow-100 text-yellow-800': ticket.level === '2',
                    'bg-red-100 text-red-800': ticket.level === '3'
                }">
                Level {{ ticket?.level || '?' }}
              </span>
            </div>
          </div>
          <button @click="closeModal" class="text-gray-400 hover:text-gray-500">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
            </svg>
          </button>
        </div>

        <!-- Ticket Details -->
        <div class="space-y-6">
          <!-- Description -->
          <div>
            <h3 class="text-lg font-medium text-gray-900 mb-3">Description</h3>
            <div class="bg-gray-50 rounded-lg p-4">
              <p class="text-gray-700 whitespace-pre-wrap">{{ ticket?.description || 'No description provided' }}</p>
            </div>
          </div>

          <!-- Project and Assignment Info -->
          <div class="w-full">
            <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Assigned To</h4>
            <div class="text-gray-700 text-lg" v-for="assignee in ticket.assignee" :key="assignee.id" v-if="ticket && ticket.assignee.length > 0">
              <UserName :userId="assignee.id" />
            </div>
            <div class="text-gray-700 text-lg" v-else>
              No users assigned
            </div>          
          </div>
          <!-- Dates -->
          <div class="w-full">
            <div>
              <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Created By</h4>
              <p class="text-lg text-gray-900">{{ ticket?.user || 'Unknown' }}</p>
            </div>
          </div>

          <!-- Timestamps -->
          <div class="border-t pt-6">
            <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div v-if="ticket?.statustxt === 'close'">
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Closed Date</h4>
                <p class="text-sm text-gray-600">{{ formatDate(ticket?.closingdate) }}</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Created At</h4>
                <p class="text-sm text-gray-600">{{ formatDateTime(ticket?.createdAt) }}</p>
              </div>
              <div>
                <h4 class="text-sm font-medium text-gray-500 uppercase tracking-wide mb-2">Last Updated</h4>
                <p class="text-sm text-gray-600">{{ formatDateTime(ticket?.updatedAt) }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Footer -->
        <div class="flex justify-end pt-6 border-t mt-6">
          <button
            @click="closeModal"
            class="px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            Close
          </button>
        </div>
      </div>
    </div>
  </div>
</template>