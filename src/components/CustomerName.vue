<script setup lang="ts">
import { ref, onMounted, defineProps, watch } from 'vue';
import { useCustomerStore } from '../stores/customerStore';

const props = defineProps({
  customerId: {
    type: String,
    default: null
  }
});

const customerStore = useCustomerStore();
const customerName = ref('No Customer');
const isLoading = ref(false);

// Function to load customer data
const loadCustomer = async () => {
  if (!props.customerId) {
    customerName.value = 'No Customer';
    return;
  }

  isLoading.value = true;
  try {
    // First, ensure we have the customer data by fetching it if needed
    const customer = await customerStore.ensureCustomer(props.customerId);

    if (customer && customer.name) {
      customerName.value = customer.name;
    } else {
      customerName.value = `Customer #${props.customerId}`;
    }
  } catch (error) {
    console.error(`Error loading customer with ID ${props.customerId}:`, error);
    customerName.value = `Customer #${props.customerId}`;
  } finally {
    isLoading.value = false;
  }
};

// Load customer data when component is mounted
onMounted(loadCustomer);

// Watch for changes in customerId
watch(() => props.customerId, loadCustomer);
</script>

<template>
  <span v-if="isLoading" class="inline-flex items-center">
    <svg class="animate-spin h-4 w-4 mr-1 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
      <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
      <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
    </svg>
    Loading...
  </span>
  <span v-else>{{ customerName }}</span>
</template>
