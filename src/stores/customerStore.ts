import { defineStore } from 'pinia';
import { CustomerService } from '../services';

// Define the Customer interface directly in this file
interface Customer {
  id?: number;
  _id?: string; // MongoDB ID format
  name: string;
  status?: string;
  // Add other customer properties as needed
}

interface CustomerState {
  customers: Customer[];
  selectedCustomer: Customer | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useCustomerStore = defineStore('customer', {
  state: (): CustomerState => ({
    customers: [],
    selectedCustomer: null,
    loading: false,
    error: null,
    total: 0,
  }),

  getters: {
    getCustomerById: (state) => (id: number) => {
      return state.customers.find(customer => customer.id === id);
    },
  },

  actions: {
    async fetchCustomers(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await CustomerService.getAll(params);
        // Handle response format {data: actual_data, total: number}
        this.customers = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch customers';
        console.error('Error fetching customers:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchCustomerById(id: string | number) {
      this.loading = true;
      this.error = null;

      try {
        const response = await CustomerService.getById(id.toString());

        const customer = response.data || response || null;
        this.selectedCustomer = customer;
        return customer;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch customer';
        console.error(`Error fetching customer with ID ${id}:`, error);
        return null;
      } finally {
        this.loading = false;
      }
    },

    async ensureCustomer(id: string) {
      // Check if the customer already exists in the array
      const existingCustomer = this.customers.find(c => c._id === id);
      if (existingCustomer) {
        return existingCustomer;
      }

      // If not, fetch it and add it to the array
      try {
        const customer = await this.fetchCustomerById(id);

        if (customer && !this.customers.some(c => c._id === id)) {
          this.customers.push(customer);
        }
        return customer;
      } catch (error) {
        console.error(`Error ensuring customer with ID ${id}:`, error);
        return null;
      }
    },

    async createCustomer(customer: Customer) {
      this.loading = true;
      this.error = null;

      try {
        const response = await CustomerService.create(customer);
        // Handle response format {data: actual_data}
        const newCustomer = response.data || customer;
        this.customers.push(newCustomer);
        return newCustomer;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create customer';
        console.error('Error creating customer:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateCustomer(id: number | string, customer: Customer) {
      this.loading = true;
      this.error = null;

      try {
        const response = await CustomerService.update(Number(id), customer);
        // Handle response format {data: actual_data}
        const updatedCustomer = response.data || customer;
        const index = this.customers.findIndex(c => c.id === id);
        if (index !== -1) {
          this.customers[index] = updatedCustomer;
        }
        return updatedCustomer;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update customer';
        console.error(`Error updating customer with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteCustomer(id: number) {
      this.loading = true;
      this.error = null;

      try {
        await CustomerService.delete(id);
        this.customers = this.customers.filter(c => c.id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete customer';
        console.error(`Error deleting customer with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});

