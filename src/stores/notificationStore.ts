import { defineStore } from 'pinia';
import { NotificationService, type Notification } from '../services/notificationService';

interface NotificationState {
  notifications: Notification[];
  selectedNotification: Notification | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useNotificationStore = defineStore('notification', {
  state: (): NotificationState => ({
    notifications: [],
    selectedNotification: null,
    loading: false,
    error: null,
    total: 0,
  }),

  getters: {
    getNotificationById: (state) => (id: number | string) => {
      return state.notifications.find(notification => notification.id === id || notification._id === id);
    },
  },

  actions: {
    async fetchNotifications(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await NotificationService.getAll(params);
        // Handle response format {data: actual_data, total: number}
        this.notifications = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch notifications';
        console.error('Error fetching notifications:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchNotificationById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        this.selectedNotification = await NotificationService.getById(id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch notification';
        console.error(`Error fetching notification with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async createNotification(notification: Notification) {
      this.loading = true;
      this.error = null;

      try {
        const newNotification = await NotificationService.create(notification);
        // Don't push to notifications array as we'll refresh the list with pagination
        return newNotification;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create notification';
        console.error('Error creating notification:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateNotification(id: string, notification: Notification) {
      this.loading = true;
      this.error = null;

      try {
        const updatedNotification = await NotificationService.update(id, notification);
        // Find the notification by either id or _id
        const index = this.notifications.findIndex(p => p.id === id || p._id === id);
        if (index !== -1) {
          this.notifications[index] = updatedNotification;
        }
        return updatedNotification;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update notification';
        console.error(`Error updating notification with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteNotification(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await NotificationService.delete(id);
        // Filter out the deleted notification by either id or _id
        this.notifications = this.notifications.filter(p => p.id !== id && p._id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete notification';
        console.error(`Error deleting notification with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async fetchMyNotifications(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await NotificationService.getMyNotifications(params);

        this.notifications = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch notifications';
        console.error('Error fetching notifications:', error);
      } finally {
        this.loading = false;
      }
    },

    async markAsRead(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await NotificationService.update(id, { isRead: true });
        const index = this.notifications.findIndex(p => p.id === id || p._id === id);
        if (index !== -1) {
          this.notifications[index].isRead = true;
        }
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to mark notification as read';
        console.error(`Error marking notification with ID ${id} as read:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
