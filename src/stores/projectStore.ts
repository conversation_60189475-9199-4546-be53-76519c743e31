import { defineS<PERSON> } from 'pinia';
import { ProjectService, type Project } from '../services/projectService';

interface ProjectState {
  projects: Project[];
  selectedProject: Project | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useProjectStore = defineStore('project', {
  state: (): ProjectState => ({
    projects: [],
    selectedProject: null,
    loading: false,
    error: null,
    total: 0,
  }),

  getters: {
    getProjectById: (state) => (id: number | string) => {
      return state.projects.find(project => project.id === id || project._id === id);
    },
  },

  actions: {
    async fetchProjects(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await ProjectService.getAll(params);
        this.projects = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch projects';
        console.error('Error fetching projects:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchProjectById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const project = await ProjectService.getById(id);
        this.selectedProject = project;
        return project;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch project';
        console.error(`Error fetching project with ID ${id}:`, error);
        return null;
      } finally {
        this.loading = false;
      }
    },

    async ensureProject(id: string) {
      const existingProject = this.projects.find(c => c._id === id);
      if (existingProject) {
        return existingProject;
      }

      try {
        const project = await this.fetchProjectById(id);

        if (project && !this.projects.some(c => c._id === id)) {
          this.projects.push(project);
        }
        return project;
      } catch (error) {
        console.error(`Error ensuring project with ID ${id}:`, error);
        return null;
      }
    },

    async createProject(project: Project) {
      this.loading = true;
      this.error = null;

      try {
        const newProject = await ProjectService.create(project);
        // Don't push to projects array as we'll refresh the list with pagination
        return newProject;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create project';
        console.error('Error creating project:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateProject(id: string, project: Project) {
      this.loading = true;
      this.error = null;

      try {
        const updatedProject = await ProjectService.update(id, project);
        // Find the project by either id or _id
        const index = this.projects.findIndex(p => p.id === id || p._id === id);
        if (index !== -1) {
          this.projects[index] = updatedProject;
        }
        return updatedProject;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update project';
        console.error(`Error updating project with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteProject(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await ProjectService.delete(id);
        // Filter out the deleted project by either id or _id
        this.projects = this.projects.filter(p => p.id !== id && p._id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete project';
        console.error(`Error deleting project with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
