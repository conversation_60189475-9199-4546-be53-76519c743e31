import { defineStore } from 'pinia';
import { DepartmentTaskService, type DepartmentTask } from '../services/departmentTaskService';

interface DepartmentTaskState {
  departmentTasks: DepartmentTask[];
  selectedDepartmentTask: DepartmentTask | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useDepartmentTaskStore = defineStore('departmentTask', {
  state: (): DepartmentTaskState => ({
    departmentTasks: [],
    selectedDepartmentTask: null,
    loading: false,
    error: null,
    total: 0
  }),

  getters: {
    getDepartmentTaskById: (state) => (id: string) => {
      return state.departmentTasks.find(departmentTask => departmentTask.id === id);
    },
  },

  actions: {
    async fetchDepartmentTasks(params?: { $skip?: number; $limit?: number; $keywords?: string; department?: string; project?: string }) {
      this.loading = true;
      this.error = null;
    
      try {
        const response = await DepartmentTaskService.getAll(params);
        this.departmentTasks = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch departmentTasks';
        console.error('Error fetching departmentTasks:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchDepartmentTaskById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        this.selectedDepartmentTask = await DepartmentTaskService.getById(id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch departmentTask';
        console.error(`Error fetching departmentTask with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async createDepartmentTask(departmentTask: DepartmentTask) {
      this.loading = true;
      this.error = null;

      try {
        const newDepartment = await DepartmentTaskService.create(departmentTask);
        this.departmentTasks.push(newDepartment);
        return newDepartment;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create departmentTask';
        console.error('Error creating departmentTask:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateDepartmentTask(id: string, departmentTask: DepartmentTask) {
      this.loading = true;
      this.error = null;

      try {
        const updatedDepartment = await DepartmentTaskService.update(id, departmentTask);
        const index = this.departmentTasks.findIndex(d => d.id === id);
        if (index !== -1) {
          this.departmentTasks[index] = updatedDepartment;
        }
        return updatedDepartment;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update departmentTask';
        console.error(`Error updating departmentTask with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteDepartmentTask(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await DepartmentTaskService.delete(id);
        this.departmentTasks = this.departmentTasks.filter(d => d.id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete departmentTask';
        console.error(`Error deleting departmentTask with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
