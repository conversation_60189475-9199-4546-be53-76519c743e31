import { defineStore } from 'pinia';
import { TicketService, type Ticket } from '../services';

interface TicketState {
  tickets: Ticket[];
  selectedTicket: Ticket | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useTicketStore = defineStore('ticket', {
  state: (): TicketState => ({
    tickets: [],
    selectedTicket: null,
    loading: false,
    error: null,
    total: 0,
  }),

  getters: {
    getTicketById: (state) => (id: number) => {
      return state.tickets.find((ticket: Ticket) => ticket.id === id);
    },
  },

  actions: {
    async fetchTickets(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await TicketService.getAll(params);
        this.tickets = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch tickets';
        console.error('Error fetching tickets:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchTicketById(id: number) {
      this.loading = true;
      this.error = null;

      try {
        this.selectedTicket = await TicketService.getById(id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch ticket';
        console.error(`Error fetching ticket with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async createTicket(ticket: Ticket) {
      this.loading = true;
      this.error = null;

      try {
        const newTicket = await TicketService.create(ticket);
        this.tickets.push(newTicket);
        return newTicket;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create ticket';
        console.error('Error creating ticket:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateTicket(id: number, ticket: Ticket) {
      this.loading = true;
      this.error = null;

      try {
        const updatedTicket = await TicketService.update(id, ticket);
        const index = this.tickets.findIndex((t: Ticket) => t.id === id);
        if (index !== -1) {
          this.tickets[index] = updatedTicket;
        }
        return updatedTicket;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update ticket';
        console.error(`Error updating ticket with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteTicket(id: number) {
      this.loading = true;
      this.error = null;

      try {
        await TicketService.delete(id);
        this.tickets = this.tickets.filter((t: Ticket) => t.id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete ticket';
        console.error(`Error deleting ticket with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
