import { defineStore } from 'pinia';
import { DepartmentService, type Department } from '../services';

interface DepartmentState {
  departments: Department[];
  selectedDepartment: Department | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useDepartmentStore = defineStore('department', {
  state: (): DepartmentState => ({
    departments: [],
    selectedDepartment: null,
    loading: false,
    error: null,
    total: 0
  }),

  getters: {
    getDepartmentById: (state) => (id: string) => {
      return state.departments.find(department => department.id === id);
    },
  },

  actions: {
    async fetchDepartments(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await DepartmentService.getAll(params);
        this.departments = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch departments';
        console.error('Error fetching departments:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchDepartmentById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        this.selectedDepartment = await DepartmentService.getById(id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch department';
        console.error(`Error fetching department with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async createDepartment(department: Department) {
      this.loading = true;
      this.error = null;

      try {
        const newDepartment = await DepartmentService.create(department);
        this.departments.push(newDepartment);
        return newDepartment;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create department';
        console.error('Error creating department:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateDepartment(id: string, department: Department) {
      this.loading = true;
      this.error = null;

      try {
        const updatedDepartment = await DepartmentService.update(id, department);
        const index = this.departments.findIndex(d => d.id === id);
        if (index !== -1) {
          this.departments[index] = updatedDepartment;
        }
        return updatedDepartment;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update department';
        console.error(`Error updating department with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteDepartment(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await DepartmentService.delete(id);
        this.departments = this.departments.filter(d => d.id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete department';
        console.error(`Error deleting department with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
