import { defineStore } from 'pinia';
import { UserService, type User } from '../services';

interface UserState {
  users: User[];
  selectedUser: User | null;
  loading: boolean;
  error: string | null;
  total: number;
}

export const useUserStore = defineStore('user', {
  state: (): UserState => ({
    users: [],
    selectedUser: null,
    loading: false,
    error: null,
    total: 0,
  }),

  getters: {
    getUserById: (state) => (id: number | string) => {
      return state.users.find(user => user.id === id || user._id === id);
    },
  },

  actions: {
    async fetchUsers(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string }) {
      this.loading = true;
      this.error = null;

      try {
        const response = await UserService.getAll(params);
        this.users = response.data || [];
        this.total = response.total || 0;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch users';
        console.error('Error fetching users:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchUserById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        const response = await UserService.getById(id);
        this.selectedUser = response || null;
        return response;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch user';
        console.error(`Error fetching user with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async ensureUser(id: string) {
      // Check if the user already exists in the array
      const existingUser = this.users.find(c => c._id === id);
      if (existingUser) {
        return existingUser;
      }
      
      // If not, fetch it and add it to the array
      try {
        const user = await this.fetchUserById(id);
        if (user && !this.users.some(c => c._id === id)) {
          this.users.push(user);
        }
        return user;
      } catch (error) {
        console.error(`Error ensuring user with ID ${id}:`, error);
        return null;
      }
    },

    async createUser(user: User) {
      this.loading = true;
      this.error = null;

      try {
        const newUser = await UserService.create(user);
        this.users.push(newUser);
        return newUser;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create user';
        console.error('Error creating user:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateUser(id: number, user: User) {
      this.loading = true;
      this.error = null;

      try {
        const updatedUser = await UserService.update(id, user);
        const index = this.users.findIndex(u => u.id === id || u._id === String(id));
        if (index !== -1) {
          this.users[index] = updatedUser;
        }
        return updatedUser;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update user';
        console.error(`Error updating user with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteUser(id: number) {
      this.loading = true;
      this.error = null;

      try {
        await UserService.delete(id);
        this.users = this.users.filter(u => u.id !== id && u._id !== String(id));
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete user';
        console.error(`Error deleting user with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
