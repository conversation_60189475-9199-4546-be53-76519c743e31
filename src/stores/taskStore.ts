import { define<PERSON>tore } from 'pinia';
import { TaskService } from '../services';

// Define the Task interface directly in this file
interface Task {
  id?: string;
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  project?: string;
  assignedTo?: string;
  dueDate?: string;
  estimatedHours?: number | string;
  // Add other task properties as needed
}

interface TaskState {
  tasks: Task[];
  selectedTask: Task | null;
  loading: boolean;
  error: string | null;
}

export const useTaskStore = defineStore('task', {
  state: (): TaskState => ({
    tasks: [],
    selectedTask: null,
    loading: false,
    error: null,
  }),

  getters: {
    getTaskById: (state) => (id: string) => {
      return state.tasks.find(task => task.id === id);
    },

    todoTasks: (state) => {
      return state.tasks.filter(task => task.status === 'todo');
    },

    inProgressTasks: (state) => {
      return state.tasks.filter(task => task.status === 'in-progress');
    },

    completedTasks: (state) => {
      return state.tasks.filter(task => task.status === 'completed');
    },
  },

  actions: {
    async fetchTasks() {
      this.loading = true;
      this.error = null;

      try {
        this.tasks = await TaskService.getAll();
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch tasks';
        console.error('Error fetching tasks:', error);
      } finally {
        this.loading = false;
      }
    },

    async fetchTaskById(id: string) {
      this.loading = true;
      this.error = null;

      try {
        this.selectedTask = await TaskService.getById(id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to fetch task';
        console.error(`Error fetching task with ID ${id}:`, error);
      } finally {
        this.loading = false;
      }
    },

    async createTask(task: Task) {
      this.loading = true;
      this.error = null;

      try {
        const newTask = await TaskService.create(task);
        this.tasks.push(newTask);
        return newTask;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to create task';
        console.error('Error creating task:', error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async updateTask(id: string, task: Task) {
      this.loading = true;
      this.error = null;

      try {
        const updatedTask = await TaskService.update(id, task);
        const index = this.tasks.findIndex(t => t.id === id);
        if (index !== -1) {
          this.tasks[index] = updatedTask;
        }
        return updatedTask;
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to update task';
        console.error(`Error updating task with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },

    async deleteTask(id: string) {
      this.loading = true;
      this.error = null;

      try {
        await TaskService.delete(id);
        this.tasks = this.tasks.filter(t => t.id !== id);
      } catch (error: any) {
        this.error = error.response?.data?.message || 'Failed to delete task';
        console.error(`Error deleting task with ID ${id}:`, error);
        throw error;
      } finally {
        this.loading = false;
      }
    },
  },
});
