<script setup lang="ts">
import { ref, onMounted, computed } from 'vue';
import router from '@/router';
import { useProjectStore } from '../stores/projectStore';
import { useUserStore } from '../stores/userStore';
import { useCustomerStore } from '../stores/customerStore';
import { useNotificationStore } from '../stores/notificationStore';
import { useTicketStore } from '../stores/ticketStore';
import UserName from '../components/UserName.vue';

const limit = ref(1);
const projectStore = useProjectStore();
const projectTotal = computed(() => projectStore.total);
const isLoadingProject = computed(() => projectStore.loading);
const userStore = useUserStore();
const userTotal = computed(() => userStore.total);
const isLoadingUser = computed(() => userStore.loading);
const customerStore = useCustomerStore();
const customerTotal = computed(() => customerStore.total);
const isLoadingCustomer = computed(() => customerStore.loading);
const notificationStore = useNotificationStore();
const mynotification = computed(() => notificationStore.notifications);
const tickets = computed(() => ticketStore.tickets);
const ticketStore = useTicketStore();

const gotoPage = (page: any) => {
  if (page === 'projects') {
    router.push('/projects')
  }
  if (page === 'users') {
    router.push('/users')
  }
  if (page === 'tickets') {
    router.push('/tickets')
  }
  if (page === 'task') {
    router.push('/tasks')
  }
  if (page === 'messages') {
    router.push('/messages')
  }
}

const fetchProjects = async () => {
  await projectStore.fetchProjects({
    $limit: limit.value,
  });
};

const fetchUsers = async () => {
  await userStore.fetchUsers({
    $limit: limit.value,
  });
};

const fetchCustomers = async () => {
  await customerStore.fetchCustomers({
    $limit: limit.value,
  });
};

const fetchNotifications = async () => {
  await notificationStore.fetchMyNotifications({
    $limit: 5,
  });
};

const fetchTickets = async () => {
  await ticketStore.fetchTickets({
    $limit: 3,
  });
};

const formatDateTime = (dateString: any) => {
  if (!dateString) return 'Not set';
  return new Date(dateString).toLocaleString('en-GB', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const markAsRead = async (id: any) => {
  await notificationStore.markAsRead(id);
  await fetchNotifications();
};

onMounted(async () => {
  fetchProjects();
  fetchUsers();
  fetchCustomers();
  fetchNotifications();
  fetchTickets();
});
</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Dashboard</h1>

    <!-- Stats cards -->
    <div class="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8">
      <div class="bg-white rounded-lg shadow p-4 md:p-6">
        <h2 class="text-sm md:text-lg font-semibold mb-2">🙍🏻‍♂️ Total Users</h2>
        <p class="text-2xl md:text-3xl font-bold">{{ isLoadingUser ? 'Loading...' : userTotal }}</p>
      </div>
      <div class="bg-white rounded-lg shadow p-4 md:p-6">
        <h2 class="text-sm md:text-lg font-semibold mb-2">🤝 Total Customer</h2>
        <p class="text-2xl md:text-3xl font-bold">{{ isLoadingCustomer ? 'Loading...' : customerTotal }}</p>
      </div>
      <div class="bg-white rounded-lg shadow p-4 md:p-6">
        <h2 class="text-sm md:text-lg font-semibold mb-2">🚀 Total Projects</h2>
        <p class="text-2xl md:text-3xl font-bold">{{ isLoadingProject ? 'Loading...' : projectTotal }}</p>
      </div>
    </div>

    <!-- Recent activity section -->
    <div class="bg-white rounded-lg shadow p-4 md:p-6 mb-8">
      <!-- Header -->
      <div class="flex justify-between items-center mb-4">
        <h2 class="text-xl font-semibold text-gray-800">Notifications 🔔</h2>
      </div>
      
      <!-- Notification List -->
      <ul class="divide-y divide-gray-200">
        <!-- Notification 1: Unread Message -->
        <li v-for="mynotification in mynotification" class="py-4 flex transition-colors rounded-md px-2" 
        :class="!mynotification?.isRead ? 'bg-blue-50' : ''">
          <!-- Icon -->
          <div class="mr-4 flex-shrink-0">
            <div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor">
                <path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z" />
                <path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z" />
              </svg>
            </div>
          </div>
          
          <!-- Content -->
          <div class="flex-1 min-w-0">
            <div class="flex justify-between">
              <p class="text-sm font-semibold text-gray-900">
                New message from <UserName :id="mynotification?.sender" />
              </p>
              <span class="text-xs text-gray-500">{{ formatDateTime(mynotification?.createdAt) }}</span>
            </div>
            <p class="text-sm text-gray-500 mt-1">{{ mynotification?.message }}</p>
            
            <div class="mt-2 flex space-x-2">
              <button v-if="!mynotification?.isRead" @click="markAsRead(mynotification?._id)" class="px-3 py-1 text-xs font-medium rounded-md bg-gray-200 text-gray-600 hover:bg-gray-300">
                Mark as read ✅
              </button>
            </div>
          </div>
          
          <!-- Unread indicator -->
          <div v-if="!mynotification?.isRead" class="ml-3 flex-shrink-0">
            <div class="h-2 w-2 rounded-full bg-blue-600"></div>
          </div>
        </li>
      </ul>
      
      <!-- Footer -->
      <div class="mt-4 flex justify-center">
        <button @click="gotoPage('messages')" class="text-sm text-blue-600 hover:text-blue-800 font-medium">
          View all notifications
        </button>
      </div>
    </div>

    <!-- Quick actions -->
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6">
      <div class="bg-white rounded-lg shadow p-4 md:p-6">
        <h2 class="text-lg font-semibold mb-4">Quick Actions</h2>
        <div class="grid grid-cols-2 gap-3">
          <button @click="gotoPage('projects')" class="bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4" />
            </svg>
            <span>New Project</span>
          </button>
          <button @click="gotoPage('users')" class="bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" class="h-6 w-6 mb-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z" />
            </svg>
            <span>Add User</span>
          </button>
          <button @click="gotoPage('tickets')" class="bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-lg flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 mb-1">
              <path stroke-linecap="round" stroke-linejoin="round" d="M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z" />
            </svg>
            <span>Tickets</span>
          </button>
          <button @click="gotoPage('task')" class="bg-yellow-500 hover:bg-yellow-600 text-white p-3 rounded-lg flex flex-col items-center justify-center">
            <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke-width="1.5" stroke="currentColor" class="h-6 w-6 mb-1">
              <path stroke-linecap="round" stroke-linejoin="round" d="m4.5 12.75 6 6 9-13.5" />
            </svg>
            <span>Tasks</span>
          </button>
        </div>
      </div>

      <div class="bg-white rounded-lg shadow p-4 md:p-6">
        <h2 class="text-lg font-semibold mb-4">Recent Tickets</h2>
        <ul class="space-y-3">
          <li v-for="ticket in tickets" :key="ticket.id" class="flex justify-between items-center p-3 bg-gray-50 shadow-sm rounded-lg">
            <div>
              <p class="font-medium">{{ ticket.title }}</p>
              <p class="text-sm text-gray-500">{{ ticket.createdAt ? new Date(ticket.createdAt).toLocaleString() : 'No date' }}</p>
            </div>
            <span v-if="ticket.statustxt === 'open'" class="bg-red-100 text-red-600 text-xs font-medium px-2.5 py-0.5 rounded-full">Urgent</span>
            <span v-else class="bg-green-100 text-green-600 text-xs font-medium px-2.5 py-0.5 rounded-full">Closed</span>
          </li>
        </ul>
      </div>
    </div>
  </div>
</template>