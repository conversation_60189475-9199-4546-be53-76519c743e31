<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue';
import { useUserStore } from '../stores/userStore';
import UserForm from '../components/forms/UserForm.vue';

// Initialize stores
const userStore = useUserStore();

// State for user form
const showUserForm = ref(false);
const isEditMode = ref(false);
const selectedUser = ref<any>(null);

// State for search and filters
const searchQuery = ref('');

// Pagination
const limit = ref(10);
const skip = ref(0);
const total = computed(() => userStore.total);

onMounted(async () => {
  await fetchUsers();
});

const fetchUsers = async () => {
  await userStore.fetchUsers({
    $skip: skip.value,
    $limit: limit.value,
    $keywords: searchQuery.value || undefined
  });
};

watch(searchQuery, () => {
  skip.value = 0;
  fetchUsers();
});

const users = computed(() => userStore.users);

// User form functions
const openAddUserForm = () => {
  selectedUser.value = null;
  isEditMode.value = false;
  showUserForm.value = true;
};

const openEditUserForm = (user: any) => {
  selectedUser.value = { ...user };
  isEditMode.value = true;
  showUserForm.value = true;
};

const closeUserForm = () => {
  showUserForm.value = false;
  selectedUser.value = null;
};

const handleSaveUser = async (userData: any) => {
  try {
    if (isEditMode.value && selectedUser.value) {
      // Update existing user
      await userStore.updateUser(selectedUser.value.id || selectedUser.value._id, userData);
    } else {
      // Add new user
      await userStore.createUser(userData);
    }
    closeUserForm();
    await fetchUsers(); // Refresh the list
  } catch (error) {
    console.error('Error saving user:', error);
  }
};

// Function to delete a user
const deleteUser = async (userId: any) => {
  if (confirm('Are you sure you want to delete this user?')) {
    try {
      await userStore.deleteUser(userId);
      await fetchUsers(); // Refresh the list
    } catch (error) {
      console.error('Error deleting user:', error);
    }
  }
};

const roleColor = (role: any) => {
  switch(role) {
    case 'dev':
      return 'bg-red-100 text-red-800';
    case 'admin':
      return 'bg-yellow-100 text-yellow-800';
    case 'user':
      return 'bg-green-100 text-green-800';
    case 'sales':
      return 'bg-blue-100 text-blue-800';
    case 'pm':
      return 'bg-yellow-100 text-yellow-800';
    case 'fin':
      return 'bg-indigo-100 text-indigo-800';
    case 'tech':
      return 'bg-violet-100 text-violet-800';
    case 'pur':
      return 'bg-rose-100 text-rose-800';
    case 'overview':
      return 'bg-lime-100 text-lime-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}

// Pagination methods
const goToPage = (page: number | string) => {
  if (typeof page === 'number') {
    skip.value = (page - 1) * limit.value;
    fetchUsers();
  }
};

const nextPage = () => {
  if (skip.value + limit.value < total.value) {
    skip.value += limit.value;
    fetchUsers();
  }
};

const prevPage = () => {
  if (skip.value - limit.value >= 0) {
    skip.value -= limit.value;
    fetchUsers();
  }
};

// Computed properties for pagination
const currentPage = computed(() => Math.floor(skip.value / limit.value) + 1);
const totalPages = computed(() => Math.ceil(total.value / limit.value));
const pages = computed(() => {
  const pagesArray: (number | string)[] = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages if there are fewer than maxVisiblePages
    for (let i = 1; i <= totalPages.value; i++) {
      pagesArray.push(i);
    }
  } else {
    // Always show first page
    pagesArray.push(1);

    // Calculate start and end of visible pages
    let start = Math.max(2, currentPage.value - 1);
    let end = Math.min(totalPages.value - 1, start + maxVisiblePages - 3);

    // Adjust start if end is too close to totalPages
    start = Math.max(2, end - (maxVisiblePages - 3));

    // Add ellipsis if needed
    if (start > 2) {
      pagesArray.push('...');
    }

    // Add middle pages
    for (let i = start; i <= end; i++) {
      pagesArray.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages.value - 1) {
      pagesArray.push('...');
    }

    // Always show last page
    pagesArray.push(totalPages.value);
  }

  return pagesArray;
});
</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Users</h1>

    <!-- Search and filter section -->
    <div class="bg-white rounded-lg shadow p-4 md:p-6 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-grow">
          <label for="search" class="sr-only">Search users</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
            </div>
            <input 
              type="text" 
              id="search" 
              v-model="searchQuery"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" 
              placeholder="Search users..."
            >
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-4">
          <button 
            @click="openAddUserForm"
            class="whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            Add User
          </button>
        </div>
      </div>
    </div>

    <!-- Users table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                User
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Role
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="user in users" :key="user.id || user._id" class="hover:bg-gray-50">
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div class="flex-shrink-0 h-10 w-10">
                    <img v-if="user.avatar" class="h-10 w-10 rounded-full object-cover" :src="user.avatar" :alt="`${user.name}`">
                    <div v-else class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold">
                      {{ user.name.charAt(0) }}
                    </div>
                  </div>
                  <div class="ml-4">
                    <div class="text-sm font-medium text-gray-900">{{ user.name }}</div>
                    <div class="text-sm text-gray-500">{{ user.email }}</div>
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 flex items-center gap-1">
                <div class="text-sm capitalize rounded px-1 py-0.5" :class="roleColor(scope)" v-for="scope in (user.scopes || [user.role])">{{ scope }}</div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span 
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="user.status ? 'bg-green-100 text-green-800' : 'bg-gray-100 text-gray-800'"
                >
                  {{ user.status ? 'Active' : 'Inactive' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                <button 
                  @click="openEditUserForm(user)" 
                  class="text-blue-600 hover:text-blue-900 mr-3"
                >
                  Edit
                </button>
                <button 
                  @click="deleteUser(user.id || user._id)"
                  class="text-red-600 hover:text-red-900"
                >
                  Delete
                </button>
              </td>
            </tr>
            <tr v-if="users.length === 0">
              <td colspan="7" class="px-6 py-4 text-center text-sm text-gray-500">
                No users found matching your search criteria.
              </td>
            </tr>
          </tbody>
        </table>
      </div>
      
      <!-- Pagination (simplified for now) -->
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <!-- Mobile pagination controls -->
            <div class="flex w-full justify-between sm:hidden">
              <button
                @click="prevPage"
                :disabled="skip === 0"
                :class="[
                  'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                  skip === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                Previous
              </button>
              <span class="text-sm text-gray-700">
                Page {{ currentPage }} of {{ totalPages }}
              </span>
              <button
                @click="nextPage"
                :disabled="skip + limit >= total"
                :class="[
                  'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                  skip + limit >= total ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                Next
              </button>
            </div>

            <!-- Desktop pagination info and controls -->
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium">{{ users.length > 0 ? skip + 1 : 0 }}</span>
                  to
                  <span class="font-medium">{{ Math.min(skip + users.length, total) }}</span>
                  of
                  <span class="font-medium">{{ total }}</span>
                  users
                </p>
              </div>
              <div v-if="total > 0">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    @click="prevPage"
                    :disabled="skip === 0"
                    :class="[
                      'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                      skip === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">Previous</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>

                  <!-- Page numbers -->
                  <template v-for="(page, index) in pages" :key="index">
                    <!-- Ellipsis -->
                    <span v-if="page === '...'" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                      ...
                    </span>

                    <!-- Page number -->
                    <button
                      v-else
                      @click="goToPage(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                        currentPage === page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      {{ page }}
                    </button>
                  </template>

                  <button
                    @click="nextPage"
                    :disabled="skip + limit >= total"
                    :class="[
                      'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                      skip + limit >= total ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">Next</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
    </div>
  </div>
  
  <!-- User Form Component -->
  <UserForm 
    :show="showUserForm"
    :user="selectedUser"
    :isEdit="isEditMode"
    @close="closeUserForm"
    @save="handleSaveUser"
  />
</template>
