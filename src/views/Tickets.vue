<script setup lang="ts">
// Tickets component
import { ref, computed, onMounted } from 'vue';
import TicketForm from '@/components/forms/TicketForm.vue';
import TicketDetailsModal from '@/components/TicketDetailsModal.vue';
import CustomerName from '@/components/CustomerName.vue';
import UserName from '@/components/UserName.vue';
import { useTicketStore } from '@/stores/ticketStore';
import { useProjectStore } from '@/stores/projectStore';
import { useUserStore } from '@/stores/userStore';

// Initialize stores
const ticketStore = useTicketStore();
const projectStore = useProjectStore();
const userStore = useUserStore();

// State for ticket form
const showTicketForm = ref(false);
const isEditMode = ref(false);
const selectedTicket = ref<any>(null);

// State for ticket details modal
const showTicketDetails = ref(false);

// State for search and filters
const searchQuery = ref('');
const statustxtFilter = ref('');
const priorityFilter = ref('');

// Computed properties for loading and error states
const isLoading = computed(() => ticketStore.loading);
const error = computed(() => ticketStore.error);

// Fetch data on component mount
onMounted(async () => {
  await Promise.all([
    ticketStore.fetchTickets(),
    projectStore.fetchProjects(),
    userStore.fetchUsers()
  ]);
});

// Computed property for filtered tickets
const filteredTickets = computed(() => {
  return ticketStore.tickets.filter((ticket: any) => {
    // Search query filter
    const searchLower = searchQuery.value.toLowerCase();
    const matchesSearch = searchQuery.value === '' ||
                          ticket.title.toLowerCase().includes(searchLower) ||
                          (ticket.description && ticket.description.toLowerCase().includes(searchLower));

    // Status filter
    const matchesStatus = statustxtFilter.value === '' || ticket.statustxt === statustxtFilter.value;

    // Priority filter
    const matchesPriority = priorityFilter.value === '' || ticket.priority === priorityFilter.value;

    return matchesSearch && matchesStatus && matchesPriority;
  });
});

const formatDate = (dateString: string | undefined): string => {
  if (!dateString) return '';

  const date = new Date(dateString);
  return new Intl.DateTimeFormat('en-GB', {
    year: 'numeric',
    month: 'short',
    day: 'numeric'
  }).format(date);
};

// Ticket form functions
const openAddTicketForm = (): void => {
  selectedTicket.value = null;
  isEditMode.value = false;
  showTicketForm.value = true;
};

const openEditTicketForm = (ticket: any): void => {
  selectedTicket.value = { ...ticket };
  isEditMode.value = true;
  showTicketForm.value = true;
  showTicketDetails.value = false; // Close details modal if open
};

const closeTicketForm = (): void => {
  showTicketForm.value = false;
  if (isEditMode.value) {
    isEditMode.value = false;
    selectedTicket.value = null;
  }
};

const handleSaveTicket = async (ticketData: any): Promise<void> => {
  try {
    if (isEditMode.value && selectedTicket.value) {
      // Update existing ticket
      await ticketStore.updateTicket(selectedTicket.value.id, ticketData);
    } else {
      // Add new ticket
      await ticketStore.createTicket(ticketData);
    }

    // Close the form
    closeTicketForm();
  } catch (error) {
    console.error('Error saving ticket:', error);
  }
};

// Ticket details functions
const viewTicketDetails = (ticket: any): void => {
  selectedTicket.value = { ...ticket };
  showTicketDetails.value = true;
};

const closeTicketDetails = (): void => {
  showTicketDetails.value = false;
  selectedTicket.value = null;
};

// Delete ticket function
const deleteTicket = async (id: number | undefined): Promise<void> => {
  if (!id) return;

  if (confirm('Are you sure you want to delete this ticket?')) {
    try {
      await ticketStore.deleteTicket(id);
    } catch (error) {
      console.error('Error deleting ticket:', error);
    }
  }
};
</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Tickets</h1>

    <!-- Search and filter section -->
    <div class="bg-white rounded-lg shadow p-4 md:p-6 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-grow">
          <label for="search" class="sr-only">Search tickets</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              id="search"
              v-model="searchQuery"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search tickets...">
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-4">
          <select
            v-model="statustxtFilter"
            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Status</option>
            <option value="open">Open</option>
            <option value="in-progress">In Progress</option>
            <option value="resolved">Resolved</option>
            <option value="closed">Closed</option>
          </select>
          <select
            v-model="priorityFilter"
            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Priorities</option>
            <option value="low">Low</option>
            <option value="medium">Medium</option>
            <option value="high">High</option>
            <option value="urgent">Urgent</option>
          </select>
          <button
            @click="openAddTicketForm"
            class="whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            New Ticket
          </button>
        </div>
      </div>
    </div>

    <!-- Ticket Form Component -->
    <TicketForm
      :show="showTicketForm"
      :ticket="selectedTicket"
      :isEdit="isEditMode"
      @close="closeTicketForm"
      @save="handleSaveTicket"
    />

    <!-- Ticket Details Modal -->
    <TicketDetailsModal
      :show="showTicketDetails"
      :ticket="selectedTicket"
      @close="closeTicketDetails"
      @edit="openEditTicketForm"
    />

    <!-- Loading state -->
    <div v-if="isLoading" class="flex justify-center items-center py-12">
      <svg class="animate-spin h-8 w-8 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
        <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
        <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
      </svg>
      <span class="ml-3 text-lg text-gray-600">Loading tickets...</span>
    </div>

    <!-- Error state -->
    <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative my-6">
      <strong class="font-bold">Error!</strong>
      <span class="block sm:inline"> {{ error }}</span>
    </div>

    <!-- Empty state -->
    <div v-else-if="filteredTickets.length === 0" class="bg-white rounded-lg shadow p-6 text-center my-6">
      <svg xmlns="http://www.w3.org/2000/svg" class="h-12 w-12 text-gray-400 mx-auto mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
      </svg>
      <h3 class="text-lg font-medium text-gray-900">No tickets found</h3>
      <p class="mt-2 text-gray-500">Get started by creating a new ticket.</p>
      <button
        @click="openAddTicketForm"
        class="mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        New Ticket
      </button>
    </div>

    <!-- Tickets table -->
    <div v-else class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto">
        <table class="min-w-full divide-y divide-gray-200">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Ticket
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Customer
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Assigned To
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Level
              </th>
              <th scope="col" class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Date
              </th>
              <th scope="col" class="relative px-6 py-3">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <tr v-for="ticket in filteredTickets" :key="ticket.id" class="hover:bg-gray-50">
              <td class="px-6 py-4">
                <div class="flex flex-col">
                  <a
                    href="#"
                    @click.prevent="viewTicketDetails(ticket)"
                    class="text-sm font-medium text-blue-600 hover:text-blue-900 whitespace-nowrap"
                  >
                    {{ ticket.ticketno }}
                  </a>
                  <div class="text-xs text-gray-700 mt-1 whitespace-nowrap">
                    {{ ticket.title }}
                  </div>
                </div>
              </td>
              <td class="px-6 py-4 truncate text-sm text-gray-500 max-w-xs">
                <CustomerName :customerId="ticket.customer" />
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                <div v-for="assignee in ticket.assignee" :key="assignee.id" v-if="ticket && ticket.assignee.length > 0">
                  <UserName :userId="assignee.id" />
                </div>
                <div v-else>
                  No users assigned
                </div>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="{
                    'bg-blue-100 text-blue-800': ticket.statustxt === 'open',
                    'bg-gray-100 text-gray-800': ticket.statustxt === 'close'
                  }"
                >
                  {{ ticket.statustxt ? (ticket.statustxt === 'in-progress' ? 'In Progress' : ticket.statustxt.charAt(0).toUpperCase() + ticket.statustxt.slice(1)) : 'Unknown' }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <span
                  class="px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full"
                  :class="{
                    'bg-green-100 text-green-800': ticket.level === '1',
                    'bg-yellow-100 text-yellow-800': ticket.level === '2',
                    'bg-red-100 text-red-800': ticket.level === '3'
                  }"
                >
                  {{ ticket.level }}
                </span>
              </td>
              <td class="px-6 py-4 whitespace-nowrap text-right text-sm font-medium">
                {{ formatDate(ticket.createdAt) }}
              </td>
              <td class="px-6 py-4 whitespace-nowrap">
                <div class="flex justify-end space-x-2">
                  <button
                    @click="viewTicketDetails(ticket)"
                    class="text-blue-600 hover:text-blue-900"
                    title="View Details"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M10 12a2 2 0 100-4 2 2 0 000 4z" />
                      <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                  <button
                    @click="openEditTicketForm(ticket)"
                    class="text-indigo-600 hover:text-indigo-900"
                    title="Edit"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path d="M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z" />
                    </svg>
                  </button>
                  <button
                    @click="deleteTicket(ticket.id)"
                    class="text-red-600 hover:text-red-900"
                    title="Delete"
                  >
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                      <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Pagination (simplified for now) -->
      <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
        <div class="flex items-center justify-between">
          <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
            <div>
              <p class="text-sm text-gray-700">
                Showing <span class="font-medium">{{ filteredTickets.length }}</span> tickets
              </p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>
