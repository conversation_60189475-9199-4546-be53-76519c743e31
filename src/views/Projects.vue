<script setup lang="ts">
// Projects component
import { ref, onMounted, computed, watch } from 'vue';
import ProjectForm from '../components/forms/ProjectForm.vue';
import ProjectDetailsModal from '../components/ProjectDetailsModal.vue';
import CustomerName from '../components/CustomerName.vue';
import { useProjectStore } from '../stores/projectStore';
import { useCustomerStore } from '../stores/customerStore';
import type { Project } from '../services/projectService';
import Swal from 'sweetalert2';

// Initialize stores
const projectStore = useProjectStore();
const customerStore = useCustomerStore();

const showProjectForm = ref(false);
const showProjectDetails = ref(false);
const selectedProject = ref<Project | null>(null);
const isEditMode = ref(false);
const searchQuery = ref('');
const stageFilter = ref('');
const isLoading = computed(() => projectStore.loading);
const error = computed(() => projectStore.error);

// Pagination
const limit = ref(10);
const skip = ref(0);
const total = computed(() => projectStore.total);

// Fetch projects on component mount
onMounted(async () => {
  await fetchProjects();
});

// Function to fetch projects with pagination and filtering
const fetchProjects = async () => {
  await projectStore.fetchProjects({
    $skip: skip.value,
    $limit: limit.value,
    $keywords: searchQuery.value || undefined,
    stage: stageFilter.value || undefined
  });
};

// Computed property for customers
const customers = computed(() => {
  return customerStore.customers;
});

// Use projects directly from the store since filtering is now done on the server
const projects = computed(() => projectStore.projects);

// Watch for filter changes and reset pagination
watch([searchQuery, stageFilter], () => {
  skip.value = 0;
  fetchProjects();
});

const openProjectForm = async (): Promise<void> => {
  selectedProject.value = null;
  isEditMode.value = false;

  // Fetch a small batch of customers for the dropdown
  await customerStore.fetchCustomers({ $limit: 20, $skip: 0 });

  showProjectForm.value = true;
};

const openEditProjectForm = async (project: Project): Promise<void> => {
  selectedProject.value = project;
  isEditMode.value = true;

  // Fetch a small batch of customers for the dropdown
  await customerStore.fetchCustomers({ $limit: 20, $skip: 0 });

  // If this project has a customer, ensure we have that customer's data
  if (project.customer) {
    await customerStore.ensureCustomer(project.customer);
  }

  showProjectForm.value = true;
};

const closeProjectForm = (): void => {
  showProjectForm.value = false;
  selectedProject.value = null;
};

const handleSaveProject = async (projectData: Project): Promise<void> => {
  try {
    if (isEditMode.value && selectedProject.value) {
      // Update existing project - use _id if available, otherwise use id
      const projectId = selectedProject.value._id || selectedProject.value.id;
      if (projectId !== undefined) {
        await projectStore.updateProject(projectId, projectData);
      }
    } else {
      // Create new project
      await projectStore.createProject(projectData);
    }
    closeProjectForm();
    Swal.fire({
      title: 'Success!',
      text: 'Project saved successfully',
      icon: 'success',
      showConfirmButton: false,
      timer: 1000,
    });
    // Refresh the project list after adding/updating
    await fetchProjects();
  } catch (err) {
    console.error('Error saving project:', err);
    Swal.fire({
      title: 'Error!',
      text: 'Failed to save project',
      icon: 'error',
      showConfirmButton: false,
      timer: 1000,
    });
  }
};

const viewProjectDetails = (project: Project): void => {
  selectedProject.value = project;
  showProjectDetails.value = true;
};

const closeProjectDetails = (): void => {
  showProjectDetails.value = false;
  selectedProject.value = null;
};

const handleEditFromDetails = (project: Project): void => {
  closeProjectDetails();
  openEditProjectForm(project);
};

const deleteProject = async (id: string): Promise<void> => {
  if (confirm('Are you sure you want to delete this project?')) {
    try {
      const projectData: any = {
        status: false
      }
      await projectStore.updateProject(id, projectData);
      // Refresh the project list after deleting
      await fetchProjects();
    } catch (err) {
      console.error('Error deleting project:', err);
    }
  }
};

// Pagination methods
const goToPage = (page: number | string) => {
  if (typeof page === 'number') {
    skip.value = (page - 1) * limit.value;
    fetchProjects();
  }
};

const nextPage = () => {
  if (skip.value + limit.value < total.value) {
    skip.value += limit.value;
    fetchProjects();
  }
};

const prevPage = () => {
  if (skip.value - limit.value >= 0) {
    skip.value -= limit.value;
    fetchProjects();
  }
};

// Computed properties for pagination
const currentPage = computed(() => Math.floor(skip.value / limit.value) + 1);
const totalPages = computed(() => Math.ceil(total.value / limit.value));
const pages = computed(() => {
  const pagesArray: (number | string)[] = [];
  const maxVisiblePages = 5;

  if (totalPages.value <= maxVisiblePages) {
    // Show all pages if there are fewer than maxVisiblePages
    for (let i = 1; i <= totalPages.value; i++) {
      pagesArray.push(i);
    }
  } else {
    // Always show first page
    pagesArray.push(1);

    // Calculate start and end of visible pages
    let start = Math.max(2, currentPage.value - 1);
    let end = Math.min(totalPages.value - 1, start + maxVisiblePages - 3);

    // Adjust start if end is too close to totalPages
    start = Math.max(2, end - (maxVisiblePages - 3));

    // Add ellipsis if needed
    if (start > 2) {
      pagesArray.push('...');
    }

    // Add middle pages
    for (let i = start; i <= end; i++) {
      pagesArray.push(i);
    }

    // Add ellipsis if needed
    if (end < totalPages.value - 1) {
      pagesArray.push('...');
    }

    // Always show last page
    pagesArray.push(totalPages.value);
  }

  return pagesArray;
});

function getStageClass(stage?: string) {
  if (!stage) return 'bg-gray-100 text-gray-800';
  switch(stage.toLowerCase()) {
    case 'completed':
      return 'bg-green-100 text-green-800';
    case 'in progress':
      return 'bg-blue-100 text-blue-800';
    case 'on hold':
      return 'bg-yellow-100 text-yellow-800';
    default:
      return 'bg-gray-100 text-gray-800';
  }
}
</script>

<template>
  <div class="p-4 md:p-6 lg:p-8">
    <h1 class="text-xl md:text-2xl font-bold mb-4 md:mb-6">Projects</h1>

    <!-- Search and filter section -->
    <div class="bg-white rounded-lg shadow p-4 md:p-6 mb-6">
      <div class="flex flex-col md:flex-row gap-4">
        <div class="flex-grow">
          <label for="search" class="sr-only">Search projects</label>
          <div class="relative">
            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
              <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor">
                <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
              </svg>
            </div>
            <input
              type="text"
              id="search"
              v-model="searchQuery"
              class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
              placeholder="Search projects..."
            >
          </div>
        </div>
        <div class="flex flex-col sm:flex-row gap-4">
          <select
            v-model="stageFilter"
            class="block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"
          >
            <option value="">All Stage</option>
            <option value="planning">Planning</option>
            <option value="in progress">In Progress</option>
            <option value="completed">Completed</option>
          </select>
          <button
            @click="() => openProjectForm()"
            class="whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
          >
            <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
            </svg>
            New Project
          </button>
        </div>
      </div>
    </div>

    <!-- Projects table -->
    <div class="bg-white shadow rounded-lg overflow-hidden">
      <div class="overflow-x-auto w-full">
        <table class="min-w-full divide-y divide-gray-200 table-fixed md:table-auto">
          <thead class="bg-gray-50">
            <tr>
              <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5">
                Project Name
              </th>
              <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Customer
              </th>
              <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Stage
              </th>
              <th scope="col" class="px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5">
                Salesperson
              </th>
              <th scope="col" class="relative px-4 sm:px-6 py-3 w-1/5">
                <span class="sr-only">Actions</span>
              </th>
            </tr>
          </thead>
          <tbody class="bg-white divide-y divide-gray-200">
            <!-- Loading state -->
            <tr v-if="isLoading">
              <td colspan="4" class="px-4 sm:px-6 py-4 text-center">
                <div class="flex justify-center">
                  <svg class="animate-spin h-5 w-5 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                  </svg>
                  <span class="ml-2">Loading projects...</span>
                </div>
              </td>
            </tr>

            <!-- Error state -->
            <tr v-else-if="error">
              <td colspan="4" class="px-4 sm:px-6 py-4 text-center text-red-500">
                {{ error }}
              </td>
            </tr>

            <!-- Empty state -->
            <tr v-else-if="projects.length === 0">
              <td colspan="4" class="px-4 sm:px-6 py-4 text-center text-gray-500">
                No projects found.
              </td>
            </tr>

            <!-- Project rows -->
            <tr v-for="project in projects" :key="project.id" class="hover:bg-gray-50">
              <td class="px-4 sm:px-6 py-4">
                <div class="flex items-center">
                  <div class="overflow-hidden">
                    <div class="text-sm font-medium text-gray-900 truncate max-w-xs">{{ project.name || 'Unnamed Project' }}</div>
                    <div class="text-xs sm:text-sm text-gray-500 truncate max-w-xs">{{ project.description }}</div>
                  </div>
                </div>
              </td>
              <td class="px-4 sm:px-6 py-4">
                <div class="text-xs sm:text-sm text-gray-900 truncate max-w-xs">
                  <CustomerName :customerId="project.customer" />
                </div>
              </td>
              <td class="px-4 sm:px-6 py-4">
                <span
                  class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize whitespace-nowrap"
                  :class="getStageClass(project.stage)"
                >
                  {{ project.stage || 'No stage' }}
                </span>
              </td>
              <td class="px-4 sm:px-6 py-4">
                <span class="text-sm sm:text-sm text-gray-900 truncate max-w-xs">{{ project.salesperson }}</span>
              </td>
              <td class="px-4 sm:px-6 py-4 text-right text-xs sm:text-sm font-medium">
                <div class="flex flex-col sm:flex-row sm:justify-end space-y-1 sm:space-y-0 sm:space-x-2">
                  <button
                    @click="() => openEditProjectForm(project)"
                    class="text-blue-600 hover:text-blue-900"
                  >
                    Edit
                  </button>
                  <button
                    @click="viewProjectDetails(project)"
                    class="text-gray-600 hover:text-gray-900"
                  >
                    Details
                  </button>
                  <button
                    @click="deleteProject(String(project._id || project.id || 0))"
                    class="text-red-600 hover:text-red-900"
                  >
                    Delete
                  </button>
                </div>
              </td>
            </tr>
          </tbody>
        </table>
        <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
          <div class="flex flex-col sm:flex-row items-center justify-between gap-4">
            <!-- Mobile pagination controls -->
            <div class="flex w-full justify-between sm:hidden">
              <button
                @click="prevPage"
                :disabled="skip === 0"
                :class="[
                  'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                  skip === 0 ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                Previous
              </button>
              <span class="text-sm text-gray-700">
                Page {{ currentPage }} of {{ totalPages }}
              </span>
              <button
                @click="nextPage"
                :disabled="skip + limit >= total"
                :class="[
                  'relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white',
                  skip + limit >= total ? 'opacity-50 cursor-not-allowed' : 'hover:bg-gray-50'
                ]"
              >
                Next
              </button>
            </div>

            <!-- Desktop pagination info and controls -->
            <div class="hidden sm:flex-1 sm:flex sm:items-center sm:justify-between">
              <div>
                <p class="text-sm text-gray-700">
                  Showing
                  <span class="font-medium">{{ projects.length > 0 ? skip + 1 : 0 }}</span>
                  to
                  <span class="font-medium">{{ Math.min(skip + projects.length, total) }}</span>
                  of
                  <span class="font-medium">{{ total }}</span>
                  projects
                </p>
              </div>
              <div v-if="total > 0">
                <nav class="relative z-0 inline-flex rounded-md shadow-sm -space-x-px" aria-label="Pagination">
                  <button
                    @click="prevPage"
                    :disabled="skip === 0"
                    :class="[
                      'relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium',
                      skip === 0 ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">Previous</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>

                  <!-- Page numbers -->
                  <template v-for="(page, index) in pages" :key="index">
                    <!-- Ellipsis -->
                    <span v-if="page === '...'" class="relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700">
                      ...
                    </span>

                    <!-- Page number -->
                    <button
                      v-else
                      @click="goToPage(page)"
                      :class="[
                        'relative inline-flex items-center px-4 py-2 border text-sm font-medium',
                        currentPage === page
                          ? 'z-10 bg-blue-50 border-blue-500 text-blue-600'
                          : 'bg-white border-gray-300 text-gray-500 hover:bg-gray-50'
                      ]"
                    >
                      {{ page }}
                    </button>
                  </template>

                  <button
                    @click="nextPage"
                    :disabled="skip + limit >= total"
                    :class="[
                      'relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium',
                      skip + limit >= total ? 'text-gray-300 cursor-not-allowed' : 'text-gray-500 hover:bg-gray-50'
                    ]"
                  >
                    <span class="sr-only">Next</span>
                    <svg class="h-5 w-5" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor" aria-hidden="true">
                      <path fill-rule="evenodd" d="M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z" clip-rule="evenodd" />
                    </svg>
                  </button>
                </nav>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Project Form Component -->
    <ProjectForm
      :show="showProjectForm"
      :customers="customers"
      :project="selectedProject"
      :isEdit="isEditMode"
      @close="closeProjectForm"
      @save="handleSaveProject"
    />

    <!-- Project Details Modal -->
    <ProjectDetailsModal
      :show="showProjectDetails"
      :project="selectedProject"
      @close="closeProjectDetails"
      @edit="handleEditFromDetails"
    />
  </div>
</template>
