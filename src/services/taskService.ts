import { ApiService } from './api';

export interface Task {
  id?: string;
  title: string;
  description?: string;
  status?: string;
  priority?: string;
  project?: string;
  assignedTo?: string;
  dueDate?: string;
  estimatedHours?: number | string;
  // Add other task properties as needed
}

export class TaskService {
  private static readonly baseUrl = 'tasks';

  /**
   * Get all tasks
   */
  static async getAll(): Promise<Task[]> {
    return ApiService.get<Task[]>(this.baseUrl);
  }

  /**
   * Get task by ID
   */
  static async getById(id: string): Promise<Task> {
    return ApiService.get<Task>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new task
   */
  static async create(task: Task): Promise<Task> {
    return ApiService.post<Task>(this.baseUrl, task);
  }

  /**
   * Update an existing task
   */
  static async update(id: string, task: Task): Promise<Task> {
    return ApiService.patch<Task>(`${this.baseUrl}/${id}`, task);
  }

  /**
   * Delete a task
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default TaskService;
