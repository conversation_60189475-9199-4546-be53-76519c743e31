// Common types
export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export * from './api';
export * from './authService';
export { CustomerService, type Customer } from './customerService';
export { ProjectService, type Project } from './projectService';
export { TicketService, type Ticket } from './ticketService';
export { TaskService, type Task } from './taskService';
export { DepartmentService, type Department } from './departmentService';
export { DepartmentTaskService, type DepartmentTask, type TaskField } from './departmentTaskService';
export { UserService, type User } from './userService';
export { NotificationService, type Notification } from './notificationService';
export { FormulaService, type Formula, type FormulaVariable } from './formulaService';
