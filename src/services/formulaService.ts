import { ApiService } from './api';

export interface Formula {
  id?: string;
  name: string;
  description: string;
  category: string;
  expression: string;
  variables: FormulaVariable[];
  isActive: boolean;
  sampleInput: string;
  sampleOutput: string;
}

export interface FormulaVariable {
  name: string;
  description: string;
  type: string;
  defaultValue: string;
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class FormulaService {
  private static readonly baseUrl = '/formulas';

  /**
   * Get all formulas
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Formula[]>> {
    return ApiService.get<ApiResponse<Formula[]>>(this.baseUrl, { params });
  }

  /**
   * Get formula by ID
   */
  static async getById(id: string): Promise<Formula> {
    return ApiService.get<Formula>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new formula
   */
  static async create(formula: Formula): Promise<Formula> {
    return ApiService.post<Formula>(this.baseUrl, formula);
  }

  /**
   * Update an existing formula
   */
  static async update(id: string, formula: Formula): Promise<Formula> {
    return ApiService.patch<Formula>(`${this.baseUrl}/${id}`, formula);
  }

  /**
   * Delete a formula
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default FormulaService;
