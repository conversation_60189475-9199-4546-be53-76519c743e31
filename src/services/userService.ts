import { ApiService } from './api';

export interface User {
  id?: string;
  _id?: string;
  firstName: string;
  lastName: string;
  name: string; // Full name
  email: string;
  phone?: string;
  role?: string;
  department?: string;
  status?: string;
  avatar?: string;
  password?: string;
  lastLogin?: string;
  scopes?: string[];
  createdAt?: string;
  updatedAt?: string;
  // Add other user properties as needed
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class UserService {
  private static readonly baseUrl = '/users';

  /**
   * Get all users
   * @param params Optional parameters for pagination and filtering
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string }): Promise<ApiResponse<User[]>> {
    return ApiService.get<ApiResponse<User[]>>(this.baseUrl, { params });
  }

  /**
   * Get user by ID
   */
  static async getById(id: string): Promise<User> {
    return ApiService.get<User>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new user
   */
  static async create(user: User): Promise<User> {
    return ApiService.post<User>(this.baseUrl, user);
  }

  /**
   * Update an existing user
   */
  static async update(id: string, user: User): Promise<User> {
    return ApiService.patch<User>(`${this.baseUrl}/${id}`, user);
  }

  /**
   * Delete a user
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default UserService;
