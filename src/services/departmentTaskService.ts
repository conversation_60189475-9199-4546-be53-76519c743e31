import { ApiService } from './api';

export interface DepartmentTask {
  id?: string;
  department: string;
  task: string;
  assignedBy?: string;
  assignedAt?: string;
  status?: string;
  editable?: string[]; // Array of editable task IDs
  populatedTasks?: TaskField[]; // Array of populated task fields
  // Add other department task properties as needed
}

export interface TaskField {
  _id?: string;
  name: string;
  type: 'string' | 'number' | 'date' | 'select' | 'checkbox' | 'textarea' | 'jsonObject' | 'jsonDate';
  value?: any;
  required?: boolean;
  options?: string[];
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class DepartmentTaskService {
  private static readonly baseUrl = '/departmenttasks/populated';

  /**
   * Get all department tasks
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; department?: string; project?: string }): Promise<ApiResponse<DepartmentTask[]>> {
    return ApiService.get<ApiResponse<DepartmentTask[]>>(this.baseUrl, { params });
  }

  /**
   * Get department task by ID
   */
  static async getById(id: string): Promise<DepartmentTask> {
    return ApiService.get<DepartmentTask>(`${this.baseUrl}/${id}`);
  }

  /**
   * Get tasks by department ID
   */
  static async getByDepartment(departmentId: string): Promise<DepartmentTask[]> {
    return ApiService.get<DepartmentTask[]>(`${this.baseUrl}/department/${departmentId}`);
  }

  /**
   * Create a new department task
   */
  static async create(departmentTask: DepartmentTask): Promise<DepartmentTask> {
    return ApiService.post<DepartmentTask>(this.baseUrl, departmentTask);
  }

  /**
   * Update an existing department task
   */
  static async update(id: string, departmentTask: DepartmentTask): Promise<DepartmentTask> {
    return ApiService.patch<DepartmentTask>(`${this.baseUrl}/${id}`, departmentTask);
  }

  /**
   * Delete a department task
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default DepartmentTaskService;
