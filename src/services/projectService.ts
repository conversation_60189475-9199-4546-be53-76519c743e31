import { ApiService } from './api';

export interface Project {
  id?: string;
  _id?: string; // MongoDB ID format
  name: string;
  description?: string;
  startDate?: string;
  endDate?: string;
  status?: string;
  stage?: string;
  priority?: string;
  customer?: string;
  salesperson?: string; // Add salesperson property
  budget?: string;
  type?: string;
  createdAt?: string;
  updatedAt?: string;
  statusClass?: string;
  progress?: number;
  completedTasks?: number;
  totalTasks?: number;
  team?: any[];
  activities?: any[];
  // Add other project properties as needed
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class ProjectService {
  private static readonly baseUrl = '/projects';

  /**
   * Get all projects
   * @param params Optional parameters for pagination and filtering
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Project[]>> {
    return ApiService.get<ApiResponse<Project[]>>(this.baseUrl, { params });
  }

  /**
   * Get project by ID
   */
  static async getById(id: string): Promise<Project> {
    return ApiService.get<Project>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new project
   */
  static async create(project: Project): Promise<Project> {
    return ApiService.post<Project>(this.baseUrl, project);
  }

  /**
   * Update an existing project
   */
  static async update(id: string, project: Project): Promise<Project> {
    return ApiService.patch<Project>(`${this.baseUrl}/${id}`, project);
  }

  /**
   * Delete a project
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default ProjectService;
