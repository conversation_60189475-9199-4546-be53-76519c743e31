import { ApiService } from './api';

export interface Ticket {
  id?: string;
  title: string;
  description?: string;
  status?: string;
  statustxt?: string;
  priority?: string;
  project?: string;
  assignedTo?: string;
  dueDate?: string;
  createdBy?: string;
  createdAt?: string;
  updatedAt?: string;
}

export interface ApiResponse<T> {
  data: T;
  total?: number;
}

export class TicketService {
  private static readonly baseUrl = 'tickets';

  /**
   * Get all tickets
   */
  static async getAll(params?: { $skip?: number; $limit?: number; $keywords?: string; status?: string; stage?: string }): Promise<ApiResponse<Ticket[]>> {
    return ApiService.get<ApiResponse<Ticket[]>>(this.baseUrl, { params });
  }

  /**
   * Get ticket by ID
   */
  static async getById(id: string): Promise<Ticket> {
    return ApiService.get<Ticket>(`${this.baseUrl}/${id}`);
  }

  /**
   * Create a new ticket
   */
  static async create(ticket: Ticket): Promise<Ticket> {
    return ApiService.post<Ticket>(this.baseUrl, ticket);
  }

  /**
   * Update an existing ticket
   */
  static async update(id: string, ticket: Ticket): Promise<Ticket> {
    return ApiService.patch<Ticket>(`${this.baseUrl}/${id}`, ticket);
  }

  /**
   * Delete a ticket
   */
  static async delete(id: string): Promise<void> {
    return ApiService.delete<void>(`${this.baseUrl}/${id}`);
  }
}

export default TicketService;
