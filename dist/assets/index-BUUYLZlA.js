var sd=Object.defineProperty;var od=(e,t,s)=>t in e?sd(e,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):e[t]=s;var It=(e,t,s)=>od(e,typeof t!="symbol"?t+"":t,s);(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const n of document.querySelectorAll('link[rel="modulepreload"]'))o(n);new MutationObserver(n=>{for(const r of n)if(r.type==="childList")for(const l of r.addedNodes)l.tagName==="LINK"&&l.rel==="modulepreload"&&o(l)}).observe(document,{childList:!0,subtree:!0});function s(n){const r={};return n.integrity&&(r.integrity=n.integrity),n.referrerPolicy&&(r.referrerPolicy=n.referrerPolicy),n.crossOrigin==="use-credentials"?r.credentials="include":n.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function o(n){if(n.ep)return;n.ep=!0;const r=s(n);fetch(n.href,r)}})();/**
* @vue/shared v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**//*! #__NO_SIDE_EFFECTS__ */function Wr(e){const t=Object.create(null);for(const s of e.split(","))t[s]=1;return s=>s in t}const Ce={},Ds=[],jt=()=>{},nd=()=>!1,kn=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&(e.charCodeAt(2)>122||e.charCodeAt(2)<97),Jr=e=>e.startsWith("onUpdate:"),Ke=Object.assign,Gr=(e,t)=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)},rd=Object.prototype.hasOwnProperty,ve=(e,t)=>rd.call(e,t),se=Array.isArray,Ls=e=>Eo(e)==="[object Map]",_n=e=>Eo(e)==="[object Set]",Oi=e=>Eo(e)==="[object Date]",le=e=>typeof e=="function",Ie=e=>typeof e=="string",Rt=e=>typeof e=="symbol",Se=e=>e!==null&&typeof e=="object",aa=e=>(Se(e)||le(e))&&le(e.then)&&le(e.catch),ca=Object.prototype.toString,Eo=e=>ca.call(e),id=e=>Eo(e).slice(8,-1),ua=e=>Eo(e)==="[object Object]",Yr=e=>Ie(e)&&e!=="NaN"&&e[0]!=="-"&&""+parseInt(e,10)===e,ao=Wr(",key,ref,ref_for,ref_key,onVnodeBeforeMount,onVnodeMounted,onVnodeBeforeUpdate,onVnodeUpdated,onVnodeBeforeUnmount,onVnodeUnmounted"),Cn=e=>{const t=Object.create(null);return s=>t[s]||(t[s]=e(s))},ld=/-(\w)/g,gt=Cn(e=>e.replace(ld,(t,s)=>s?s.toUpperCase():"")),ad=/\B([A-Z])/g,xs=Cn(e=>e.replace(ad,"-$1").toLowerCase()),$n=Cn(e=>e.charAt(0).toUpperCase()+e.slice(1)),er=Cn(e=>e?`on${$n(e)}`:""),Qt=(e,t)=>!Object.is(e,t),zo=(e,...t)=>{for(let s=0;s<e.length;s++)e[s](...t)},da=(e,t,s,o=!1)=>{Object.defineProperty(e,t,{configurable:!0,enumerable:!1,writable:o,value:s})},Xo=e=>{const t=parseFloat(e);return isNaN(t)?e:t};let Ii;const En=()=>Ii||(Ii=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:typeof global<"u"?global:{});function Sn(e){if(se(e)){const t={};for(let s=0;s<e.length;s++){const o=e[s],n=Ie(o)?fd(o):Sn(o);if(n)for(const r in n)t[r]=n[r]}return t}else if(Ie(e)||Se(e))return e}const cd=/;(?![^(]*\))/g,ud=/:([^]+)/,dd=/\/\*[^]*?\*\//g;function fd(e){const t={};return e.replace(dd,"").split(cd).forEach(s=>{if(s){const o=s.split(ud);o.length>1&&(t[o[0].trim()]=o[1].trim())}}),t}function ne(e){let t="";if(Ie(e))t=e;else if(se(e))for(let s=0;s<e.length;s++){const o=ne(e[s]);o&&(t+=o+" ")}else if(Se(e))for(const s in e)e[s]&&(t+=s+" ");return t.trim()}const pd="itemscope,allowfullscreen,formnovalidate,ismap,nomodule,novalidate,readonly",hd=Wr(pd);function fa(e){return!!e||e===""}function md(e,t){if(e.length!==t.length)return!1;let s=!0;for(let o=0;s&&o<e.length;o++)s=An(e[o],t[o]);return s}function An(e,t){if(e===t)return!0;let s=Oi(e),o=Oi(t);if(s||o)return s&&o?e.getTime()===t.getTime():!1;if(s=Rt(e),o=Rt(t),s||o)return e===t;if(s=se(e),o=se(t),s||o)return s&&o?md(e,t):!1;if(s=Se(e),o=Se(t),s||o){if(!s||!o)return!1;const n=Object.keys(e).length,r=Object.keys(t).length;if(n!==r)return!1;for(const l in e){const a=e.hasOwnProperty(l),f=t.hasOwnProperty(l);if(a&&!f||!a&&f||!An(e[l],t[l]))return!1}}return String(e)===String(t)}function gd(e,t){return e.findIndex(s=>An(s,t))}const pa=e=>!!(e&&e.__v_isRef===!0),R=e=>Ie(e)?e:e==null?"":se(e)||Se(e)&&(e.toString===ca||!le(e.toString))?pa(e)?R(e.value):JSON.stringify(e,ha,2):String(e),ha=(e,t)=>pa(t)?ha(e,t.value):Ls(t)?{[`Map(${t.size})`]:[...t.entries()].reduce((s,[o,n],r)=>(s[tr(o,r)+" =>"]=n,s),{})}:_n(t)?{[`Set(${t.size})`]:[...t.values()].map(s=>tr(s))}:Rt(t)?tr(t):Se(t)&&!se(t)&&!ua(t)?String(t):t,tr=(e,t="")=>{var s;return Rt(e)?`Symbol(${(s=e.description)!=null?s:t})`:e};/**
* @vue/reactivity v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Ge;class ma{constructor(t=!1){this.detached=t,this._active=!0,this.effects=[],this.cleanups=[],this._isPaused=!1,this.parent=Ge,!t&&Ge&&(this.index=(Ge.scopes||(Ge.scopes=[])).push(this)-1)}get active(){return this._active}pause(){if(this._active){this._isPaused=!0;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].pause();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].pause()}}resume(){if(this._active&&this._isPaused){this._isPaused=!1;let t,s;if(this.scopes)for(t=0,s=this.scopes.length;t<s;t++)this.scopes[t].resume();for(t=0,s=this.effects.length;t<s;t++)this.effects[t].resume()}}run(t){if(this._active){const s=Ge;try{return Ge=this,t()}finally{Ge=s}}}on(){Ge=this}off(){Ge=this.parent}stop(t){if(this._active){this._active=!1;let s,o;for(s=0,o=this.effects.length;s<o;s++)this.effects[s].stop();for(this.effects.length=0,s=0,o=this.cleanups.length;s<o;s++)this.cleanups[s]();if(this.cleanups.length=0,this.scopes){for(s=0,o=this.scopes.length;s<o;s++)this.scopes[s].stop(!0);this.scopes.length=0}if(!this.detached&&this.parent&&!t){const n=this.parent.scopes.pop();n&&n!==this&&(this.parent.scopes[this.index]=n,n.index=this.index)}this.parent=void 0}}}function ga(e){return new ma(e)}function wa(){return Ge}function wd(e,t=!1){Ge&&Ge.cleanups.push(e)}let Ee;const sr=new WeakSet;class ba{constructor(t){this.fn=t,this.deps=void 0,this.depsTail=void 0,this.flags=5,this.next=void 0,this.cleanup=void 0,this.scheduler=void 0,Ge&&Ge.active&&Ge.effects.push(this)}pause(){this.flags|=64}resume(){this.flags&64&&(this.flags&=-65,sr.has(this)&&(sr.delete(this),this.trigger()))}notify(){this.flags&2&&!(this.flags&32)||this.flags&8||ya(this)}run(){if(!(this.flags&1))return this.fn();this.flags|=2,Di(this),xa(this);const t=Ee,s=bt;Ee=this,bt=!0;try{return this.fn()}finally{ka(this),Ee=t,bt=s,this.flags&=-3}}stop(){if(this.flags&1){for(let t=this.deps;t;t=t.nextDep)Qr(t);this.deps=this.depsTail=void 0,Di(this),this.onStop&&this.onStop(),this.flags&=-2}}trigger(){this.flags&64?sr.add(this):this.scheduler?this.scheduler():this.runIfDirty()}runIfDirty(){_r(this)&&this.run()}get dirty(){return _r(this)}}let va=0,co,uo;function ya(e,t=!1){if(e.flags|=8,t){e.next=uo,uo=e;return}e.next=co,co=e}function Zr(){va++}function Xr(){if(--va>0)return;if(uo){let t=uo;for(uo=void 0;t;){const s=t.next;t.next=void 0,t.flags&=-9,t=s}}let e;for(;co;){let t=co;for(co=void 0;t;){const s=t.next;if(t.next=void 0,t.flags&=-9,t.flags&1)try{t.trigger()}catch(o){e||(e=o)}t=s}}if(e)throw e}function xa(e){for(let t=e.deps;t;t=t.nextDep)t.version=-1,t.prevActiveLink=t.dep.activeLink,t.dep.activeLink=t}function ka(e){let t,s=e.depsTail,o=s;for(;o;){const n=o.prevDep;o.version===-1?(o===s&&(s=n),Qr(o),bd(o)):t=o,o.dep.activeLink=o.prevActiveLink,o.prevActiveLink=void 0,o=n}e.deps=t,e.depsTail=s}function _r(e){for(let t=e.deps;t;t=t.nextDep)if(t.dep.version!==t.version||t.dep.computed&&(_a(t.dep.computed)||t.dep.version!==t.version))return!0;return!!e._dirty}function _a(e){if(e.flags&4&&!(e.flags&16)||(e.flags&=-17,e.globalVersion===bo))return;e.globalVersion=bo;const t=e.dep;if(e.flags|=2,t.version>0&&!e.isSSR&&e.deps&&!_r(e)){e.flags&=-3;return}const s=Ee,o=bt;Ee=e,bt=!0;try{xa(e);const n=e.fn(e._value);(t.version===0||Qt(n,e._value))&&(e._value=n,t.version++)}catch(n){throw t.version++,n}finally{Ee=s,bt=o,ka(e),e.flags&=-3}}function Qr(e,t=!1){const{dep:s,prevSub:o,nextSub:n}=e;if(o&&(o.nextSub=n,e.prevSub=void 0),n&&(n.prevSub=o,e.nextSub=void 0),s.subs===e&&(s.subs=o,!o&&s.computed)){s.computed.flags&=-5;for(let r=s.computed.deps;r;r=r.nextDep)Qr(r,!0)}!t&&!--s.sc&&s.map&&s.map.delete(s.key)}function bd(e){const{prevDep:t,nextDep:s}=e;t&&(t.nextDep=s,e.prevDep=void 0),s&&(s.prevDep=t,e.nextDep=void 0)}let bt=!0;const Ca=[];function ts(){Ca.push(bt),bt=!1}function ss(){const e=Ca.pop();bt=e===void 0?!0:e}function Di(e){const{cleanup:t}=e;if(e.cleanup=void 0,t){const s=Ee;Ee=void 0;try{t()}finally{Ee=s}}}let bo=0;class vd{constructor(t,s){this.sub=t,this.dep=s,this.version=s.version,this.nextDep=this.prevDep=this.nextSub=this.prevSub=this.prevActiveLink=void 0}}class ei{constructor(t){this.computed=t,this.version=0,this.activeLink=void 0,this.subs=void 0,this.map=void 0,this.key=void 0,this.sc=0}track(t){if(!Ee||!bt||Ee===this.computed)return;let s=this.activeLink;if(s===void 0||s.sub!==Ee)s=this.activeLink=new vd(Ee,this),Ee.deps?(s.prevDep=Ee.depsTail,Ee.depsTail.nextDep=s,Ee.depsTail=s):Ee.deps=Ee.depsTail=s,$a(s);else if(s.version===-1&&(s.version=this.version,s.nextDep)){const o=s.nextDep;o.prevDep=s.prevDep,s.prevDep&&(s.prevDep.nextDep=o),s.prevDep=Ee.depsTail,s.nextDep=void 0,Ee.depsTail.nextDep=s,Ee.depsTail=s,Ee.deps===s&&(Ee.deps=o)}return s}trigger(t){this.version++,bo++,this.notify(t)}notify(t){Zr();try{for(let s=this.subs;s;s=s.prevSub)s.sub.notify()&&s.sub.dep.notify()}finally{Xr()}}}function $a(e){if(e.dep.sc++,e.sub.flags&4){const t=e.dep.computed;if(t&&!e.dep.subs){t.flags|=20;for(let o=t.deps;o;o=o.nextDep)$a(o)}const s=e.dep.subs;s!==e&&(e.prevSub=s,s&&(s.nextSub=e)),e.dep.subs=e}}const Qo=new WeakMap,ds=Symbol(""),Cr=Symbol(""),vo=Symbol("");function He(e,t,s){if(bt&&Ee){let o=Qo.get(e);o||Qo.set(e,o=new Map);let n=o.get(s);n||(o.set(s,n=new ei),n.map=o,n.key=s),n.track()}}function Nt(e,t,s,o,n,r){const l=Qo.get(e);if(!l){bo++;return}const a=f=>{f&&f.trigger()};if(Zr(),t==="clear")l.forEach(a);else{const f=se(e),u=f&&Yr(s);if(f&&s==="length"){const c=Number(o);l.forEach((d,p)=>{(p==="length"||p===vo||!Rt(p)&&p>=c)&&a(d)})}else switch((s!==void 0||l.has(void 0))&&a(l.get(s)),u&&a(l.get(vo)),t){case"add":f?u&&a(l.get("length")):(a(l.get(ds)),Ls(e)&&a(l.get(Cr)));break;case"delete":f||(a(l.get(ds)),Ls(e)&&a(l.get(Cr)));break;case"set":Ls(e)&&a(l.get(ds));break}}Xr()}function yd(e,t){const s=Qo.get(e);return s&&s.get(t)}function Es(e){const t=pe(e);return t===e?t:(He(t,"iterate",vo),ht(e)?t:t.map(ze))}function Pn(e){return He(e=pe(e),"iterate",vo),e}const xd={__proto__:null,[Symbol.iterator](){return or(this,Symbol.iterator,ze)},concat(...e){return Es(this).concat(...e.map(t=>se(t)?Es(t):t))},entries(){return or(this,"entries",e=>(e[1]=ze(e[1]),e))},every(e,t){return Dt(this,"every",e,t,void 0,arguments)},filter(e,t){return Dt(this,"filter",e,t,s=>s.map(ze),arguments)},find(e,t){return Dt(this,"find",e,t,ze,arguments)},findIndex(e,t){return Dt(this,"findIndex",e,t,void 0,arguments)},findLast(e,t){return Dt(this,"findLast",e,t,ze,arguments)},findLastIndex(e,t){return Dt(this,"findLastIndex",e,t,void 0,arguments)},forEach(e,t){return Dt(this,"forEach",e,t,void 0,arguments)},includes(...e){return nr(this,"includes",e)},indexOf(...e){return nr(this,"indexOf",e)},join(e){return Es(this).join(e)},lastIndexOf(...e){return nr(this,"lastIndexOf",e)},map(e,t){return Dt(this,"map",e,t,void 0,arguments)},pop(){return oo(this,"pop")},push(...e){return oo(this,"push",e)},reduce(e,...t){return Li(this,"reduce",e,t)},reduceRight(e,...t){return Li(this,"reduceRight",e,t)},shift(){return oo(this,"shift")},some(e,t){return Dt(this,"some",e,t,void 0,arguments)},splice(...e){return oo(this,"splice",e)},toReversed(){return Es(this).toReversed()},toSorted(e){return Es(this).toSorted(e)},toSpliced(...e){return Es(this).toSpliced(...e)},unshift(...e){return oo(this,"unshift",e)},values(){return or(this,"values",ze)}};function or(e,t,s){const o=Pn(e),n=o[t]();return o!==e&&!ht(e)&&(n._next=n.next,n.next=()=>{const r=n._next();return r.value&&(r.value=s(r.value)),r}),n}const kd=Array.prototype;function Dt(e,t,s,o,n,r){const l=Pn(e),a=l!==e&&!ht(e),f=l[t];if(f!==kd[t]){const d=f.apply(e,r);return a?ze(d):d}let u=s;l!==e&&(a?u=function(d,p){return s.call(this,ze(d),p,e)}:s.length>2&&(u=function(d,p){return s.call(this,d,p,e)}));const c=f.call(l,u,o);return a&&n?n(c):c}function Li(e,t,s,o){const n=Pn(e);let r=s;return n!==e&&(ht(e)?s.length>3&&(r=function(l,a,f){return s.call(this,l,a,f,e)}):r=function(l,a,f){return s.call(this,l,ze(a),f,e)}),n[t](r,...o)}function nr(e,t,s){const o=pe(e);He(o,"iterate",vo);const n=o[t](...s);return(n===-1||n===!1)&&oi(s[0])?(s[0]=pe(s[0]),o[t](...s)):n}function oo(e,t,s=[]){ts(),Zr();const o=pe(e)[t].apply(e,s);return Xr(),ss(),o}const _d=Wr("__proto__,__v_isRef,__isVue"),Ea=new Set(Object.getOwnPropertyNames(Symbol).filter(e=>e!=="arguments"&&e!=="caller").map(e=>Symbol[e]).filter(Rt));function Cd(e){Rt(e)||(e=String(e));const t=pe(this);return He(t,"has",e),t.hasOwnProperty(e)}class Sa{constructor(t=!1,s=!1){this._isReadonly=t,this._isShallow=s}get(t,s,o){if(s==="__v_skip")return t.__v_skip;const n=this._isReadonly,r=this._isShallow;if(s==="__v_isReactive")return!n;if(s==="__v_isReadonly")return n;if(s==="__v_isShallow")return r;if(s==="__v_raw")return o===(n?r?Bd:ja:r?Ta:Pa).get(t)||Object.getPrototypeOf(t)===Object.getPrototypeOf(o)?t:void 0;const l=se(t);if(!n){let f;if(l&&(f=xd[s]))return f;if(s==="hasOwnProperty")return Cd}const a=Reflect.get(t,s,Me(t)?t:o);return(Rt(s)?Ea.has(s):_d(s))||(n||He(t,"get",s),r)?a:Me(a)?l&&Yr(s)?a:a.value:Se(a)?n?Ma(a):So(a):a}}class Aa extends Sa{constructor(t=!1){super(!1,t)}set(t,s,o,n){let r=t[s];if(!this._isShallow){const f=ms(r);if(!ht(o)&&!ms(o)&&(r=pe(r),o=pe(o)),!se(t)&&Me(r)&&!Me(o))return f?!1:(r.value=o,!0)}const l=se(t)&&Yr(s)?Number(s)<t.length:ve(t,s),a=Reflect.set(t,s,o,Me(t)?t:n);return t===pe(n)&&(l?Qt(o,r)&&Nt(t,"set",s,o):Nt(t,"add",s,o)),a}deleteProperty(t,s){const o=ve(t,s);t[s];const n=Reflect.deleteProperty(t,s);return n&&o&&Nt(t,"delete",s,void 0),n}has(t,s){const o=Reflect.has(t,s);return(!Rt(s)||!Ea.has(s))&&He(t,"has",s),o}ownKeys(t){return He(t,"iterate",se(t)?"length":ds),Reflect.ownKeys(t)}}class $d extends Sa{constructor(t=!1){super(!0,t)}set(t,s){return!0}deleteProperty(t,s){return!0}}const Ed=new Aa,Sd=new $d,Ad=new Aa(!0);const $r=e=>e,Uo=e=>Reflect.getPrototypeOf(e);function Pd(e,t,s){return function(...o){const n=this.__v_raw,r=pe(n),l=Ls(r),a=e==="entries"||e===Symbol.iterator&&l,f=e==="keys"&&l,u=n[e](...o),c=s?$r:t?Er:ze;return!t&&He(r,"iterate",f?Cr:ds),{next(){const{value:d,done:p}=u.next();return p?{value:d,done:p}:{value:a?[c(d[0]),c(d[1])]:c(d),done:p}},[Symbol.iterator](){return this}}}}function No(e){return function(...t){return e==="delete"?!1:e==="clear"?void 0:this}}function Td(e,t){const s={get(n){const r=this.__v_raw,l=pe(r),a=pe(n);e||(Qt(n,a)&&He(l,"get",n),He(l,"get",a));const{has:f}=Uo(l),u=t?$r:e?Er:ze;if(f.call(l,n))return u(r.get(n));if(f.call(l,a))return u(r.get(a));r!==l&&r.get(n)},get size(){const n=this.__v_raw;return!e&&He(pe(n),"iterate",ds),Reflect.get(n,"size",n)},has(n){const r=this.__v_raw,l=pe(r),a=pe(n);return e||(Qt(n,a)&&He(l,"has",n),He(l,"has",a)),n===a?r.has(n):r.has(n)||r.has(a)},forEach(n,r){const l=this,a=l.__v_raw,f=pe(a),u=t?$r:e?Er:ze;return!e&&He(f,"iterate",ds),a.forEach((c,d)=>n.call(r,u(c),u(d),l))}};return Ke(s,e?{add:No("add"),set:No("set"),delete:No("delete"),clear:No("clear")}:{add(n){!t&&!ht(n)&&!ms(n)&&(n=pe(n));const r=pe(this);return Uo(r).has.call(r,n)||(r.add(n),Nt(r,"add",n,n)),this},set(n,r){!t&&!ht(r)&&!ms(r)&&(r=pe(r));const l=pe(this),{has:a,get:f}=Uo(l);let u=a.call(l,n);u||(n=pe(n),u=a.call(l,n));const c=f.call(l,n);return l.set(n,r),u?Qt(r,c)&&Nt(l,"set",n,r):Nt(l,"add",n,r),this},delete(n){const r=pe(this),{has:l,get:a}=Uo(r);let f=l.call(r,n);f||(n=pe(n),f=l.call(r,n)),a&&a.call(r,n);const u=r.delete(n);return f&&Nt(r,"delete",n,void 0),u},clear(){const n=pe(this),r=n.size!==0,l=n.clear();return r&&Nt(n,"clear",void 0,void 0),l}}),["keys","values","entries",Symbol.iterator].forEach(n=>{s[n]=Pd(n,e,t)}),s}function ti(e,t){const s=Td(e,t);return(o,n,r)=>n==="__v_isReactive"?!e:n==="__v_isReadonly"?e:n==="__v_raw"?o:Reflect.get(ve(s,n)&&n in o?s:o,n,r)}const jd={get:ti(!1,!1)},Rd={get:ti(!1,!0)},Md={get:ti(!0,!1)};const Pa=new WeakMap,Ta=new WeakMap,ja=new WeakMap,Bd=new WeakMap;function Od(e){switch(e){case"Object":case"Array":return 1;case"Map":case"Set":case"WeakMap":case"WeakSet":return 2;default:return 0}}function Id(e){return e.__v_skip||!Object.isExtensible(e)?0:Od(id(e))}function So(e){return ms(e)?e:si(e,!1,Ed,jd,Pa)}function Ra(e){return si(e,!1,Ad,Rd,Ta)}function Ma(e){return si(e,!0,Sd,Md,ja)}function si(e,t,s,o,n){if(!Se(e)||e.__v_raw&&!(t&&e.__v_isReactive))return e;const r=n.get(e);if(r)return r;const l=Id(e);if(l===0)return e;const a=new Proxy(e,l===2?o:s);return n.set(e,a),a}function es(e){return ms(e)?es(e.__v_raw):!!(e&&e.__v_isReactive)}function ms(e){return!!(e&&e.__v_isReadonly)}function ht(e){return!!(e&&e.__v_isShallow)}function oi(e){return e?!!e.__v_raw:!1}function pe(e){const t=e&&e.__v_raw;return t?pe(t):e}function ni(e){return!ve(e,"__v_skip")&&Object.isExtensible(e)&&da(e,"__v_skip",!0),e}const ze=e=>Se(e)?So(e):e,Er=e=>Se(e)?Ma(e):e;function Me(e){return e?e.__v_isRef===!0:!1}function W(e){return Ba(e,!1)}function Dd(e){return Ba(e,!0)}function Ba(e,t){return Me(e)?e:new Ld(e,t)}class Ld{constructor(t,s){this.dep=new ei,this.__v_isRef=!0,this.__v_isShallow=!1,this._rawValue=s?t:pe(t),this._value=s?t:ze(t),this.__v_isShallow=s}get value(){return this.dep.track(),this._value}set value(t){const s=this._rawValue,o=this.__v_isShallow||ht(t)||ms(t);t=o?t:pe(t),Qt(t,s)&&(this._rawValue=t,this._value=o?t:ze(t),this.dep.trigger())}}function Fs(e){return Me(e)?e.value:e}const Fd={get:(e,t,s)=>t==="__v_raw"?e:Fs(Reflect.get(e,t,s)),set:(e,t,s,o)=>{const n=e[t];return Me(n)&&!Me(s)?(n.value=s,!0):Reflect.set(e,t,s,o)}};function Oa(e){return es(e)?e:new Proxy(e,Fd)}function Ud(e){const t=se(e)?new Array(e.length):{};for(const s in e)t[s]=Vd(e,s);return t}class Nd{constructor(t,s,o){this._object=t,this._key=s,this._defaultValue=o,this.__v_isRef=!0,this._value=void 0}get value(){const t=this._object[this._key];return this._value=t===void 0?this._defaultValue:t}set value(t){this._object[this._key]=t}get dep(){return yd(pe(this._object),this._key)}}function Vd(e,t,s){const o=e[t];return Me(o)?o:new Nd(e,t,s)}class Hd{constructor(t,s,o){this.fn=t,this.setter=s,this._value=void 0,this.dep=new ei(this),this.__v_isRef=!0,this.deps=void 0,this.depsTail=void 0,this.flags=16,this.globalVersion=bo-1,this.next=void 0,this.effect=this,this.__v_isReadonly=!s,this.isSSR=o}notify(){if(this.flags|=16,!(this.flags&8)&&Ee!==this)return ya(this,!0),!0}get value(){const t=this.dep.track();return _a(this),t&&(t.version=this.dep.version),this._value}set value(t){this.setter&&this.setter(t)}}function zd(e,t,s=!1){let o,n;return le(e)?o=e:(o=e.get,n=e.set),new Hd(o,n,s)}const Vo={},en=new WeakMap;let ls;function qd(e,t=!1,s=ls){if(s){let o=en.get(s);o||en.set(s,o=[]),o.push(e)}}function Kd(e,t,s=Ce){const{immediate:o,deep:n,once:r,scheduler:l,augmentJob:a,call:f}=s,u=T=>n?T:ht(T)||n===!1||n===0?Vt(T,1):Vt(T);let c,d,p,h,v=!1,$=!1;if(Me(e)?(d=()=>e.value,v=ht(e)):es(e)?(d=()=>u(e),v=!0):se(e)?($=!0,v=e.some(T=>es(T)||ht(T)),d=()=>e.map(T=>{if(Me(T))return T.value;if(es(T))return u(T);if(le(T))return f?f(T,2):T()})):le(e)?t?d=f?()=>f(e,2):e:d=()=>{if(p){ts();try{p()}finally{ss()}}const T=ls;ls=c;try{return f?f(e,3,[h]):e(h)}finally{ls=T}}:d=jt,t&&n){const T=d,L=n===!0?1/0:n;d=()=>Vt(T(),L)}const y=wa(),x=()=>{c.stop(),y&&y.active&&Gr(y.effects,c)};if(r&&t){const T=t;t=(...L)=>{T(...L),x()}}let m=$?new Array(e.length).fill(Vo):Vo;const b=T=>{if(!(!(c.flags&1)||!c.dirty&&!T))if(t){const L=c.run();if(n||v||($?L.some((J,j)=>Qt(J,m[j])):Qt(L,m))){p&&p();const J=ls;ls=c;try{const j=[L,m===Vo?void 0:$&&m[0]===Vo?[]:m,h];f?f(t,3,j):t(...j),m=L}finally{ls=J}}}else c.run()};return a&&a(b),c=new ba(d),c.scheduler=l?()=>l(b,!1):b,h=T=>qd(T,!1,c),p=c.onStop=()=>{const T=en.get(c);if(T){if(f)f(T,4);else for(const L of T)L();en.delete(c)}},t?o?b(!0):m=c.run():l?l(b.bind(null,!0),!0):c.run(),x.pause=c.pause.bind(c),x.resume=c.resume.bind(c),x.stop=x,x}function Vt(e,t=1/0,s){if(t<=0||!Se(e)||e.__v_skip||(s=s||new Set,s.has(e)))return e;if(s.add(e),t--,Me(e))Vt(e.value,t,s);else if(se(e))for(let o=0;o<e.length;o++)Vt(e[o],t,s);else if(_n(e)||Ls(e))e.forEach(o=>{Vt(o,t,s)});else if(ua(e)){for(const o in e)Vt(e[o],t,s);for(const o of Object.getOwnPropertySymbols(e))Object.prototype.propertyIsEnumerable.call(e,o)&&Vt(e[o],t,s)}return e}/**
* @vue/runtime-core v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/function Ao(e,t,s,o){try{return o?e(...o):e()}catch(n){Tn(n,t,s)}}function Mt(e,t,s,o){if(le(e)){const n=Ao(e,t,s,o);return n&&aa(n)&&n.catch(r=>{Tn(r,t,s)}),n}if(se(e)){const n=[];for(let r=0;r<e.length;r++)n.push(Mt(e[r],t,s,o));return n}}function Tn(e,t,s,o=!0){const n=t?t.vnode:null,{errorHandler:r,throwUnhandledErrorInProduction:l}=t&&t.appContext.config||Ce;if(t){let a=t.parent;const f=t.proxy,u=`https://vuejs.org/error-reference/#runtime-${s}`;for(;a;){const c=a.ec;if(c){for(let d=0;d<c.length;d++)if(c[d](e,f,u)===!1)return}a=a.parent}if(r){ts(),Ao(r,null,10,[e,f,u]),ss();return}}Wd(e,s,n,o,l)}function Wd(e,t,s,o=!0,n=!1){if(n)throw e;console.error(e)}const Ye=[];let At=-1;const Us=[];let Jt=null,As=0;const Ia=Promise.resolve();let tn=null;function jn(e){const t=tn||Ia;return e?t.then(this?e.bind(this):e):t}function Jd(e){let t=At+1,s=Ye.length;for(;t<s;){const o=t+s>>>1,n=Ye[o],r=yo(n);r<e||r===e&&n.flags&2?t=o+1:s=o}return t}function ri(e){if(!(e.flags&1)){const t=yo(e),s=Ye[Ye.length-1];!s||!(e.flags&2)&&t>=yo(s)?Ye.push(e):Ye.splice(Jd(t),0,e),e.flags|=1,Da()}}function Da(){tn||(tn=Ia.then(Fa))}function Gd(e){se(e)?Us.push(...e):Jt&&e.id===-1?Jt.splice(As+1,0,e):e.flags&1||(Us.push(e),e.flags|=1),Da()}function Fi(e,t,s=At+1){for(;s<Ye.length;s++){const o=Ye[s];if(o&&o.flags&2){if(e&&o.id!==e.uid)continue;Ye.splice(s,1),s--,o.flags&4&&(o.flags&=-2),o(),o.flags&4||(o.flags&=-2)}}}function La(e){if(Us.length){const t=[...new Set(Us)].sort((s,o)=>yo(s)-yo(o));if(Us.length=0,Jt){Jt.push(...t);return}for(Jt=t,As=0;As<Jt.length;As++){const s=Jt[As];s.flags&4&&(s.flags&=-2),s.flags&8||s(),s.flags&=-2}Jt=null,As=0}}const yo=e=>e.id==null?e.flags&2?-1:1/0:e.id;function Fa(e){try{for(At=0;At<Ye.length;At++){const t=Ye[At];t&&!(t.flags&8)&&(t.flags&4&&(t.flags&=-2),Ao(t,t.i,t.i?15:14),t.flags&4||(t.flags&=-2))}}finally{for(;At<Ye.length;At++){const t=Ye[At];t&&(t.flags&=-2)}At=-1,Ye.length=0,La(),tn=null,(Ye.length||Us.length)&&Fa()}}let tt=null,Ua=null;function sn(e){const t=tt;return tt=e,Ua=e&&e.type.__scopeId||null,t}function Na(e,t=tt,s){if(!t||e._n)return e;const o=(...n)=>{o._d&&Gi(-1);const r=sn(t);let l;try{l=e(...n)}finally{sn(r),o._d&&Gi(1)}return l};return o._n=!0,o._c=!0,o._d=!0,o}function oe(e,t){if(tt===null)return e;const s=On(tt),o=e.dirs||(e.dirs=[]);for(let n=0;n<t.length;n++){let[r,l,a,f=Ce]=t[n];r&&(le(r)&&(r={mounted:r,updated:r}),r.deep&&Vt(l),o.push({dir:r,instance:s,value:l,oldValue:void 0,arg:a,modifiers:f}))}return e}function ns(e,t,s,o){const n=e.dirs,r=t&&t.dirs;for(let l=0;l<n.length;l++){const a=n[l];r&&(a.oldValue=r[l].value);let f=a.dir[o];f&&(ts(),Mt(f,s,8,[e.el,a,e,t]),ss())}}const Yd=Symbol("_vte"),Zd=e=>e.__isTeleport;function ii(e,t){e.shapeFlag&6&&e.component?(e.transition=t,ii(e.component.subTree,t)):e.shapeFlag&128?(e.ssContent.transition=t.clone(e.ssContent),e.ssFallback.transition=t.clone(e.ssFallback)):e.transition=t}/*! #__NO_SIDE_EFFECTS__ */function Pe(e,t){return le(e)?Ke({name:e.name},t,{setup:e}):e}function Va(e){e.ids=[e.ids[0]+e.ids[2]+++"-",0,0]}function on(e,t,s,o,n=!1){if(se(e)){e.forEach((v,$)=>on(v,t&&(se(t)?t[$]:t),s,o,n));return}if(fo(o)&&!n){o.shapeFlag&512&&o.type.__asyncResolved&&o.component.subTree.component&&on(e,t,s,o.component.subTree);return}const r=o.shapeFlag&4?On(o.component):o.el,l=n?null:r,{i:a,r:f}=e,u=t&&t.r,c=a.refs===Ce?a.refs={}:a.refs,d=a.setupState,p=pe(d),h=d===Ce?()=>!1:v=>ve(p,v);if(u!=null&&u!==f&&(Ie(u)?(c[u]=null,h(u)&&(d[u]=null)):Me(u)&&(u.value=null)),le(f))Ao(f,a,12,[l,c]);else{const v=Ie(f),$=Me(f);if(v||$){const y=()=>{if(e.f){const x=v?h(f)?d[f]:c[f]:f.value;n?se(x)&&Gr(x,r):se(x)?x.includes(r)||x.push(r):v?(c[f]=[r],h(f)&&(d[f]=c[f])):(f.value=[r],e.k&&(c[e.k]=f.value))}else v?(c[f]=l,h(f)&&(d[f]=l)):$&&(f.value=l,e.k&&(c[e.k]=l))};l?(y.id=-1,at(y,s)):y()}}}En().requestIdleCallback;En().cancelIdleCallback;const fo=e=>!!e.type.__asyncLoader,Ha=e=>e.type.__isKeepAlive;function Xd(e,t){za(e,"a",t)}function Qd(e,t){za(e,"da",t)}function za(e,t,s=Ue){const o=e.__wdc||(e.__wdc=()=>{let n=s;for(;n;){if(n.isDeactivated)return;n=n.parent}return e()});if(Rn(t,o,s),s){let n=s.parent;for(;n&&n.parent;)Ha(n.parent.vnode)&&ef(o,t,s,n),n=n.parent}}function ef(e,t,s,o){const n=Rn(t,e,o,!0);Ka(()=>{Gr(o[t],n)},s)}function Rn(e,t,s=Ue,o=!1){if(s){const n=s[e]||(s[e]=[]),r=t.__weh||(t.__weh=(...l)=>{ts();const a=Po(s),f=Mt(t,s,e,l);return a(),ss(),f});return o?n.unshift(r):n.push(r),r}}const qt=e=>(t,s=Ue)=>{(!ko||e==="sp")&&Rn(e,(...o)=>t(...o),s)},tf=qt("bm"),Ve=qt("m"),sf=qt("bu"),of=qt("u"),qa=qt("bum"),Ka=qt("um"),nf=qt("sp"),rf=qt("rtg"),lf=qt("rtc");function af(e,t=Ue){Rn("ec",e,t)}const cf="components";function li(e,t){return df(cf,e,!0,t)||e}const uf=Symbol.for("v-ndc");function df(e,t,s=!0,o=!1){const n=tt||Ue;if(n){const r=n.type;{const a=Xf(r,!1);if(a&&(a===t||a===gt(t)||a===$n(gt(t))))return r}const l=Ui(n[e]||r[e],t)||Ui(n.appContext[e],t);return!l&&o?r:l}}function Ui(e,t){return e&&(e[t]||e[gt(t)]||e[$n(gt(t))])}function xe(e,t,s,o){let n;const r=s,l=se(e);if(l||Ie(e)){const a=l&&es(e);let f=!1;a&&(f=!ht(e),e=Pn(e)),n=new Array(e.length);for(let u=0,c=e.length;u<c;u++)n[u]=t(f?ze(e[u]):e[u],u,void 0,r)}else if(typeof e=="number"){n=new Array(e);for(let a=0;a<e;a++)n[a]=t(a+1,a,void 0,r)}else if(Se(e))if(e[Symbol.iterator])n=Array.from(e,(a,f)=>t(a,f,void 0,r));else{const a=Object.keys(e);n=new Array(a.length);for(let f=0,u=a.length;f<u;f++){const c=a[f];n[f]=t(e[c],c,f,r)}}else n=[];return n}const Sr=e=>e?pc(e)?On(e):Sr(e.parent):null,po=Ke(Object.create(null),{$:e=>e,$el:e=>e.vnode.el,$data:e=>e.data,$props:e=>e.props,$attrs:e=>e.attrs,$slots:e=>e.slots,$refs:e=>e.refs,$parent:e=>Sr(e.parent),$root:e=>Sr(e.root),$host:e=>e.ce,$emit:e=>e.emit,$options:e=>Ja(e),$forceUpdate:e=>e.f||(e.f=()=>{ri(e.update)}),$nextTick:e=>e.n||(e.n=jn.bind(e.proxy)),$watch:e=>Mf.bind(e)}),rr=(e,t)=>e!==Ce&&!e.__isScriptSetup&&ve(e,t),ff={get({_:e},t){if(t==="__v_skip")return!0;const{ctx:s,setupState:o,data:n,props:r,accessCache:l,type:a,appContext:f}=e;let u;if(t[0]!=="$"){const h=l[t];if(h!==void 0)switch(h){case 1:return o[t];case 2:return n[t];case 4:return s[t];case 3:return r[t]}else{if(rr(o,t))return l[t]=1,o[t];if(n!==Ce&&ve(n,t))return l[t]=2,n[t];if((u=e.propsOptions[0])&&ve(u,t))return l[t]=3,r[t];if(s!==Ce&&ve(s,t))return l[t]=4,s[t];Ar&&(l[t]=0)}}const c=po[t];let d,p;if(c)return t==="$attrs"&&He(e.attrs,"get",""),c(e);if((d=a.__cssModules)&&(d=d[t]))return d;if(s!==Ce&&ve(s,t))return l[t]=4,s[t];if(p=f.config.globalProperties,ve(p,t))return p[t]},set({_:e},t,s){const{data:o,setupState:n,ctx:r}=e;return rr(n,t)?(n[t]=s,!0):o!==Ce&&ve(o,t)?(o[t]=s,!0):ve(e.props,t)||t[0]==="$"&&t.slice(1)in e?!1:(r[t]=s,!0)},has({_:{data:e,setupState:t,accessCache:s,ctx:o,appContext:n,propsOptions:r}},l){let a;return!!s[l]||e!==Ce&&ve(e,l)||rr(t,l)||(a=r[0])&&ve(a,l)||ve(o,l)||ve(po,l)||ve(n.config.globalProperties,l)},defineProperty(e,t,s){return s.get!=null?e._.accessCache[t]=0:ve(s,"value")&&this.set(e,t,s.value,null),Reflect.defineProperty(e,t,s)}};function Ni(e){return se(e)?e.reduce((t,s)=>(t[s]=null,t),{}):e}let Ar=!0;function pf(e){const t=Ja(e),s=e.proxy,o=e.ctx;Ar=!1,t.beforeCreate&&Vi(t.beforeCreate,e,"bc");const{data:n,computed:r,methods:l,watch:a,provide:f,inject:u,created:c,beforeMount:d,mounted:p,beforeUpdate:h,updated:v,activated:$,deactivated:y,beforeDestroy:x,beforeUnmount:m,destroyed:b,unmounted:T,render:L,renderTracked:J,renderTriggered:j,errorCaptured:_,serverPrefetch:E,expose:q,inheritAttrs:we,components:Te,directives:je,filters:Ot}=t;if(u&&hf(u,o,null),l)for(const F in l){const G=l[F];le(G)&&(o[F]=G.bind(s))}if(n){const F=n.call(s,s);Se(F)&&(e.data=So(F))}if(Ar=!0,r)for(const F in r){const G=r[F],Fe=le(G)?G.bind(s,s):le(G.get)?G.get.bind(s,s):jt,rt=!le(G)&&le(G.set)?G.set.bind(s):jt,kt=Y({get:Fe,set:rt});Object.defineProperty(o,F,{enumerable:!0,configurable:!0,get:()=>kt.value,set:Qe=>kt.value=Qe})}if(a)for(const F in a)Wa(a[F],o,s,F);if(f){const F=le(f)?f.call(s):f;Reflect.ownKeys(F).forEach(G=>{qo(G,F[G])})}c&&Vi(c,e,"c");function X(F,G){se(G)?G.forEach(Fe=>F(Fe.bind(s))):G&&F(G.bind(s))}if(X(tf,d),X(Ve,p),X(sf,h),X(of,v),X(Xd,$),X(Qd,y),X(af,_),X(lf,J),X(rf,j),X(qa,m),X(Ka,T),X(nf,E),se(q))if(q.length){const F=e.exposed||(e.exposed={});q.forEach(G=>{Object.defineProperty(F,G,{get:()=>s[G],set:Fe=>s[G]=Fe})})}else e.exposed||(e.exposed={});L&&e.render===jt&&(e.render=L),we!=null&&(e.inheritAttrs=we),Te&&(e.components=Te),je&&(e.directives=je),E&&Va(e)}function hf(e,t,s=jt){se(e)&&(e=Pr(e));for(const o in e){const n=e[o];let r;Se(n)?"default"in n?r=mt(n.from||o,n.default,!0):r=mt(n.from||o):r=mt(n),Me(r)?Object.defineProperty(t,o,{enumerable:!0,configurable:!0,get:()=>r.value,set:l=>r.value=l}):t[o]=r}}function Vi(e,t,s){Mt(se(e)?e.map(o=>o.bind(t.proxy)):e.bind(t.proxy),t,s)}function Wa(e,t,s,o){let n=o.includes(".")?ac(s,o):()=>s[o];if(Ie(e)){const r=t[e];le(r)&&De(n,r)}else if(le(e))De(n,e.bind(s));else if(Se(e))if(se(e))e.forEach(r=>Wa(r,t,s,o));else{const r=le(e.handler)?e.handler.bind(s):t[e.handler];le(r)&&De(n,r,e)}}function Ja(e){const t=e.type,{mixins:s,extends:o}=t,{mixins:n,optionsCache:r,config:{optionMergeStrategies:l}}=e.appContext,a=r.get(t);let f;return a?f=a:!n.length&&!s&&!o?f=t:(f={},n.length&&n.forEach(u=>nn(f,u,l,!0)),nn(f,t,l)),Se(t)&&r.set(t,f),f}function nn(e,t,s,o=!1){const{mixins:n,extends:r}=t;r&&nn(e,r,s,!0),n&&n.forEach(l=>nn(e,l,s,!0));for(const l in t)if(!(o&&l==="expose")){const a=mf[l]||s&&s[l];e[l]=a?a(e[l],t[l]):t[l]}return e}const mf={data:Hi,props:zi,emits:zi,methods:lo,computed:lo,beforeCreate:Je,created:Je,beforeMount:Je,mounted:Je,beforeUpdate:Je,updated:Je,beforeDestroy:Je,beforeUnmount:Je,destroyed:Je,unmounted:Je,activated:Je,deactivated:Je,errorCaptured:Je,serverPrefetch:Je,components:lo,directives:lo,watch:wf,provide:Hi,inject:gf};function Hi(e,t){return t?e?function(){return Ke(le(e)?e.call(this,this):e,le(t)?t.call(this,this):t)}:t:e}function gf(e,t){return lo(Pr(e),Pr(t))}function Pr(e){if(se(e)){const t={};for(let s=0;s<e.length;s++)t[e[s]]=e[s];return t}return e}function Je(e,t){return e?[...new Set([].concat(e,t))]:t}function lo(e,t){return e?Ke(Object.create(null),e,t):t}function zi(e,t){return e?se(e)&&se(t)?[...new Set([...e,...t])]:Ke(Object.create(null),Ni(e),Ni(t??{})):t}function wf(e,t){if(!e)return t;if(!t)return e;const s=Ke(Object.create(null),e);for(const o in t)s[o]=Je(e[o],t[o]);return s}function Ga(){return{app:null,config:{isNativeTag:nd,performance:!1,globalProperties:{},optionMergeStrategies:{},errorHandler:void 0,warnHandler:void 0,compilerOptions:{}},mixins:[],components:{},directives:{},provides:Object.create(null),optionsCache:new WeakMap,propsCache:new WeakMap,emitsCache:new WeakMap}}let bf=0;function vf(e,t){return function(o,n=null){le(o)||(o=Ke({},o)),n!=null&&!Se(n)&&(n=null);const r=Ga(),l=new WeakSet,a=[];let f=!1;const u=r.app={_uid:bf++,_component:o,_props:n,_container:null,_context:r,_instance:null,version:e0,get config(){return r.config},set config(c){},use(c,...d){return l.has(c)||(c&&le(c.install)?(l.add(c),c.install(u,...d)):le(c)&&(l.add(c),c(u,...d))),u},mixin(c){return r.mixins.includes(c)||r.mixins.push(c),u},component(c,d){return d?(r.components[c]=d,u):r.components[c]},directive(c,d){return d?(r.directives[c]=d,u):r.directives[c]},mount(c,d,p){if(!f){const h=u._ceVNode||ge(o,n);return h.appContext=r,p===!0?p="svg":p===!1&&(p=void 0),e(h,c,p),f=!0,u._container=c,c.__vue_app__=u,On(h.component)}},onUnmount(c){a.push(c)},unmount(){f&&(Mt(a,u._instance,16),e(null,u._container),delete u._container.__vue_app__)},provide(c,d){return r.provides[c]=d,u},runWithContext(c){const d=fs;fs=u;try{return c()}finally{fs=d}}};return u}}let fs=null;function qo(e,t){if(Ue){let s=Ue.provides;const o=Ue.parent&&Ue.parent.provides;o===s&&(s=Ue.provides=Object.create(o)),s[e]=t}}function mt(e,t,s=!1){const o=Ue||tt;if(o||fs){const n=fs?fs._context.provides:o?o.parent==null?o.vnode.appContext&&o.vnode.appContext.provides:o.parent.provides:void 0;if(n&&e in n)return n[e];if(arguments.length>1)return s&&le(t)?t.call(o&&o.proxy):t}}function yf(){return!!(Ue||tt||fs)}const Ya={},Za=()=>Object.create(Ya),Xa=e=>Object.getPrototypeOf(e)===Ya;function xf(e,t,s,o=!1){const n={},r=Za();e.propsDefaults=Object.create(null),Qa(e,t,n,r);for(const l in e.propsOptions[0])l in n||(n[l]=void 0);s?e.props=o?n:Ra(n):e.type.props?e.props=n:e.props=r,e.attrs=r}function kf(e,t,s,o){const{props:n,attrs:r,vnode:{patchFlag:l}}=e,a=pe(n),[f]=e.propsOptions;let u=!1;if((o||l>0)&&!(l&16)){if(l&8){const c=e.vnode.dynamicProps;for(let d=0;d<c.length;d++){let p=c[d];if(Mn(e.emitsOptions,p))continue;const h=t[p];if(f)if(ve(r,p))h!==r[p]&&(r[p]=h,u=!0);else{const v=gt(p);n[v]=Tr(f,a,v,h,e,!1)}else h!==r[p]&&(r[p]=h,u=!0)}}}else{Qa(e,t,n,r)&&(u=!0);let c;for(const d in a)(!t||!ve(t,d)&&((c=xs(d))===d||!ve(t,c)))&&(f?s&&(s[d]!==void 0||s[c]!==void 0)&&(n[d]=Tr(f,a,d,void 0,e,!0)):delete n[d]);if(r!==a)for(const d in r)(!t||!ve(t,d))&&(delete r[d],u=!0)}u&&Nt(e.attrs,"set","")}function Qa(e,t,s,o){const[n,r]=e.propsOptions;let l=!1,a;if(t)for(let f in t){if(ao(f))continue;const u=t[f];let c;n&&ve(n,c=gt(f))?!r||!r.includes(c)?s[c]=u:(a||(a={}))[c]=u:Mn(e.emitsOptions,f)||(!(f in o)||u!==o[f])&&(o[f]=u,l=!0)}if(r){const f=pe(s),u=a||Ce;for(let c=0;c<r.length;c++){const d=r[c];s[d]=Tr(n,f,d,u[d],e,!ve(u,d))}}return l}function Tr(e,t,s,o,n,r){const l=e[s];if(l!=null){const a=ve(l,"default");if(a&&o===void 0){const f=l.default;if(l.type!==Function&&!l.skipFactory&&le(f)){const{propsDefaults:u}=n;if(s in u)o=u[s];else{const c=Po(n);o=u[s]=f.call(null,t),c()}}else o=f;n.ce&&n.ce._setProp(s,o)}l[0]&&(r&&!a?o=!1:l[1]&&(o===""||o===xs(s))&&(o=!0))}return o}const _f=new WeakMap;function ec(e,t,s=!1){const o=s?_f:t.propsCache,n=o.get(e);if(n)return n;const r=e.props,l={},a=[];let f=!1;if(!le(e)){const c=d=>{f=!0;const[p,h]=ec(d,t,!0);Ke(l,p),h&&a.push(...h)};!s&&t.mixins.length&&t.mixins.forEach(c),e.extends&&c(e.extends),e.mixins&&e.mixins.forEach(c)}if(!r&&!f)return Se(e)&&o.set(e,Ds),Ds;if(se(r))for(let c=0;c<r.length;c++){const d=gt(r[c]);qi(d)&&(l[d]=Ce)}else if(r)for(const c in r){const d=gt(c);if(qi(d)){const p=r[c],h=l[d]=se(p)||le(p)?{type:p}:Ke({},p),v=h.type;let $=!1,y=!0;if(se(v))for(let x=0;x<v.length;++x){const m=v[x],b=le(m)&&m.name;if(b==="Boolean"){$=!0;break}else b==="String"&&(y=!1)}else $=le(v)&&v.name==="Boolean";h[0]=$,h[1]=y,($||ve(h,"default"))&&a.push(d)}}const u=[l,a];return Se(e)&&o.set(e,u),u}function qi(e){return e[0]!=="$"&&!ao(e)}const tc=e=>e[0]==="_"||e==="$stable",ai=e=>se(e)?e.map(Tt):[Tt(e)],Cf=(e,t,s)=>{if(t._n)return t;const o=Na((...n)=>ai(t(...n)),s);return o._c=!1,o},sc=(e,t,s)=>{const o=e._ctx;for(const n in e){if(tc(n))continue;const r=e[n];if(le(r))t[n]=Cf(n,r,o);else if(r!=null){const l=ai(r);t[n]=()=>l}}},oc=(e,t)=>{const s=ai(t);e.slots.default=()=>s},nc=(e,t,s)=>{for(const o in t)(s||o!=="_")&&(e[o]=t[o])},$f=(e,t,s)=>{const o=e.slots=Za();if(e.vnode.shapeFlag&32){const n=t._;n?(nc(o,t,s),s&&da(o,"_",n,!0)):sc(t,o)}else t&&oc(e,t)},Ef=(e,t,s)=>{const{vnode:o,slots:n}=e;let r=!0,l=Ce;if(o.shapeFlag&32){const a=t._;a?s&&a===1?r=!1:nc(n,t,s):(r=!t.$stable,sc(t,n)),l=t}else t&&(oc(e,t),l={default:1});if(r)for(const a in n)!tc(a)&&l[a]==null&&delete n[a]},at=Uf;function Sf(e){return Af(e)}function Af(e,t){const s=En();s.__VUE__=!0;const{insert:o,remove:n,patchProp:r,createElement:l,createText:a,createComment:f,setText:u,setElementText:c,parentNode:d,nextSibling:p,setScopeId:h=jt,insertStaticContent:v}=e,$=(g,w,k,M=null,I=null,O=null,V=void 0,N=null,U=!!w.dynamicChildren)=>{if(g===w)return;g&&!no(g,w)&&(M=B(g),Qe(g,I,O,!0),g=null),w.patchFlag===-2&&(U=!1,w.dynamicChildren=null);const{type:D,ref:te,shapeFlag:z}=w;switch(D){case Bn:y(g,w,k,M);break;case gs:x(g,w,k,M);break;case Ko:g==null&&m(w,k,M,V);break;case ie:Te(g,w,k,M,I,O,V,N,U);break;default:z&1?L(g,w,k,M,I,O,V,N,U):z&6?je(g,w,k,M,I,O,V,N,U):(z&64||z&128)&&D.process(g,w,k,M,I,O,V,N,U,Z)}te!=null&&I&&on(te,g&&g.ref,O,w||g,!w)},y=(g,w,k,M)=>{if(g==null)o(w.el=a(w.children),k,M);else{const I=w.el=g.el;w.children!==g.children&&u(I,w.children)}},x=(g,w,k,M)=>{g==null?o(w.el=f(w.children||""),k,M):w.el=g.el},m=(g,w,k,M)=>{[g.el,g.anchor]=v(g.children,w,k,M,g.el,g.anchor)},b=({el:g,anchor:w},k,M)=>{let I;for(;g&&g!==w;)I=p(g),o(g,k,M),g=I;o(w,k,M)},T=({el:g,anchor:w})=>{let k;for(;g&&g!==w;)k=p(g),n(g),g=k;n(w)},L=(g,w,k,M,I,O,V,N,U)=>{w.type==="svg"?V="svg":w.type==="math"&&(V="mathml"),g==null?J(w,k,M,I,O,V,N,U):E(g,w,I,O,V,N,U)},J=(g,w,k,M,I,O,V,N)=>{let U,D;const{props:te,shapeFlag:z,transition:Q,dirs:re}=g;if(U=g.el=l(g.type,O,te&&te.is,te),z&8?c(U,g.children):z&16&&_(g.children,U,null,M,I,ir(g,O),V,N),re&&ns(g,null,M,"created"),j(U,g,g.scopeId,V,M),te){for(const $e in te)$e!=="value"&&!ao($e)&&r(U,$e,null,te[$e],O,M);"value"in te&&r(U,"value",null,te.value,O),(D=te.onVnodeBeforeMount)&&Et(D,M,g)}re&&ns(g,null,M,"beforeMount");const fe=Pf(I,Q);fe&&Q.beforeEnter(U),o(U,w,k),((D=te&&te.onVnodeMounted)||fe||re)&&at(()=>{D&&Et(D,M,g),fe&&Q.enter(U),re&&ns(g,null,M,"mounted")},I)},j=(g,w,k,M,I)=>{if(k&&h(g,k),M)for(let O=0;O<M.length;O++)h(g,M[O]);if(I){let O=I.subTree;if(w===O||uc(O.type)&&(O.ssContent===w||O.ssFallback===w)){const V=I.vnode;j(g,V,V.scopeId,V.slotScopeIds,I.parent)}}},_=(g,w,k,M,I,O,V,N,U=0)=>{for(let D=U;D<g.length;D++){const te=g[D]=N?Gt(g[D]):Tt(g[D]);$(null,te,w,k,M,I,O,V,N)}},E=(g,w,k,M,I,O,V)=>{const N=w.el=g.el;let{patchFlag:U,dynamicChildren:D,dirs:te}=w;U|=g.patchFlag&16;const z=g.props||Ce,Q=w.props||Ce;let re;if(k&&rs(k,!1),(re=Q.onVnodeBeforeUpdate)&&Et(re,k,w,g),te&&ns(w,g,k,"beforeUpdate"),k&&rs(k,!0),(z.innerHTML&&Q.innerHTML==null||z.textContent&&Q.textContent==null)&&c(N,""),D?q(g.dynamicChildren,D,N,k,M,ir(w,I),O):V||G(g,w,N,null,k,M,ir(w,I),O,!1),U>0){if(U&16)we(N,z,Q,k,I);else if(U&2&&z.class!==Q.class&&r(N,"class",null,Q.class,I),U&4&&r(N,"style",z.style,Q.style,I),U&8){const fe=w.dynamicProps;for(let $e=0;$e<fe.length;$e++){const ke=fe[$e],it=z[ke],et=Q[ke];(et!==it||ke==="value")&&r(N,ke,it,et,I,k)}}U&1&&g.children!==w.children&&c(N,w.children)}else!V&&D==null&&we(N,z,Q,k,I);((re=Q.onVnodeUpdated)||te)&&at(()=>{re&&Et(re,k,w,g),te&&ns(w,g,k,"updated")},M)},q=(g,w,k,M,I,O,V)=>{for(let N=0;N<w.length;N++){const U=g[N],D=w[N],te=U.el&&(U.type===ie||!no(U,D)||U.shapeFlag&70)?d(U.el):k;$(U,D,te,null,M,I,O,V,!0)}},we=(g,w,k,M,I)=>{if(w!==k){if(w!==Ce)for(const O in w)!ao(O)&&!(O in k)&&r(g,O,w[O],null,I,M);for(const O in k){if(ao(O))continue;const V=k[O],N=w[O];V!==N&&O!=="value"&&r(g,O,N,V,I,M)}"value"in k&&r(g,"value",w.value,k.value,I)}},Te=(g,w,k,M,I,O,V,N,U)=>{const D=w.el=g?g.el:a(""),te=w.anchor=g?g.anchor:a("");let{patchFlag:z,dynamicChildren:Q,slotScopeIds:re}=w;re&&(N=N?N.concat(re):re),g==null?(o(D,k,M),o(te,k,M),_(w.children||[],k,te,I,O,V,N,U)):z>0&&z&64&&Q&&g.dynamicChildren?(q(g.dynamicChildren,Q,k,I,O,V,N),(w.key!=null||I&&w===I.subTree)&&rc(g,w,!0)):G(g,w,k,te,I,O,V,N,U)},je=(g,w,k,M,I,O,V,N,U)=>{w.slotScopeIds=N,g==null?w.shapeFlag&512?I.ctx.activate(w,k,M,V,U):Ot(w,k,M,I,O,V,U):xt(g,w,U)},Ot=(g,w,k,M,I,O,V)=>{const N=g.component=Wf(g,M,I);if(Ha(g)&&(N.ctx.renderer=Z),Jf(N,!1,V),N.asyncDep){if(I&&I.registerDep(N,X,V),!g.el){const U=N.subTree=ge(gs);x(null,U,w,k)}}else X(N,g,w,k,I,O,V)},xt=(g,w,k)=>{const M=w.component=g.component;if(Lf(g,w,k))if(M.asyncDep&&!M.asyncResolved){F(M,w,k);return}else M.next=w,M.update();else w.el=g.el,M.vnode=w},X=(g,w,k,M,I,O,V)=>{const N=()=>{if(g.isMounted){let{next:z,bu:Q,u:re,parent:fe,vnode:$e}=g;{const Ct=ic(g);if(Ct){z&&(z.el=$e.el,F(g,z,V)),Ct.asyncDep.then(()=>{g.isUnmounted||N()});return}}let ke=z,it;rs(g,!1),z?(z.el=$e.el,F(g,z,V)):z=$e,Q&&zo(Q),(it=z.props&&z.props.onVnodeBeforeUpdate)&&Et(it,fe,z,$e),rs(g,!0);const et=Wi(g),_t=g.subTree;g.subTree=et,$(_t,et,d(_t.el),B(_t),g,I,O),z.el=et.el,ke===null&&Ff(g,et.el),re&&at(re,I),(it=z.props&&z.props.onVnodeUpdated)&&at(()=>Et(it,fe,z,$e),I)}else{let z;const{el:Q,props:re}=w,{bm:fe,m:$e,parent:ke,root:it,type:et}=g,_t=fo(w);rs(g,!1),fe&&zo(fe),!_t&&(z=re&&re.onVnodeBeforeMount)&&Et(z,ke,w),rs(g,!0);{it.ce&&it.ce._injectChildStyle(et);const Ct=g.subTree=Wi(g);$(null,Ct,k,M,g,I,O),w.el=Ct.el}if($e&&at($e,I),!_t&&(z=re&&re.onVnodeMounted)){const Ct=w;at(()=>Et(z,ke,Ct),I)}(w.shapeFlag&256||ke&&fo(ke.vnode)&&ke.vnode.shapeFlag&256)&&g.a&&at(g.a,I),g.isMounted=!0,w=k=M=null}};g.scope.on();const U=g.effect=new ba(N);g.scope.off();const D=g.update=U.run.bind(U),te=g.job=U.runIfDirty.bind(U);te.i=g,te.id=g.uid,U.scheduler=()=>ri(te),rs(g,!0),D()},F=(g,w,k)=>{w.component=g;const M=g.vnode.props;g.vnode=w,g.next=null,kf(g,w.props,M,k),Ef(g,w.children,k),ts(),Fi(g),ss()},G=(g,w,k,M,I,O,V,N,U=!1)=>{const D=g&&g.children,te=g?g.shapeFlag:0,z=w.children,{patchFlag:Q,shapeFlag:re}=w;if(Q>0){if(Q&128){rt(D,z,k,M,I,O,V,N,U);return}else if(Q&256){Fe(D,z,k,M,I,O,V,N,U);return}}re&8?(te&16&&pt(D,I,O),z!==D&&c(k,z)):te&16?re&16?rt(D,z,k,M,I,O,V,N,U):pt(D,I,O,!0):(te&8&&c(k,""),re&16&&_(z,k,M,I,O,V,N,U))},Fe=(g,w,k,M,I,O,V,N,U)=>{g=g||Ds,w=w||Ds;const D=g.length,te=w.length,z=Math.min(D,te);let Q;for(Q=0;Q<z;Q++){const re=w[Q]=U?Gt(w[Q]):Tt(w[Q]);$(g[Q],re,k,null,I,O,V,N,U)}D>te?pt(g,I,O,!0,!1,z):_(w,k,M,I,O,V,N,U,z)},rt=(g,w,k,M,I,O,V,N,U)=>{let D=0;const te=w.length;let z=g.length-1,Q=te-1;for(;D<=z&&D<=Q;){const re=g[D],fe=w[D]=U?Gt(w[D]):Tt(w[D]);if(no(re,fe))$(re,fe,k,null,I,O,V,N,U);else break;D++}for(;D<=z&&D<=Q;){const re=g[z],fe=w[Q]=U?Gt(w[Q]):Tt(w[Q]);if(no(re,fe))$(re,fe,k,null,I,O,V,N,U);else break;z--,Q--}if(D>z){if(D<=Q){const re=Q+1,fe=re<te?w[re].el:M;for(;D<=Q;)$(null,w[D]=U?Gt(w[D]):Tt(w[D]),k,fe,I,O,V,N,U),D++}}else if(D>Q)for(;D<=z;)Qe(g[D],I,O,!0),D++;else{const re=D,fe=D,$e=new Map;for(D=fe;D<=Q;D++){const lt=w[D]=U?Gt(w[D]):Tt(w[D]);lt.key!=null&&$e.set(lt.key,D)}let ke,it=0;const et=Q-fe+1;let _t=!1,Ct=0;const so=new Array(et);for(D=0;D<et;D++)so[D]=0;for(D=re;D<=z;D++){const lt=g[D];if(it>=et){Qe(lt,I,O,!0);continue}let $t;if(lt.key!=null)$t=$e.get(lt.key);else for(ke=fe;ke<=Q;ke++)if(so[ke-fe]===0&&no(lt,w[ke])){$t=ke;break}$t===void 0?Qe(lt,I,O,!0):(so[$t-fe]=D+1,$t>=Ct?Ct=$t:_t=!0,$(lt,w[$t],k,null,I,O,V,N,U),it++)}const Mi=_t?Tf(so):Ds;for(ke=Mi.length-1,D=et-1;D>=0;D--){const lt=fe+D,$t=w[lt],Bi=lt+1<te?w[lt+1].el:M;so[D]===0?$(null,$t,k,Bi,I,O,V,N,U):_t&&(ke<0||D!==Mi[ke]?kt($t,k,Bi,2):ke--)}}},kt=(g,w,k,M,I=null)=>{const{el:O,type:V,transition:N,children:U,shapeFlag:D}=g;if(D&6){kt(g.component.subTree,w,k,M);return}if(D&128){g.suspense.move(w,k,M);return}if(D&64){V.move(g,w,k,Z);return}if(V===ie){o(O,w,k);for(let z=0;z<U.length;z++)kt(U[z],w,k,M);o(g.anchor,w,k);return}if(V===Ko){b(g,w,k);return}if(M!==2&&D&1&&N)if(M===0)N.beforeEnter(O),o(O,w,k),at(()=>N.enter(O),I);else{const{leave:z,delayLeave:Q,afterLeave:re}=N,fe=()=>o(O,w,k),$e=()=>{z(O,()=>{fe(),re&&re()})};Q?Q(O,fe,$e):$e()}else o(O,w,k)},Qe=(g,w,k,M=!1,I=!1)=>{const{type:O,props:V,ref:N,children:U,dynamicChildren:D,shapeFlag:te,patchFlag:z,dirs:Q,cacheIndex:re}=g;if(z===-2&&(I=!1),N!=null&&on(N,null,k,g,!0),re!=null&&(w.renderCache[re]=void 0),te&256){w.ctx.deactivate(g);return}const fe=te&1&&Q,$e=!fo(g);let ke;if($e&&(ke=V&&V.onVnodeBeforeUnmount)&&Et(ke,w,g),te&6)Fo(g.component,k,M);else{if(te&128){g.suspense.unmount(k,M);return}fe&&ns(g,null,w,"beforeUnmount"),te&64?g.type.remove(g,w,k,Z,M):D&&!D.hasOnce&&(O!==ie||z>0&&z&64)?pt(D,w,k,!1,!0):(O===ie&&z&384||!I&&te&16)&&pt(U,w,k),M&&Cs(g)}($e&&(ke=V&&V.onVnodeUnmounted)||fe)&&at(()=>{ke&&Et(ke,w,g),fe&&ns(g,null,w,"unmounted")},k)},Cs=g=>{const{type:w,el:k,anchor:M,transition:I}=g;if(w===ie){$s(k,M);return}if(w===Ko){T(g);return}const O=()=>{n(k),I&&!I.persisted&&I.afterLeave&&I.afterLeave()};if(g.shapeFlag&1&&I&&!I.persisted){const{leave:V,delayLeave:N}=I,U=()=>V(k,O);N?N(g.el,O,U):U()}else O()},$s=(g,w)=>{let k;for(;g!==w;)k=p(g),n(g),g=k;n(w)},Fo=(g,w,k)=>{const{bum:M,scope:I,job:O,subTree:V,um:N,m:U,a:D}=g;Ki(U),Ki(D),M&&zo(M),I.stop(),O&&(O.flags|=8,Qe(V,g,w,k)),N&&at(N,w),at(()=>{g.isUnmounted=!0},w),w&&w.pendingBranch&&!w.isUnmounted&&g.asyncDep&&!g.asyncResolved&&g.suspenseId===w.pendingId&&(w.deps--,w.deps===0&&w.resolve())},pt=(g,w,k,M=!1,I=!1,O=0)=>{for(let V=O;V<g.length;V++)Qe(g[V],w,k,M,I)},B=g=>{if(g.shapeFlag&6)return B(g.component.subTree);if(g.shapeFlag&128)return g.suspense.next();const w=p(g.anchor||g.el),k=w&&w[Yd];return k?p(k):w};let K=!1;const H=(g,w,k)=>{g==null?w._vnode&&Qe(w._vnode,null,null,!0):$(w._vnode||null,g,w,null,null,null,k),w._vnode=g,K||(K=!0,Fi(),La(),K=!1)},Z={p:$,um:Qe,m:kt,r:Cs,mt:Ot,mc:_,pc:G,pbc:q,n:B,o:e};return{render:H,hydrate:void 0,createApp:vf(H)}}function ir({type:e,props:t},s){return s==="svg"&&e==="foreignObject"||s==="mathml"&&e==="annotation-xml"&&t&&t.encoding&&t.encoding.includes("html")?void 0:s}function rs({effect:e,job:t},s){s?(e.flags|=32,t.flags|=4):(e.flags&=-33,t.flags&=-5)}function Pf(e,t){return(!e||e&&!e.pendingBranch)&&t&&!t.persisted}function rc(e,t,s=!1){const o=e.children,n=t.children;if(se(o)&&se(n))for(let r=0;r<o.length;r++){const l=o[r];let a=n[r];a.shapeFlag&1&&!a.dynamicChildren&&((a.patchFlag<=0||a.patchFlag===32)&&(a=n[r]=Gt(n[r]),a.el=l.el),!s&&a.patchFlag!==-2&&rc(l,a)),a.type===Bn&&(a.el=l.el)}}function Tf(e){const t=e.slice(),s=[0];let o,n,r,l,a;const f=e.length;for(o=0;o<f;o++){const u=e[o];if(u!==0){if(n=s[s.length-1],e[n]<u){t[o]=n,s.push(o);continue}for(r=0,l=s.length-1;r<l;)a=r+l>>1,e[s[a]]<u?r=a+1:l=a;u<e[s[r]]&&(r>0&&(t[o]=s[r-1]),s[r]=o)}}for(r=s.length,l=s[r-1];r-- >0;)s[r]=l,l=t[l];return s}function ic(e){const t=e.subTree.component;if(t)return t.asyncDep&&!t.asyncResolved?t:ic(t)}function Ki(e){if(e)for(let t=0;t<e.length;t++)e[t].flags|=8}const jf=Symbol.for("v-scx"),Rf=()=>mt(jf);function De(e,t,s){return lc(e,t,s)}function lc(e,t,s=Ce){const{immediate:o,deep:n,flush:r,once:l}=s,a=Ke({},s),f=t&&o||!t&&r!=="post";let u;if(ko){if(r==="sync"){const h=Rf();u=h.__watcherHandles||(h.__watcherHandles=[])}else if(!f){const h=()=>{};return h.stop=jt,h.resume=jt,h.pause=jt,h}}const c=Ue;a.call=(h,v,$)=>Mt(h,c,v,$);let d=!1;r==="post"?a.scheduler=h=>{at(h,c&&c.suspense)}:r!=="sync"&&(d=!0,a.scheduler=(h,v)=>{v?h():ri(h)}),a.augmentJob=h=>{t&&(h.flags|=4),d&&(h.flags|=2,c&&(h.id=c.uid,h.i=c))};const p=Kd(e,t,a);return ko&&(u?u.push(p):f&&p()),p}function Mf(e,t,s){const o=this.proxy,n=Ie(e)?e.includes(".")?ac(o,e):()=>o[e]:e.bind(o,o);let r;le(t)?r=t:(r=t.handler,s=t);const l=Po(this),a=lc(n,r.bind(o),s);return l(),a}function ac(e,t){const s=t.split(".");return()=>{let o=e;for(let n=0;n<s.length&&o;n++)o=o[s[n]];return o}}const Bf=(e,t)=>t==="modelValue"||t==="model-value"?e.modelModifiers:e[`${t}Modifiers`]||e[`${gt(t)}Modifiers`]||e[`${xs(t)}Modifiers`];function Of(e,t,...s){if(e.isUnmounted)return;const o=e.vnode.props||Ce;let n=s;const r=t.startsWith("update:"),l=r&&Bf(o,t.slice(7));l&&(l.trim&&(n=s.map(c=>Ie(c)?c.trim():c)),l.number&&(n=s.map(Xo)));let a,f=o[a=er(t)]||o[a=er(gt(t))];!f&&r&&(f=o[a=er(xs(t))]),f&&Mt(f,e,6,n);const u=o[a+"Once"];if(u){if(!e.emitted)e.emitted={};else if(e.emitted[a])return;e.emitted[a]=!0,Mt(u,e,6,n)}}function cc(e,t,s=!1){const o=t.emitsCache,n=o.get(e);if(n!==void 0)return n;const r=e.emits;let l={},a=!1;if(!le(e)){const f=u=>{const c=cc(u,t,!0);c&&(a=!0,Ke(l,c))};!s&&t.mixins.length&&t.mixins.forEach(f),e.extends&&f(e.extends),e.mixins&&e.mixins.forEach(f)}return!r&&!a?(Se(e)&&o.set(e,null),null):(se(r)?r.forEach(f=>l[f]=null):Ke(l,r),Se(e)&&o.set(e,l),l)}function Mn(e,t){return!e||!kn(t)?!1:(t=t.slice(2).replace(/Once$/,""),ve(e,t[0].toLowerCase()+t.slice(1))||ve(e,xs(t))||ve(e,t))}function Wi(e){const{type:t,vnode:s,proxy:o,withProxy:n,propsOptions:[r],slots:l,attrs:a,emit:f,render:u,renderCache:c,props:d,data:p,setupState:h,ctx:v,inheritAttrs:$}=e,y=sn(e);let x,m;try{if(s.shapeFlag&4){const T=n||o,L=T;x=Tt(u.call(L,T,c,d,h,p,v)),m=a}else{const T=t;x=Tt(T.length>1?T(d,{attrs:a,slots:l,emit:f}):T(d,null)),m=t.props?a:If(a)}}catch(T){ho.length=0,Tn(T,e,1),x=ge(gs)}let b=x;if(m&&$!==!1){const T=Object.keys(m),{shapeFlag:L}=b;T.length&&L&7&&(r&&T.some(Jr)&&(m=Df(m,r)),b=qs(b,m,!1,!0))}return s.dirs&&(b=qs(b,null,!1,!0),b.dirs=b.dirs?b.dirs.concat(s.dirs):s.dirs),s.transition&&ii(b,s.transition),x=b,sn(y),x}const If=e=>{let t;for(const s in e)(s==="class"||s==="style"||kn(s))&&((t||(t={}))[s]=e[s]);return t},Df=(e,t)=>{const s={};for(const o in e)(!Jr(o)||!(o.slice(9)in t))&&(s[o]=e[o]);return s};function Lf(e,t,s){const{props:o,children:n,component:r}=e,{props:l,children:a,patchFlag:f}=t,u=r.emitsOptions;if(t.dirs||t.transition)return!0;if(s&&f>=0){if(f&1024)return!0;if(f&16)return o?Ji(o,l,u):!!l;if(f&8){const c=t.dynamicProps;for(let d=0;d<c.length;d++){const p=c[d];if(l[p]!==o[p]&&!Mn(u,p))return!0}}}else return(n||a)&&(!a||!a.$stable)?!0:o===l?!1:o?l?Ji(o,l,u):!0:!!l;return!1}function Ji(e,t,s){const o=Object.keys(t);if(o.length!==Object.keys(e).length)return!0;for(let n=0;n<o.length;n++){const r=o[n];if(t[r]!==e[r]&&!Mn(s,r))return!0}return!1}function Ff({vnode:e,parent:t},s){for(;t;){const o=t.subTree;if(o.suspense&&o.suspense.activeBranch===e&&(o.el=e.el),o===e)(e=t.vnode).el=s,t=t.parent;else break}}const uc=e=>e.__isSuspense;function Uf(e,t){t&&t.pendingBranch?se(e)?t.effects.push(...e):t.effects.push(e):Gd(e)}const ie=Symbol.for("v-fgt"),Bn=Symbol.for("v-txt"),gs=Symbol.for("v-cmt"),Ko=Symbol.for("v-stc"),ho=[];let ct=null;function C(e=!1){ho.push(ct=e?null:[])}function Nf(){ho.pop(),ct=ho[ho.length-1]||null}let xo=1;function Gi(e,t=!1){xo+=e,e<0&&ct&&t&&(ct.hasOnce=!0)}function dc(e){return e.dynamicChildren=xo>0?ct||Ds:null,Nf(),xo>0&&ct&&ct.push(e),e}function S(e,t,s,o,n,r){return dc(i(e,t,s,o,n,r,!0))}function zs(e,t,s,o,n){return dc(ge(e,t,s,o,n,!0))}function rn(e){return e?e.__v_isVNode===!0:!1}function no(e,t){return e.type===t.type&&e.key===t.key}const fc=({key:e})=>e??null,Wo=({ref:e,ref_key:t,ref_for:s})=>(typeof e=="number"&&(e=""+e),e!=null?Ie(e)||Me(e)||le(e)?{i:tt,r:e,k:t,f:!!s}:e:null);function i(e,t=null,s=null,o=0,n=null,r=e===ie?0:1,l=!1,a=!1){const f={__v_isVNode:!0,__v_skip:!0,type:e,props:t,key:t&&fc(t),ref:t&&Wo(t),scopeId:Ua,slotScopeIds:null,children:s,component:null,suspense:null,ssContent:null,ssFallback:null,dirs:null,transition:null,el:null,anchor:null,target:null,targetStart:null,targetAnchor:null,staticCount:0,shapeFlag:r,patchFlag:o,dynamicProps:n,dynamicChildren:null,appContext:null,ctx:tt};return a?(ci(f,s),r&128&&e.normalize(f)):s&&(f.shapeFlag|=Ie(s)?8:16),xo>0&&!l&&ct&&(f.patchFlag>0||r&6)&&f.patchFlag!==32&&ct.push(f),f}const ge=Vf;function Vf(e,t=null,s=null,o=0,n=null,r=!1){if((!e||e===uf)&&(e=gs),rn(e)){const a=qs(e,t,!0);return s&&ci(a,s),xo>0&&!r&&ct&&(a.shapeFlag&6?ct[ct.indexOf(e)]=a:ct.push(a)),a.patchFlag=-2,a}if(Qf(e)&&(e=e.__vccOpts),t){t=Hf(t);let{class:a,style:f}=t;a&&!Ie(a)&&(t.class=ne(a)),Se(f)&&(oi(f)&&!se(f)&&(f=Ke({},f)),t.style=Sn(f))}const l=Ie(e)?1:uc(e)?128:Zd(e)?64:Se(e)?4:le(e)?2:0;return i(e,t,s,o,n,l,r,!0)}function Hf(e){return e?oi(e)||Xa(e)?Ke({},e):e:null}function qs(e,t,s=!1,o=!1){const{props:n,ref:r,patchFlag:l,children:a,transition:f}=e,u=t?zf(n||{},t):n,c={__v_isVNode:!0,__v_skip:!0,type:e.type,props:u,key:u&&fc(u),ref:t&&t.ref?s&&r?se(r)?r.concat(Wo(t)):[r,Wo(t)]:Wo(t):r,scopeId:e.scopeId,slotScopeIds:e.slotScopeIds,children:a,target:e.target,targetStart:e.targetStart,targetAnchor:e.targetAnchor,staticCount:e.staticCount,shapeFlag:e.shapeFlag,patchFlag:t&&e.type!==ie?l===-1?16:l|16:l,dynamicProps:e.dynamicProps,dynamicChildren:e.dynamicChildren,appContext:e.appContext,dirs:e.dirs,transition:f,component:e.component,suspense:e.suspense,ssContent:e.ssContent&&qs(e.ssContent),ssFallback:e.ssFallback&&qs(e.ssFallback),el:e.el,anchor:e.anchor,ctx:e.ctx,ce:e.ce};return f&&o&&ii(c,f.clone(c)),c}function ce(e=" ",t=0){return ge(Bn,null,e,t)}function ws(e,t){const s=ge(Ko,null,e);return s.staticCount=t,s}function de(e="",t=!1){return t?(C(),zs(gs,null,e)):ge(gs,null,e)}function Tt(e){return e==null||typeof e=="boolean"?ge(gs):se(e)?ge(ie,null,e.slice()):rn(e)?Gt(e):ge(Bn,null,String(e))}function Gt(e){return e.el===null&&e.patchFlag!==-1||e.memo?e:qs(e)}function ci(e,t){let s=0;const{shapeFlag:o}=e;if(t==null)t=null;else if(se(t))s=16;else if(typeof t=="object")if(o&65){const n=t.default;n&&(n._c&&(n._d=!1),ci(e,n()),n._c&&(n._d=!0));return}else{s=32;const n=t._;!n&&!Xa(t)?t._ctx=tt:n===3&&tt&&(tt.slots._===1?t._=1:(t._=2,e.patchFlag|=1024))}else le(t)?(t={default:t,_ctx:tt},s=32):(t=String(t),o&64?(s=16,t=[ce(t)]):s=8);e.children=t,e.shapeFlag|=s}function zf(...e){const t={};for(let s=0;s<e.length;s++){const o=e[s];for(const n in o)if(n==="class")t.class!==o.class&&(t.class=ne([t.class,o.class]));else if(n==="style")t.style=Sn([t.style,o.style]);else if(kn(n)){const r=t[n],l=o[n];l&&r!==l&&!(se(r)&&r.includes(l))&&(t[n]=r?[].concat(r,l):l)}else n!==""&&(t[n]=o[n])}return t}function Et(e,t,s,o=null){Mt(e,t,7,[s,o])}const qf=Ga();let Kf=0;function Wf(e,t,s){const o=e.type,n=(t?t.appContext:e.appContext)||qf,r={uid:Kf++,vnode:e,type:o,parent:t,appContext:n,root:null,next:null,subTree:null,effect:null,update:null,job:null,scope:new ma(!0),render:null,proxy:null,exposed:null,exposeProxy:null,withProxy:null,provides:t?t.provides:Object.create(n.provides),ids:t?t.ids:["",0,0],accessCache:null,renderCache:[],components:null,directives:null,propsOptions:ec(o,n),emitsOptions:cc(o,n),emit:null,emitted:null,propsDefaults:Ce,inheritAttrs:o.inheritAttrs,ctx:Ce,data:Ce,props:Ce,attrs:Ce,slots:Ce,refs:Ce,setupState:Ce,setupContext:null,suspense:s,suspenseId:s?s.pendingId:0,asyncDep:null,asyncResolved:!1,isMounted:!1,isUnmounted:!1,isDeactivated:!1,bc:null,c:null,bm:null,m:null,bu:null,u:null,um:null,bum:null,da:null,a:null,rtg:null,rtc:null,ec:null,sp:null};return r.ctx={_:r},r.root=t?t.root:r,r.emit=Of.bind(null,r),e.ce&&e.ce(r),r}let Ue=null,ln,jr;{const e=En(),t=(s,o)=>{let n;return(n=e[s])||(n=e[s]=[]),n.push(o),r=>{n.length>1?n.forEach(l=>l(r)):n[0](r)}};ln=t("__VUE_INSTANCE_SETTERS__",s=>Ue=s),jr=t("__VUE_SSR_SETTERS__",s=>ko=s)}const Po=e=>{const t=Ue;return ln(e),e.scope.on(),()=>{e.scope.off(),ln(t)}},Yi=()=>{Ue&&Ue.scope.off(),ln(null)};function pc(e){return e.vnode.shapeFlag&4}let ko=!1;function Jf(e,t=!1,s=!1){t&&jr(t);const{props:o,children:n}=e.vnode,r=pc(e);xf(e,o,r,t),$f(e,n,s);const l=r?Gf(e,t):void 0;return t&&jr(!1),l}function Gf(e,t){const s=e.type;e.accessCache=Object.create(null),e.proxy=new Proxy(e.ctx,ff);const{setup:o}=s;if(o){ts();const n=e.setupContext=o.length>1?Zf(e):null,r=Po(e),l=Ao(o,e,0,[e.props,n]),a=aa(l);if(ss(),r(),(a||e.sp)&&!fo(e)&&Va(e),a){if(l.then(Yi,Yi),t)return l.then(f=>{Zi(e,f)}).catch(f=>{Tn(f,e,0)});e.asyncDep=l}else Zi(e,l)}else hc(e)}function Zi(e,t,s){le(t)?e.type.__ssrInlineRender?e.ssrRender=t:e.render=t:Se(t)&&(e.setupState=Oa(t)),hc(e)}function hc(e,t,s){const o=e.type;e.render||(e.render=o.render||jt);{const n=Po(e);ts();try{pf(e)}finally{ss(),n()}}}const Yf={get(e,t){return He(e,"get",""),e[t]}};function Zf(e){const t=s=>{e.exposed=s||{}};return{attrs:new Proxy(e.attrs,Yf),slots:e.slots,emit:e.emit,expose:t}}function On(e){return e.exposed?e.exposeProxy||(e.exposeProxy=new Proxy(Oa(ni(e.exposed)),{get(t,s){if(s in t)return t[s];if(s in po)return po[s](e)},has(t,s){return s in t||s in po}})):e.proxy}function Xf(e,t=!0){return le(e)?e.displayName||e.name:e.name||t&&e.__name}function Qf(e){return le(e)&&"__vccOpts"in e}const Y=(e,t)=>zd(e,t,ko);function mc(e,t,s){const o=arguments.length;return o===2?Se(t)&&!se(t)?rn(t)?ge(e,null,[t]):ge(e,t):ge(e,null,t):(o>3?s=Array.prototype.slice.call(arguments,2):o===3&&rn(s)&&(s=[s]),ge(e,t,s))}const e0="3.5.13";/**
* @vue/runtime-dom v3.5.13
* (c) 2018-present Yuxi (Evan) You and Vue contributors
* @license MIT
**/let Rr;const Xi=typeof window<"u"&&window.trustedTypes;if(Xi)try{Rr=Xi.createPolicy("vue",{createHTML:e=>e})}catch{}const gc=Rr?e=>Rr.createHTML(e):e=>e,t0="http://www.w3.org/2000/svg",s0="http://www.w3.org/1998/Math/MathML",Ft=typeof document<"u"?document:null,Qi=Ft&&Ft.createElement("template"),o0={insert:(e,t,s)=>{t.insertBefore(e,s||null)},remove:e=>{const t=e.parentNode;t&&t.removeChild(e)},createElement:(e,t,s,o)=>{const n=t==="svg"?Ft.createElementNS(t0,e):t==="mathml"?Ft.createElementNS(s0,e):s?Ft.createElement(e,{is:s}):Ft.createElement(e);return e==="select"&&o&&o.multiple!=null&&n.setAttribute("multiple",o.multiple),n},createText:e=>Ft.createTextNode(e),createComment:e=>Ft.createComment(e),setText:(e,t)=>{e.nodeValue=t},setElementText:(e,t)=>{e.textContent=t},parentNode:e=>e.parentNode,nextSibling:e=>e.nextSibling,querySelector:e=>Ft.querySelector(e),setScopeId(e,t){e.setAttribute(t,"")},insertStaticContent(e,t,s,o,n,r){const l=s?s.previousSibling:t.lastChild;if(n&&(n===r||n.nextSibling))for(;t.insertBefore(n.cloneNode(!0),s),!(n===r||!(n=n.nextSibling)););else{Qi.innerHTML=gc(o==="svg"?`<svg>${e}</svg>`:o==="mathml"?`<math>${e}</math>`:e);const a=Qi.content;if(o==="svg"||o==="mathml"){const f=a.firstChild;for(;f.firstChild;)a.appendChild(f.firstChild);a.removeChild(f)}t.insertBefore(a,s)}return[l?l.nextSibling:t.firstChild,s?s.previousSibling:t.lastChild]}},n0=Symbol("_vtc");function r0(e,t,s){const o=e[n0];o&&(t=(t?[t,...o]:[...o]).join(" ")),t==null?e.removeAttribute("class"):s?e.setAttribute("class",t):e.className=t}const el=Symbol("_vod"),i0=Symbol("_vsh"),l0=Symbol(""),a0=/(^|;)\s*display\s*:/;function c0(e,t,s){const o=e.style,n=Ie(s);let r=!1;if(s&&!n){if(t)if(Ie(t))for(const l of t.split(";")){const a=l.slice(0,l.indexOf(":")).trim();s[a]==null&&Jo(o,a,"")}else for(const l in t)s[l]==null&&Jo(o,l,"");for(const l in s)l==="display"&&(r=!0),Jo(o,l,s[l])}else if(n){if(t!==s){const l=o[l0];l&&(s+=";"+l),o.cssText=s,r=a0.test(s)}}else t&&e.removeAttribute("style");el in e&&(e[el]=r?o.display:"",e[i0]&&(o.display="none"))}const tl=/\s*!important$/;function Jo(e,t,s){if(se(s))s.forEach(o=>Jo(e,t,o));else if(s==null&&(s=""),t.startsWith("--"))e.setProperty(t,s);else{const o=u0(e,t);tl.test(s)?e.setProperty(xs(o),s.replace(tl,""),"important"):e[o]=s}}const sl=["Webkit","Moz","ms"],lr={};function u0(e,t){const s=lr[t];if(s)return s;let o=gt(t);if(o!=="filter"&&o in e)return lr[t]=o;o=$n(o);for(let n=0;n<sl.length;n++){const r=sl[n]+o;if(r in e)return lr[t]=r}return t}const ol="http://www.w3.org/1999/xlink";function nl(e,t,s,o,n,r=hd(t)){o&&t.startsWith("xlink:")?s==null?e.removeAttributeNS(ol,t.slice(6,t.length)):e.setAttributeNS(ol,t,s):s==null||r&&!fa(s)?e.removeAttribute(t):e.setAttribute(t,r?"":Rt(s)?String(s):s)}function rl(e,t,s,o,n){if(t==="innerHTML"||t==="textContent"){s!=null&&(e[t]=t==="innerHTML"?gc(s):s);return}const r=e.tagName;if(t==="value"&&r!=="PROGRESS"&&!r.includes("-")){const a=r==="OPTION"?e.getAttribute("value")||"":e.value,f=s==null?e.type==="checkbox"?"on":"":String(s);(a!==f||!("_value"in e))&&(e.value=f),s==null&&e.removeAttribute(t),e._value=s;return}let l=!1;if(s===""||s==null){const a=typeof e[t];a==="boolean"?s=fa(s):s==null&&a==="string"?(s="",l=!0):a==="number"&&(s=0,l=!0)}try{e[t]=s}catch{}l&&e.removeAttribute(n||t)}function as(e,t,s,o){e.addEventListener(t,s,o)}function d0(e,t,s,o){e.removeEventListener(t,s,o)}const il=Symbol("_vei");function f0(e,t,s,o,n=null){const r=e[il]||(e[il]={}),l=r[t];if(o&&l)l.value=o;else{const[a,f]=p0(t);if(o){const u=r[t]=g0(o,n);as(e,a,u,f)}else l&&(d0(e,a,l,f),r[t]=void 0)}}const ll=/(?:Once|Passive|Capture)$/;function p0(e){let t;if(ll.test(e)){t={};let o;for(;o=e.match(ll);)e=e.slice(0,e.length-o[0].length),t[o[0].toLowerCase()]=!0}return[e[2]===":"?e.slice(3):xs(e.slice(2)),t]}let ar=0;const h0=Promise.resolve(),m0=()=>ar||(h0.then(()=>ar=0),ar=Date.now());function g0(e,t){const s=o=>{if(!o._vts)o._vts=Date.now();else if(o._vts<=s.attached)return;Mt(w0(o,s.value),t,5,[o])};return s.value=e,s.attached=m0(),s}function w0(e,t){if(se(t)){const s=e.stopImmediatePropagation;return e.stopImmediatePropagation=()=>{s.call(e),e._stopped=!0},t.map(o=>n=>!n._stopped&&o&&o(n))}else return t}const al=e=>e.charCodeAt(0)===111&&e.charCodeAt(1)===110&&e.charCodeAt(2)>96&&e.charCodeAt(2)<123,b0=(e,t,s,o,n,r)=>{const l=n==="svg";t==="class"?r0(e,o,l):t==="style"?c0(e,s,o):kn(t)?Jr(t)||f0(e,t,s,o,r):(t[0]==="."?(t=t.slice(1),!0):t[0]==="^"?(t=t.slice(1),!1):v0(e,t,o,l))?(rl(e,t,o),!e.tagName.includes("-")&&(t==="value"||t==="checked"||t==="selected")&&nl(e,t,o,l,r,t!=="value")):e._isVueCE&&(/[A-Z]/.test(t)||!Ie(o))?rl(e,gt(t),o,r,t):(t==="true-value"?e._trueValue=o:t==="false-value"&&(e._falseValue=o),nl(e,t,o,l))};function v0(e,t,s,o){if(o)return!!(t==="innerHTML"||t==="textContent"||t in e&&al(t)&&le(s));if(t==="spellcheck"||t==="draggable"||t==="translate"||t==="form"||t==="list"&&e.tagName==="INPUT"||t==="type"&&e.tagName==="TEXTAREA")return!1;if(t==="width"||t==="height"){const n=e.tagName;if(n==="IMG"||n==="VIDEO"||n==="CANVAS"||n==="SOURCE")return!1}return al(t)&&Ie(s)?!1:t in e}const an=e=>{const t=e.props["onUpdate:modelValue"]||!1;return se(t)?s=>zo(t,s):t};function y0(e){e.target.composing=!0}function cl(e){const t=e.target;t.composing&&(t.composing=!1,t.dispatchEvent(new Event("input")))}const Ns=Symbol("_assign"),ye={created(e,{modifiers:{lazy:t,trim:s,number:o}},n){e[Ns]=an(n);const r=o||n.props&&n.props.type==="number";as(e,t?"change":"input",l=>{if(l.target.composing)return;let a=e.value;s&&(a=a.trim()),r&&(a=Xo(a)),e[Ns](a)}),s&&as(e,"change",()=>{e.value=e.value.trim()}),t||(as(e,"compositionstart",y0),as(e,"compositionend",cl),as(e,"change",cl))},mounted(e,{value:t}){e.value=t??""},beforeUpdate(e,{value:t,oldValue:s,modifiers:{lazy:o,trim:n,number:r}},l){if(e[Ns]=an(l),e.composing)return;const a=(r||e.type==="number")&&!/^0\d/.test(e.value)?Xo(e.value):e.value,f=t??"";a!==f&&(document.activeElement===e&&e.type!=="range"&&(o&&t===s||n&&e.value.trim()===f)||(e.value=f))}},Ne={deep:!0,created(e,{value:t,modifiers:{number:s}},o){const n=_n(t);as(e,"change",()=>{const r=Array.prototype.filter.call(e.options,l=>l.selected).map(l=>s?Xo(cn(l)):cn(l));e[Ns](e.multiple?n?new Set(r):r:r[0]),e._assigning=!0,jn(()=>{e._assigning=!1})}),e[Ns]=an(o)},mounted(e,{value:t}){ul(e,t)},beforeUpdate(e,t,s){e[Ns]=an(s)},updated(e,{value:t}){e._assigning||ul(e,t)}};function ul(e,t){const s=e.multiple,o=se(t);if(!(s&&!o&&!_n(t))){for(let n=0,r=e.options.length;n<r;n++){const l=e.options[n],a=cn(l);if(s)if(o){const f=typeof a;f==="string"||f==="number"?l.selected=t.some(u=>String(u)===String(a)):l.selected=gd(t,a)>-1}else l.selected=t.has(a);else if(An(cn(l),t)){e.selectedIndex!==n&&(e.selectedIndex=n);return}}!s&&e.selectedIndex!==-1&&(e.selectedIndex=-1)}}function cn(e){return"_value"in e?e._value:e.value}const x0=["ctrl","shift","alt","meta"],k0={stop:e=>e.stopPropagation(),prevent:e=>e.preventDefault(),self:e=>e.target!==e.currentTarget,ctrl:e=>!e.ctrlKey,shift:e=>!e.shiftKey,alt:e=>!e.altKey,meta:e=>!e.metaKey,left:e=>"button"in e&&e.button!==0,middle:e=>"button"in e&&e.button!==1,right:e=>"button"in e&&e.button!==2,exact:(e,t)=>x0.some(s=>e[`${s}Key`]&&!t.includes(s))},zt=(e,t)=>{const s=e._withMods||(e._withMods={}),o=t.join(".");return s[o]||(s[o]=(n,...r)=>{for(let l=0;l<t.length;l++){const a=k0[t[l]];if(a&&a(n,t))return}return e(n,...r)})},_0=Ke({patchProp:b0},o0);let dl;function C0(){return dl||(dl=Sf(_0))}const $0=(...e)=>{const t=C0().createApp(...e),{mount:s}=t;return t.mount=o=>{const n=S0(o);if(!n)return;const r=t._component;!le(r)&&!r.render&&!r.template&&(r.template=n.innerHTML),n.nodeType===1&&(n.textContent="");const l=s(n,!1,E0(n));return n instanceof Element&&(n.removeAttribute("v-cloak"),n.setAttribute("data-v-app","")),l},t};function E0(e){if(e instanceof SVGElement)return"svg";if(typeof MathMLElement=="function"&&e instanceof MathMLElement)return"mathml"}function S0(e){return Ie(e)?document.querySelector(e):e}/*!
 * pinia v3.0.2
 * (c) 2025 Eduardo San Martin Morote
 * @license MIT
 */let wc;const In=e=>wc=e,bc=Symbol();function Mr(e){return e&&typeof e=="object"&&Object.prototype.toString.call(e)==="[object Object]"&&typeof e.toJSON!="function"}var mo;(function(e){e.direct="direct",e.patchObject="patch object",e.patchFunction="patch function"})(mo||(mo={}));function A0(){const e=ga(!0),t=e.run(()=>W({}));let s=[],o=[];const n=ni({install(r){In(n),n._a=r,r.provide(bc,n),r.config.globalProperties.$pinia=n,o.forEach(l=>s.push(l)),o=[]},use(r){return this._a?s.push(r):o.push(r),this},_p:s,_a:null,_e:e,_s:new Map,state:t});return n}const vc=()=>{};function fl(e,t,s,o=vc){e.push(t);const n=()=>{const r=e.indexOf(t);r>-1&&(e.splice(r,1),o())};return!s&&wa()&&wd(n),n}function Ss(e,...t){e.slice().forEach(s=>{s(...t)})}const P0=e=>e(),pl=Symbol(),cr=Symbol();function Br(e,t){e instanceof Map&&t instanceof Map?t.forEach((s,o)=>e.set(o,s)):e instanceof Set&&t instanceof Set&&t.forEach(e.add,e);for(const s in t){if(!t.hasOwnProperty(s))continue;const o=t[s],n=e[s];Mr(n)&&Mr(o)&&e.hasOwnProperty(s)&&!Me(o)&&!es(o)?e[s]=Br(n,o):e[s]=o}return e}const T0=Symbol();function j0(e){return!Mr(e)||!Object.prototype.hasOwnProperty.call(e,T0)}const{assign:Wt}=Object;function R0(e){return!!(Me(e)&&e.effect)}function M0(e,t,s,o){const{state:n,actions:r,getters:l}=t,a=s.state.value[e];let f;function u(){a||(s.state.value[e]=n?n():{});const c=Ud(s.state.value[e]);return Wt(c,r,Object.keys(l||{}).reduce((d,p)=>(d[p]=ni(Y(()=>{In(s);const h=s._s.get(e);return l[p].call(h,h)})),d),{}))}return f=yc(e,u,t,s,o,!0),f}function yc(e,t,s={},o,n,r){let l;const a=Wt({actions:{}},s),f={deep:!0};let u,c,d=[],p=[],h;const v=o.state.value[e];!r&&!v&&(o.state.value[e]={}),W({});let $;function y(_){let E;u=c=!1,typeof _=="function"?(_(o.state.value[e]),E={type:mo.patchFunction,storeId:e,events:h}):(Br(o.state.value[e],_),E={type:mo.patchObject,payload:_,storeId:e,events:h});const q=$=Symbol();jn().then(()=>{$===q&&(u=!0)}),c=!0,Ss(d,E,o.state.value[e])}const x=r?function(){const{state:E}=s,q=E?E():{};this.$patch(we=>{Wt(we,q)})}:vc;function m(){l.stop(),d=[],p=[],o._s.delete(e)}const b=(_,E="")=>{if(pl in _)return _[cr]=E,_;const q=function(){In(o);const we=Array.from(arguments),Te=[],je=[];function Ot(F){Te.push(F)}function xt(F){je.push(F)}Ss(p,{args:we,name:q[cr],store:L,after:Ot,onError:xt});let X;try{X=_.apply(this&&this.$id===e?this:L,we)}catch(F){throw Ss(je,F),F}return X instanceof Promise?X.then(F=>(Ss(Te,F),F)).catch(F=>(Ss(je,F),Promise.reject(F))):(Ss(Te,X),X)};return q[pl]=!0,q[cr]=E,q},T={_p:o,$id:e,$onAction:fl.bind(null,p),$patch:y,$reset:x,$subscribe(_,E={}){const q=fl(d,_,E.detached,()=>we()),we=l.run(()=>De(()=>o.state.value[e],Te=>{(E.flush==="sync"?c:u)&&_({storeId:e,type:mo.direct,events:h},Te)},Wt({},f,E)));return q},$dispose:m},L=So(T);o._s.set(e,L);const j=(o._a&&o._a.runWithContext||P0)(()=>o._e.run(()=>(l=ga()).run(()=>t({action:b}))));for(const _ in j){const E=j[_];if(Me(E)&&!R0(E)||es(E))r||(v&&j0(E)&&(Me(E)?E.value=v[_]:Br(E,v[_])),o.state.value[e][_]=E);else if(typeof E=="function"){const q=b(E,_);j[_]=q,a.actions[_]=E}}return Wt(L,j),Wt(pe(L),j),Object.defineProperty(L,"$state",{get:()=>o.state.value[e],set:_=>{y(E=>{Wt(E,_)})}}),o._p.forEach(_=>{Wt(L,l.run(()=>_({store:L,app:o._a,pinia:o,options:a})))}),v&&r&&s.hydrate&&s.hydrate(L.$state,v),u=!0,c=!0,L}/*! #__NO_SIDE_EFFECTS__ */function os(e,t,s){let o;const n=typeof t=="function";o=n?s:t;function r(l,a){const f=yf();return l=l||(f?mt(bc,null):null),l&&In(l),l=wc,l._s.has(e)||(n?yc(e,t,o,l):M0(e,o,l)),l._s.get(e)}return r.$id=e,r}/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */const Ps=typeof document<"u";function xc(e){return typeof e=="object"||"displayName"in e||"props"in e||"__vccOpts"in e}function B0(e){return e.__esModule||e[Symbol.toStringTag]==="Module"||e.default&&xc(e.default)}const be=Object.assign;function ur(e,t){const s={};for(const o in t){const n=t[o];s[o]=vt(n)?n.map(e):e(n)}return s}const go=()=>{},vt=Array.isArray,kc=/#/g,O0=/&/g,I0=/\//g,D0=/=/g,L0=/\?/g,_c=/\+/g,F0=/%5B/g,U0=/%5D/g,Cc=/%5E/g,N0=/%60/g,$c=/%7B/g,V0=/%7C/g,Ec=/%7D/g,H0=/%20/g;function ui(e){return encodeURI(""+e).replace(V0,"|").replace(F0,"[").replace(U0,"]")}function z0(e){return ui(e).replace($c,"{").replace(Ec,"}").replace(Cc,"^")}function Or(e){return ui(e).replace(_c,"%2B").replace(H0,"+").replace(kc,"%23").replace(O0,"%26").replace(N0,"`").replace($c,"{").replace(Ec,"}").replace(Cc,"^")}function q0(e){return Or(e).replace(D0,"%3D")}function K0(e){return ui(e).replace(kc,"%23").replace(L0,"%3F")}function W0(e){return e==null?"":K0(e).replace(I0,"%2F")}function _o(e){try{return decodeURIComponent(""+e)}catch{}return""+e}const J0=/\/$/,G0=e=>e.replace(J0,"");function dr(e,t,s="/"){let o,n={},r="",l="";const a=t.indexOf("#");let f=t.indexOf("?");return a<f&&a>=0&&(f=-1),f>-1&&(o=t.slice(0,f),r=t.slice(f+1,a>-1?a:t.length),n=e(r)),a>-1&&(o=o||t.slice(0,a),l=t.slice(a,t.length)),o=Q0(o??t,s),{fullPath:o+(r&&"?")+r+l,path:o,query:n,hash:_o(l)}}function Y0(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}function hl(e,t){return!t||!e.toLowerCase().startsWith(t.toLowerCase())?e:e.slice(t.length)||"/"}function Z0(e,t,s){const o=t.matched.length-1,n=s.matched.length-1;return o>-1&&o===n&&Ks(t.matched[o],s.matched[n])&&Sc(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}function Ks(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function Sc(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!X0(e[s],t[s]))return!1;return!0}function X0(e,t){return vt(e)?ml(e,t):vt(t)?ml(t,e):e===t}function ml(e,t){return vt(t)?e.length===t.length&&e.every((s,o)=>s===t[o]):e.length===1&&e[0]===t}function Q0(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),o=e.split("/"),n=o[o.length-1];(n===".."||n===".")&&o.push("");let r=s.length-1,l,a;for(l=0;l<o.length;l++)if(a=o[l],a!==".")if(a==="..")r>1&&r--;else break;return s.slice(0,r).join("/")+"/"+o.slice(l).join("/")}const Kt={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var Co;(function(e){e.pop="pop",e.push="push"})(Co||(Co={}));var wo;(function(e){e.back="back",e.forward="forward",e.unknown=""})(wo||(wo={}));function ep(e){if(!e)if(Ps){const t=document.querySelector("base");e=t&&t.getAttribute("href")||"/",e=e.replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return e[0]!=="/"&&e[0]!=="#"&&(e="/"+e),G0(e)}const tp=/^[^#]+#/;function sp(e,t){return e.replace(tp,"#")+t}function op(e,t){const s=document.documentElement.getBoundingClientRect(),o=e.getBoundingClientRect();return{behavior:t.behavior,left:o.left-s.left-(t.left||0),top:o.top-s.top-(t.top||0)}}const Dn=()=>({left:window.scrollX,top:window.scrollY});function np(e){let t;if("el"in e){const s=e.el,o=typeof s=="string"&&s.startsWith("#"),n=typeof s=="string"?o?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=op(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(t.left!=null?t.left:window.scrollX,t.top!=null?t.top:window.scrollY)}function gl(e,t){return(history.state?history.state.position-t:-1)+e}const Ir=new Map;function rp(e,t){Ir.set(e,t)}function ip(e){const t=Ir.get(e);return Ir.delete(e),t}let lp=()=>location.protocol+"//"+location.host;function Ac(e,t){const{pathname:s,search:o,hash:n}=t,r=e.indexOf("#");if(r>-1){let a=n.includes(e.slice(r))?e.slice(r).length:1,f=n.slice(a);return f[0]!=="/"&&(f="/"+f),hl(f,"")}return hl(s,e)+o+n}function ap(e,t,s,o){let n=[],r=[],l=null;const a=({state:p})=>{const h=Ac(e,location),v=s.value,$=t.value;let y=0;if(p){if(s.value=h,t.value=p,l&&l===v){l=null;return}y=$?p.position-$.position:0}else o(h);n.forEach(x=>{x(s.value,v,{delta:y,type:Co.pop,direction:y?y>0?wo.forward:wo.back:wo.unknown})})};function f(){l=s.value}function u(p){n.push(p);const h=()=>{const v=n.indexOf(p);v>-1&&n.splice(v,1)};return r.push(h),h}function c(){const{history:p}=window;p.state&&p.replaceState(be({},p.state,{scroll:Dn()}),"")}function d(){for(const p of r)p();r=[],window.removeEventListener("popstate",a),window.removeEventListener("beforeunload",c)}return window.addEventListener("popstate",a),window.addEventListener("beforeunload",c,{passive:!0}),{pauseListeners:f,listen:u,destroy:d}}function wl(e,t,s,o=!1,n=!1){return{back:e,current:t,forward:s,replaced:o,position:window.history.length,scroll:n?Dn():null}}function cp(e){const{history:t,location:s}=window,o={value:Ac(e,s)},n={value:t.state};n.value||r(o.value,{back:null,current:o.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0);function r(f,u,c){const d=e.indexOf("#"),p=d>-1?(s.host&&document.querySelector("base")?e:e.slice(d))+f:lp()+e+f;try{t[c?"replaceState":"pushState"](u,"",p),n.value=u}catch(h){console.error(h),s[c?"replace":"assign"](p)}}function l(f,u){const c=be({},t.state,wl(n.value.back,f,n.value.forward,!0),u,{position:n.value.position});r(f,c,!0),o.value=f}function a(f,u){const c=be({},n.value,t.state,{forward:f,scroll:Dn()});r(c.current,c,!0);const d=be({},wl(o.value,f,null),{position:c.position+1},u);r(f,d,!1),o.value=f}return{location:o,state:n,push:a,replace:l}}function up(e){e=ep(e);const t=cp(e),s=ap(e,t.state,t.location,t.replace);function o(r,l=!0){l||s.pauseListeners(),history.go(r)}const n=be({location:"",base:e,go:o,createHref:sp.bind(null,e)},t,s);return Object.defineProperty(n,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(n,"state",{enumerable:!0,get:()=>t.state.value}),n}function dp(e){return typeof e=="string"||e&&typeof e=="object"}function Pc(e){return typeof e=="string"||typeof e=="symbol"}const Tc=Symbol("");var bl;(function(e){e[e.aborted=4]="aborted",e[e.cancelled=8]="cancelled",e[e.duplicated=16]="duplicated"})(bl||(bl={}));function Ws(e,t){return be(new Error,{type:e,[Tc]:!0},t)}function Lt(e,t){return e instanceof Error&&Tc in e&&(t==null||!!(e.type&t))}const vl="[^/]+?",fp={sensitive:!1,strict:!1,start:!0,end:!0},pp=/[.+*?^${}()[\]/\\]/g;function hp(e,t){const s=be({},fp,t),o=[];let n=s.start?"^":"";const r=[];for(const u of e){const c=u.length?[]:[90];s.strict&&!u.length&&(n+="/");for(let d=0;d<u.length;d++){const p=u[d];let h=40+(s.sensitive?.25:0);if(p.type===0)d||(n+="/"),n+=p.value.replace(pp,"\\$&"),h+=40;else if(p.type===1){const{value:v,repeatable:$,optional:y,regexp:x}=p;r.push({name:v,repeatable:$,optional:y});const m=x||vl;if(m!==vl){h+=10;try{new RegExp(`(${m})`)}catch(T){throw new Error(`Invalid custom RegExp for param "${v}" (${m}): `+T.message)}}let b=$?`((?:${m})(?:/(?:${m}))*)`:`(${m})`;d||(b=y&&u.length<2?`(?:/${b})`:"/"+b),y&&(b+="?"),n+=b,h+=20,y&&(h+=-8),$&&(h+=-20),m===".*"&&(h+=-50)}c.push(h)}o.push(c)}if(s.strict&&s.end){const u=o.length-1;o[u][o[u].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const l=new RegExp(n,s.sensitive?"":"i");function a(u){const c=u.match(l),d={};if(!c)return null;for(let p=1;p<c.length;p++){const h=c[p]||"",v=r[p-1];d[v.name]=h&&v.repeatable?h.split("/"):h}return d}function f(u){let c="",d=!1;for(const p of e){(!d||!c.endsWith("/"))&&(c+="/"),d=!1;for(const h of p)if(h.type===0)c+=h.value;else if(h.type===1){const{value:v,repeatable:$,optional:y}=h,x=v in u?u[v]:"";if(vt(x)&&!$)throw new Error(`Provided param "${v}" is an array but it is not repeatable (* or + modifiers)`);const m=vt(x)?x.join("/"):x;if(!m)if(y)p.length<2&&(c.endsWith("/")?c=c.slice(0,-1):d=!0);else throw new Error(`Missing required param "${v}"`);c+=m}}return c||"/"}return{re:l,score:o,keys:r,parse:a,stringify:f}}function mp(e,t){let s=0;for(;s<e.length&&s<t.length;){const o=t[s]-e[s];if(o)return o;s++}return e.length<t.length?e.length===1&&e[0]===80?-1:1:e.length>t.length?t.length===1&&t[0]===80?1:-1:0}function jc(e,t){let s=0;const o=e.score,n=t.score;for(;s<o.length&&s<n.length;){const r=mp(o[s],n[s]);if(r)return r;s++}if(Math.abs(n.length-o.length)===1){if(yl(o))return 1;if(yl(n))return-1}return n.length-o.length}function yl(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const gp={type:0,value:""},wp=/[a-zA-Z0-9_]/;function bp(e){if(!e)return[[]];if(e==="/")return[[gp]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(h){throw new Error(`ERR (${s})/"${u}": ${h}`)}let s=0,o=s;const n=[];let r;function l(){r&&n.push(r),r=[]}let a=0,f,u="",c="";function d(){u&&(s===0?r.push({type:0,value:u}):s===1||s===2||s===3?(r.length>1&&(f==="*"||f==="+")&&t(`A repeatable param (${u}) must be alone in its segment. eg: '/:ids+.`),r.push({type:1,value:u,regexp:c,repeatable:f==="*"||f==="+",optional:f==="*"||f==="?"})):t("Invalid state to consume buffer"),u="")}function p(){u+=f}for(;a<e.length;){if(f=e[a++],f==="\\"&&s!==2){o=s,s=4;continue}switch(s){case 0:f==="/"?(u&&d(),l()):f===":"?(d(),s=1):p();break;case 4:p(),s=o;break;case 1:f==="("?s=2:wp.test(f)?p():(d(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&a--);break;case 2:f===")"?c[c.length-1]=="\\"?c=c.slice(0,-1)+f:s=3:c+=f;break;case 3:d(),s=0,f!=="*"&&f!=="?"&&f!=="+"&&a--,c="";break;default:t("Unknown state");break}}return s===2&&t(`Unfinished custom RegExp for param "${u}"`),d(),l(),n}function vp(e,t,s){const o=hp(bp(e.path),s),n=be(o,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function yp(e,t){const s=[],o=new Map;t=Cl({strict:!1,end:!0,sensitive:!1},t);function n(d){return o.get(d)}function r(d,p,h){const v=!h,$=kl(d);$.aliasOf=h&&h.record;const y=Cl(t,d),x=[$];if("alias"in d){const T=typeof d.alias=="string"?[d.alias]:d.alias;for(const L of T)x.push(kl(be({},$,{components:h?h.record.components:$.components,path:L,aliasOf:h?h.record:$})))}let m,b;for(const T of x){const{path:L}=T;if(p&&L[0]!=="/"){const J=p.record.path,j=J[J.length-1]==="/"?"":"/";T.path=p.record.path+(L&&j+L)}if(m=vp(T,p,y),h?h.alias.push(m):(b=b||m,b!==m&&b.alias.push(m),v&&d.name&&!_l(m)&&l(d.name)),Rc(m)&&f(m),$.children){const J=$.children;for(let j=0;j<J.length;j++)r(J[j],m,h&&h.children[j])}h=h||m}return b?()=>{l(b)}:go}function l(d){if(Pc(d)){const p=o.get(d);p&&(o.delete(d),s.splice(s.indexOf(p),1),p.children.forEach(l),p.alias.forEach(l))}else{const p=s.indexOf(d);p>-1&&(s.splice(p,1),d.record.name&&o.delete(d.record.name),d.children.forEach(l),d.alias.forEach(l))}}function a(){return s}function f(d){const p=_p(d,s);s.splice(p,0,d),d.record.name&&!_l(d)&&o.set(d.record.name,d)}function u(d,p){let h,v={},$,y;if("name"in d&&d.name){if(h=o.get(d.name),!h)throw Ws(1,{location:d});y=h.record.name,v=be(xl(p.params,h.keys.filter(b=>!b.optional).concat(h.parent?h.parent.keys.filter(b=>b.optional):[]).map(b=>b.name)),d.params&&xl(d.params,h.keys.map(b=>b.name))),$=h.stringify(v)}else if(d.path!=null)$=d.path,h=s.find(b=>b.re.test($)),h&&(v=h.parse($),y=h.record.name);else{if(h=p.name?o.get(p.name):s.find(b=>b.re.test(p.path)),!h)throw Ws(1,{location:d,currentLocation:p});y=h.record.name,v=be({},p.params,d.params),$=h.stringify(v)}const x=[];let m=h;for(;m;)x.unshift(m.record),m=m.parent;return{name:y,path:$,params:v,matched:x,meta:kp(x)}}e.forEach(d=>r(d));function c(){s.length=0,o.clear()}return{addRoute:r,resolve:u,removeRoute:l,clearRoutes:c,getRoutes:a,getRecordMatcher:n}}function xl(e,t){const s={};for(const o of t)o in e&&(s[o]=e[o]);return s}function kl(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:xp(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function xp(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const o in e.components)t[o]=typeof s=="object"?s[o]:s;return t}function _l(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function kp(e){return e.reduce((t,s)=>be(t,s.meta),{})}function Cl(e,t){const s={};for(const o in e)s[o]=o in t?t[o]:e[o];return s}function _p(e,t){let s=0,o=t.length;for(;s!==o;){const r=s+o>>1;jc(e,t[r])<0?o=r:s=r+1}const n=Cp(e);return n&&(o=t.lastIndexOf(n,o-1)),o}function Cp(e){let t=e;for(;t=t.parent;)if(Rc(t)&&jc(e,t)===0)return t}function Rc({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function $p(e){const t={};if(e===""||e==="?")return t;const o=(e[0]==="?"?e.slice(1):e).split("&");for(let n=0;n<o.length;++n){const r=o[n].replace(_c," "),l=r.indexOf("="),a=_o(l<0?r:r.slice(0,l)),f=l<0?null:_o(r.slice(l+1));if(a in t){let u=t[a];vt(u)||(u=t[a]=[u]),u.push(f)}else t[a]=f}return t}function $l(e){let t="";for(let s in e){const o=e[s];if(s=q0(s),o==null){o!==void 0&&(t+=(t.length?"&":"")+s);continue}(vt(o)?o.map(r=>r&&Or(r)):[o&&Or(o)]).forEach(r=>{r!==void 0&&(t+=(t.length?"&":"")+s,r!=null&&(t+="="+r))})}return t}function Ep(e){const t={};for(const s in e){const o=e[s];o!==void 0&&(t[s]=vt(o)?o.map(n=>n==null?null:""+n):o==null?o:""+o)}return t}const Sp=Symbol(""),El=Symbol(""),Ln=Symbol(""),di=Symbol(""),Dr=Symbol("");function ro(){let e=[];function t(o){return e.push(o),()=>{const n=e.indexOf(o);n>-1&&e.splice(n,1)}}function s(){e=[]}return{add:t,list:()=>e.slice(),reset:s}}function Yt(e,t,s,o,n,r=l=>l()){const l=o&&(o.enterCallbacks[n]=o.enterCallbacks[n]||[]);return()=>new Promise((a,f)=>{const u=p=>{p===!1?f(Ws(4,{from:s,to:t})):p instanceof Error?f(p):dp(p)?f(Ws(2,{from:t,to:p})):(l&&o.enterCallbacks[n]===l&&typeof p=="function"&&l.push(p),a())},c=r(()=>e.call(o&&o.instances[n],t,s,u));let d=Promise.resolve(c);e.length<3&&(d=d.then(u)),d.catch(p=>f(p))})}function fr(e,t,s,o,n=r=>r()){const r=[];for(const l of e)for(const a in l.components){let f=l.components[a];if(!(t!=="beforeRouteEnter"&&!l.instances[a]))if(xc(f)){const c=(f.__vccOpts||f)[t];c&&r.push(Yt(c,s,o,l,a,n))}else{let u=f();r.push(()=>u.then(c=>{if(!c)throw new Error(`Couldn't resolve component "${a}" at "${l.path}"`);const d=B0(c)?c.default:c;l.mods[a]=c,l.components[a]=d;const h=(d.__vccOpts||d)[t];return h&&Yt(h,s,o,l,a,n)()}))}}return r}function Sl(e){const t=mt(Ln),s=mt(di),o=Y(()=>{const f=Fs(e.to);return t.resolve(f)}),n=Y(()=>{const{matched:f}=o.value,{length:u}=f,c=f[u-1],d=s.matched;if(!c||!d.length)return-1;const p=d.findIndex(Ks.bind(null,c));if(p>-1)return p;const h=Al(f[u-2]);return u>1&&Al(c)===h&&d[d.length-1].path!==h?d.findIndex(Ks.bind(null,f[u-2])):p}),r=Y(()=>n.value>-1&&Rp(s.params,o.value.params)),l=Y(()=>n.value>-1&&n.value===s.matched.length-1&&Sc(s.params,o.value.params));function a(f={}){if(jp(f)){const u=t[Fs(e.replace)?"replace":"push"](Fs(e.to)).catch(go);return e.viewTransition&&typeof document<"u"&&"startViewTransition"in document&&document.startViewTransition(()=>u),u}return Promise.resolve()}return{route:o,href:Y(()=>o.value.href),isActive:r,isExactActive:l,navigate:a}}function Ap(e){return e.length===1?e[0]:e}const Pp=Pe({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:Sl,setup(e,{slots:t}){const s=So(Sl(e)),{options:o}=mt(Ln),n=Y(()=>({[Pl(e.activeClass,o.linkActiveClass,"router-link-active")]:s.isActive,[Pl(e.exactActiveClass,o.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const r=t.default&&Ap(t.default(s));return e.custom?r:mc("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},r)}}}),Tp=Pp;function jp(e){if(!(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)&&!e.defaultPrevented&&!(e.button!==void 0&&e.button!==0)){if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}return e.preventDefault&&e.preventDefault(),!0}}function Rp(e,t){for(const s in t){const o=t[s],n=e[s];if(typeof o=="string"){if(o!==n)return!1}else if(!vt(n)||n.length!==o.length||o.some((r,l)=>r!==n[l]))return!1}return!0}function Al(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const Pl=(e,t,s)=>e??t??s,Mp=Pe({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const o=mt(Dr),n=Y(()=>e.route||o.value),r=mt(El,0),l=Y(()=>{let u=Fs(r);const{matched:c}=n.value;let d;for(;(d=c[u])&&!d.components;)u++;return u}),a=Y(()=>n.value.matched[l.value]);qo(El,Y(()=>l.value+1)),qo(Sp,a),qo(Dr,n);const f=W();return De(()=>[f.value,a.value,e.name],([u,c,d],[p,h,v])=>{c&&(c.instances[d]=u,h&&h!==c&&u&&u===p&&(c.leaveGuards.size||(c.leaveGuards=h.leaveGuards),c.updateGuards.size||(c.updateGuards=h.updateGuards))),u&&c&&(!h||!Ks(c,h)||!p)&&(c.enterCallbacks[d]||[]).forEach($=>$(u))},{flush:"post"}),()=>{const u=n.value,c=e.name,d=a.value,p=d&&d.components[c];if(!p)return Tl(s.default,{Component:p,route:u});const h=d.props[c],v=h?h===!0?u.params:typeof h=="function"?h(u):h:null,y=mc(p,be({},v,t,{onVnodeUnmounted:x=>{x.component.isUnmounted&&(d.instances[c]=null)},ref:f}));return Tl(s.default,{Component:y,route:u})||y}}});function Tl(e,t){if(!e)return null;const s=e(t);return s.length===1?s[0]:s}const Bp=Mp;function Op(e){const t=yp(e.routes,e),s=e.parseQuery||$p,o=e.stringifyQuery||$l,n=e.history,r=ro(),l=ro(),a=ro(),f=Dd(Kt);let u=Kt;Ps&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const c=ur.bind(null,B=>""+B),d=ur.bind(null,W0),p=ur.bind(null,_o);function h(B,K){let H,Z;return Pc(B)?(H=t.getRecordMatcher(B),Z=K):Z=B,t.addRoute(Z,H)}function v(B){const K=t.getRecordMatcher(B);K&&t.removeRoute(K)}function $(){return t.getRoutes().map(B=>B.record)}function y(B){return!!t.getRecordMatcher(B)}function x(B,K){if(K=be({},K||f.value),typeof B=="string"){const k=dr(s,B,K.path),M=t.resolve({path:k.path},K),I=n.createHref(k.fullPath);return be(k,M,{params:p(M.params),hash:_o(k.hash),redirectedFrom:void 0,href:I})}let H;if(B.path!=null)H=be({},B,{path:dr(s,B.path,K.path).path});else{const k=be({},B.params);for(const M in k)k[M]==null&&delete k[M];H=be({},B,{params:d(k)}),K.params=d(K.params)}const Z=t.resolve(H,K),_e=B.hash||"";Z.params=c(p(Z.params));const g=Y0(o,be({},B,{hash:z0(_e),path:Z.path})),w=n.createHref(g);return be({fullPath:g,hash:_e,query:o===$l?Ep(B.query):B.query||{}},Z,{redirectedFrom:void 0,href:w})}function m(B){return typeof B=="string"?dr(s,B,f.value.path):be({},B)}function b(B,K){if(u!==B)return Ws(8,{from:K,to:B})}function T(B){return j(B)}function L(B){return T(be(m(B),{replace:!0}))}function J(B){const K=B.matched[B.matched.length-1];if(K&&K.redirect){const{redirect:H}=K;let Z=typeof H=="function"?H(B):H;return typeof Z=="string"&&(Z=Z.includes("?")||Z.includes("#")?Z=m(Z):{path:Z},Z.params={}),be({query:B.query,hash:B.hash,params:Z.path!=null?{}:B.params},Z)}}function j(B,K){const H=u=x(B),Z=f.value,_e=B.state,g=B.force,w=B.replace===!0,k=J(H);if(k)return j(be(m(k),{state:typeof k=="object"?be({},_e,k.state):_e,force:g,replace:w}),K||H);const M=H;M.redirectedFrom=K;let I;return!g&&Z0(o,Z,H)&&(I=Ws(16,{to:M,from:Z}),kt(Z,Z,!0,!1)),(I?Promise.resolve(I):q(M,Z)).catch(O=>Lt(O)?Lt(O,2)?O:rt(O):G(O,M,Z)).then(O=>{if(O){if(Lt(O,2))return j(be({replace:w},m(O.to),{state:typeof O.to=="object"?be({},_e,O.to.state):_e,force:g}),K||M)}else O=Te(M,Z,!0,w,_e);return we(M,Z,O),O})}function _(B,K){const H=b(B,K);return H?Promise.reject(H):Promise.resolve()}function E(B){const K=$s.values().next().value;return K&&typeof K.runWithContext=="function"?K.runWithContext(B):B()}function q(B,K){let H;const[Z,_e,g]=Ip(B,K);H=fr(Z.reverse(),"beforeRouteLeave",B,K);for(const k of Z)k.leaveGuards.forEach(M=>{H.push(Yt(M,B,K))});const w=_.bind(null,B,K);return H.push(w),pt(H).then(()=>{H=[];for(const k of r.list())H.push(Yt(k,B,K));return H.push(w),pt(H)}).then(()=>{H=fr(_e,"beforeRouteUpdate",B,K);for(const k of _e)k.updateGuards.forEach(M=>{H.push(Yt(M,B,K))});return H.push(w),pt(H)}).then(()=>{H=[];for(const k of g)if(k.beforeEnter)if(vt(k.beforeEnter))for(const M of k.beforeEnter)H.push(Yt(M,B,K));else H.push(Yt(k.beforeEnter,B,K));return H.push(w),pt(H)}).then(()=>(B.matched.forEach(k=>k.enterCallbacks={}),H=fr(g,"beforeRouteEnter",B,K,E),H.push(w),pt(H))).then(()=>{H=[];for(const k of l.list())H.push(Yt(k,B,K));return H.push(w),pt(H)}).catch(k=>Lt(k,8)?k:Promise.reject(k))}function we(B,K,H){a.list().forEach(Z=>E(()=>Z(B,K,H)))}function Te(B,K,H,Z,_e){const g=b(B,K);if(g)return g;const w=K===Kt,k=Ps?history.state:{};H&&(Z||w?n.replace(B.fullPath,be({scroll:w&&k&&k.scroll},_e)):n.push(B.fullPath,_e)),f.value=B,kt(B,K,H,w),rt()}let je;function Ot(){je||(je=n.listen((B,K,H)=>{if(!Fo.listening)return;const Z=x(B),_e=J(Z);if(_e){j(be(_e,{replace:!0,force:!0}),Z).catch(go);return}u=Z;const g=f.value;Ps&&rp(gl(g.fullPath,H.delta),Dn()),q(Z,g).catch(w=>Lt(w,12)?w:Lt(w,2)?(j(be(m(w.to),{force:!0}),Z).then(k=>{Lt(k,20)&&!H.delta&&H.type===Co.pop&&n.go(-1,!1)}).catch(go),Promise.reject()):(H.delta&&n.go(-H.delta,!1),G(w,Z,g))).then(w=>{w=w||Te(Z,g,!1),w&&(H.delta&&!Lt(w,8)?n.go(-H.delta,!1):H.type===Co.pop&&Lt(w,20)&&n.go(-1,!1)),we(Z,g,w)}).catch(go)}))}let xt=ro(),X=ro(),F;function G(B,K,H){rt(B);const Z=X.list();return Z.length?Z.forEach(_e=>_e(B,K,H)):console.error(B),Promise.reject(B)}function Fe(){return F&&f.value!==Kt?Promise.resolve():new Promise((B,K)=>{xt.add([B,K])})}function rt(B){return F||(F=!B,Ot(),xt.list().forEach(([K,H])=>B?H(B):K()),xt.reset()),B}function kt(B,K,H,Z){const{scrollBehavior:_e}=e;if(!Ps||!_e)return Promise.resolve();const g=!H&&ip(gl(B.fullPath,0))||(Z||!H)&&history.state&&history.state.scroll||null;return jn().then(()=>_e(B,K,g)).then(w=>w&&np(w)).catch(w=>G(w,B,K))}const Qe=B=>n.go(B);let Cs;const $s=new Set,Fo={currentRoute:f,listening:!0,addRoute:h,removeRoute:v,clearRoutes:t.clearRoutes,hasRoute:y,getRoutes:$,resolve:x,options:e,push:T,replace:L,go:Qe,back:()=>Qe(-1),forward:()=>Qe(1),beforeEach:r.add,beforeResolve:l.add,afterEach:a.add,onError:X.add,isReady:Fe,install(B){const K=this;B.component("RouterLink",Tp),B.component("RouterView",Bp),B.config.globalProperties.$router=K,Object.defineProperty(B.config.globalProperties,"$route",{enumerable:!0,get:()=>Fs(f)}),Ps&&!Cs&&f.value===Kt&&(Cs=!0,T(n.location).catch(_e=>{}));const H={};for(const _e in Kt)Object.defineProperty(H,_e,{get:()=>f.value[_e],enumerable:!0});B.provide(Ln,K),B.provide(di,Ra(H)),B.provide(Dr,f);const Z=B.unmount;$s.add(B),B.unmount=function(){$s.delete(B),$s.size<1&&(u=Kt,je&&je(),je=null,f.value=Kt,Cs=!1,F=!1),Z()}}};function pt(B){return B.reduce((K,H)=>K.then(()=>E(H)),Promise.resolve())}return Fo}function Ip(e,t){const s=[],o=[],n=[],r=Math.max(t.matched.length,e.matched.length);for(let l=0;l<r;l++){const a=t.matched[l];a&&(e.matched.find(u=>Ks(u,a))?o.push(a):s.push(a));const f=e.matched[l];f&&(t.matched.find(u=>Ks(u,f))||n.push(f))}return[s,o,n]}function Mc(){return mt(Ln)}function Dp(e){return mt(di)}function Bc(e,t){return function(){return e.apply(t,arguments)}}const{toString:Lp}=Object.prototype,{getPrototypeOf:fi}=Object,{iterator:Fn,toStringTag:Oc}=Symbol,Un=(e=>t=>{const s=Lp.call(t);return e[s]||(e[s]=s.slice(8,-1).toLowerCase())})(Object.create(null)),yt=e=>(e=e.toLowerCase(),t=>Un(t)===e),Nn=e=>t=>typeof t===e,{isArray:Ys}=Array,$o=Nn("undefined");function Fp(e){return e!==null&&!$o(e)&&e.constructor!==null&&!$o(e.constructor)&&st(e.constructor.isBuffer)&&e.constructor.isBuffer(e)}const Ic=yt("ArrayBuffer");function Up(e){let t;return typeof ArrayBuffer<"u"&&ArrayBuffer.isView?t=ArrayBuffer.isView(e):t=e&&e.buffer&&Ic(e.buffer),t}const Np=Nn("string"),st=Nn("function"),Dc=Nn("number"),Vn=e=>e!==null&&typeof e=="object",Vp=e=>e===!0||e===!1,Go=e=>{if(Un(e)!=="object")return!1;const t=fi(e);return(t===null||t===Object.prototype||Object.getPrototypeOf(t)===null)&&!(Oc in e)&&!(Fn in e)},Hp=yt("Date"),zp=yt("File"),qp=yt("Blob"),Kp=yt("FileList"),Wp=e=>Vn(e)&&st(e.pipe),Jp=e=>{let t;return e&&(typeof FormData=="function"&&e instanceof FormData||st(e.append)&&((t=Un(e))==="formdata"||t==="object"&&st(e.toString)&&e.toString()==="[object FormData]"))},Gp=yt("URLSearchParams"),[Yp,Zp,Xp,Qp]=["ReadableStream","Request","Response","Headers"].map(yt),eh=e=>e.trim?e.trim():e.replace(/^[\s\uFEFF\xA0]+|[\s\uFEFF\xA0]+$/g,"");function To(e,t,{allOwnKeys:s=!1}={}){if(e===null||typeof e>"u")return;let o,n;if(typeof e!="object"&&(e=[e]),Ys(e))for(o=0,n=e.length;o<n;o++)t.call(null,e[o],o,e);else{const r=s?Object.getOwnPropertyNames(e):Object.keys(e),l=r.length;let a;for(o=0;o<l;o++)a=r[o],t.call(null,e[a],a,e)}}function Lc(e,t){t=t.toLowerCase();const s=Object.keys(e);let o=s.length,n;for(;o-- >0;)if(n=s[o],t===n.toLowerCase())return n;return null}const us=typeof globalThis<"u"?globalThis:typeof self<"u"?self:typeof window<"u"?window:global,Fc=e=>!$o(e)&&e!==us;function Lr(){const{caseless:e}=Fc(this)&&this||{},t={},s=(o,n)=>{const r=e&&Lc(t,n)||n;Go(t[r])&&Go(o)?t[r]=Lr(t[r],o):Go(o)?t[r]=Lr({},o):Ys(o)?t[r]=o.slice():t[r]=o};for(let o=0,n=arguments.length;o<n;o++)arguments[o]&&To(arguments[o],s);return t}const th=(e,t,s,{allOwnKeys:o}={})=>(To(t,(n,r)=>{s&&st(n)?e[r]=Bc(n,s):e[r]=n},{allOwnKeys:o}),e),sh=e=>(e.charCodeAt(0)===65279&&(e=e.slice(1)),e),oh=(e,t,s,o)=>{e.prototype=Object.create(t.prototype,o),e.prototype.constructor=e,Object.defineProperty(e,"super",{value:t.prototype}),s&&Object.assign(e.prototype,s)},nh=(e,t,s,o)=>{let n,r,l;const a={};if(t=t||{},e==null)return t;do{for(n=Object.getOwnPropertyNames(e),r=n.length;r-- >0;)l=n[r],(!o||o(l,e,t))&&!a[l]&&(t[l]=e[l],a[l]=!0);e=s!==!1&&fi(e)}while(e&&(!s||s(e,t))&&e!==Object.prototype);return t},rh=(e,t,s)=>{e=String(e),(s===void 0||s>e.length)&&(s=e.length),s-=t.length;const o=e.indexOf(t,s);return o!==-1&&o===s},ih=e=>{if(!e)return null;if(Ys(e))return e;let t=e.length;if(!Dc(t))return null;const s=new Array(t);for(;t-- >0;)s[t]=e[t];return s},lh=(e=>t=>e&&t instanceof e)(typeof Uint8Array<"u"&&fi(Uint8Array)),ah=(e,t)=>{const o=(e&&e[Fn]).call(e);let n;for(;(n=o.next())&&!n.done;){const r=n.value;t.call(e,r[0],r[1])}},ch=(e,t)=>{let s;const o=[];for(;(s=e.exec(t))!==null;)o.push(s);return o},uh=yt("HTMLFormElement"),dh=e=>e.toLowerCase().replace(/[-_\s]([a-z\d])(\w*)/g,function(s,o,n){return o.toUpperCase()+n}),jl=(({hasOwnProperty:e})=>(t,s)=>e.call(t,s))(Object.prototype),fh=yt("RegExp"),Uc=(e,t)=>{const s=Object.getOwnPropertyDescriptors(e),o={};To(s,(n,r)=>{let l;(l=t(n,r,e))!==!1&&(o[r]=l||n)}),Object.defineProperties(e,o)},ph=e=>{Uc(e,(t,s)=>{if(st(e)&&["arguments","caller","callee"].indexOf(s)!==-1)return!1;const o=e[s];if(st(o)){if(t.enumerable=!1,"writable"in t){t.writable=!1;return}t.set||(t.set=()=>{throw Error("Can not rewrite read-only method '"+s+"'")})}})},hh=(e,t)=>{const s={},o=n=>{n.forEach(r=>{s[r]=!0})};return Ys(e)?o(e):o(String(e).split(t)),s},mh=()=>{},gh=(e,t)=>e!=null&&Number.isFinite(e=+e)?e:t;function wh(e){return!!(e&&st(e.append)&&e[Oc]==="FormData"&&e[Fn])}const bh=e=>{const t=new Array(10),s=(o,n)=>{if(Vn(o)){if(t.indexOf(o)>=0)return;if(!("toJSON"in o)){t[n]=o;const r=Ys(o)?[]:{};return To(o,(l,a)=>{const f=s(l,n+1);!$o(f)&&(r[a]=f)}),t[n]=void 0,r}}return o};return s(e,0)},vh=yt("AsyncFunction"),yh=e=>e&&(Vn(e)||st(e))&&st(e.then)&&st(e.catch),Nc=((e,t)=>e?setImmediate:t?((s,o)=>(us.addEventListener("message",({source:n,data:r})=>{n===us&&r===s&&o.length&&o.shift()()},!1),n=>{o.push(n),us.postMessage(s,"*")}))(`axios@${Math.random()}`,[]):s=>setTimeout(s))(typeof setImmediate=="function",st(us.postMessage)),xh=typeof queueMicrotask<"u"?queueMicrotask.bind(us):typeof process<"u"&&process.nextTick||Nc,kh=e=>e!=null&&st(e[Fn]),A={isArray:Ys,isArrayBuffer:Ic,isBuffer:Fp,isFormData:Jp,isArrayBufferView:Up,isString:Np,isNumber:Dc,isBoolean:Vp,isObject:Vn,isPlainObject:Go,isReadableStream:Yp,isRequest:Zp,isResponse:Xp,isHeaders:Qp,isUndefined:$o,isDate:Hp,isFile:zp,isBlob:qp,isRegExp:fh,isFunction:st,isStream:Wp,isURLSearchParams:Gp,isTypedArray:lh,isFileList:Kp,forEach:To,merge:Lr,extend:th,trim:eh,stripBOM:sh,inherits:oh,toFlatObject:nh,kindOf:Un,kindOfTest:yt,endsWith:rh,toArray:ih,forEachEntry:ah,matchAll:ch,isHTMLForm:uh,hasOwnProperty:jl,hasOwnProp:jl,reduceDescriptors:Uc,freezeMethods:ph,toObjectSet:hh,toCamelCase:dh,noop:mh,toFiniteNumber:gh,findKey:Lc,global:us,isContextDefined:Fc,isSpecCompliantForm:wh,toJSONObject:bh,isAsyncFn:vh,isThenable:yh,setImmediate:Nc,asap:xh,isIterable:kh};function ae(e,t,s,o,n){Error.call(this),Error.captureStackTrace?Error.captureStackTrace(this,this.constructor):this.stack=new Error().stack,this.message=e,this.name="AxiosError",t&&(this.code=t),s&&(this.config=s),o&&(this.request=o),n&&(this.response=n,this.status=n.status?n.status:null)}A.inherits(ae,Error,{toJSON:function(){return{message:this.message,name:this.name,description:this.description,number:this.number,fileName:this.fileName,lineNumber:this.lineNumber,columnNumber:this.columnNumber,stack:this.stack,config:A.toJSONObject(this.config),code:this.code,status:this.status}}});const Vc=ae.prototype,Hc={};["ERR_BAD_OPTION_VALUE","ERR_BAD_OPTION","ECONNABORTED","ETIMEDOUT","ERR_NETWORK","ERR_FR_TOO_MANY_REDIRECTS","ERR_DEPRECATED","ERR_BAD_RESPONSE","ERR_BAD_REQUEST","ERR_CANCELED","ERR_NOT_SUPPORT","ERR_INVALID_URL"].forEach(e=>{Hc[e]={value:e}});Object.defineProperties(ae,Hc);Object.defineProperty(Vc,"isAxiosError",{value:!0});ae.from=(e,t,s,o,n,r)=>{const l=Object.create(Vc);return A.toFlatObject(e,l,function(f){return f!==Error.prototype},a=>a!=="isAxiosError"),ae.call(l,e.message,t,s,o,n),l.cause=e,l.name=e.name,r&&Object.assign(l,r),l};const _h=null;function Fr(e){return A.isPlainObject(e)||A.isArray(e)}function zc(e){return A.endsWith(e,"[]")?e.slice(0,-2):e}function Rl(e,t,s){return e?e.concat(t).map(function(n,r){return n=zc(n),!s&&r?"["+n+"]":n}).join(s?".":""):t}function Ch(e){return A.isArray(e)&&!e.some(Fr)}const $h=A.toFlatObject(A,{},null,function(t){return/^is[A-Z]/.test(t)});function Hn(e,t,s){if(!A.isObject(e))throw new TypeError("target must be an object");t=t||new FormData,s=A.toFlatObject(s,{metaTokens:!0,dots:!1,indexes:!1},!1,function($,y){return!A.isUndefined(y[$])});const o=s.metaTokens,n=s.visitor||c,r=s.dots,l=s.indexes,f=(s.Blob||typeof Blob<"u"&&Blob)&&A.isSpecCompliantForm(t);if(!A.isFunction(n))throw new TypeError("visitor must be a function");function u(v){if(v===null)return"";if(A.isDate(v))return v.toISOString();if(!f&&A.isBlob(v))throw new ae("Blob is not supported. Use a Buffer instead.");return A.isArrayBuffer(v)||A.isTypedArray(v)?f&&typeof Blob=="function"?new Blob([v]):Buffer.from(v):v}function c(v,$,y){let x=v;if(v&&!y&&typeof v=="object"){if(A.endsWith($,"{}"))$=o?$:$.slice(0,-2),v=JSON.stringify(v);else if(A.isArray(v)&&Ch(v)||(A.isFileList(v)||A.endsWith($,"[]"))&&(x=A.toArray(v)))return $=zc($),x.forEach(function(b,T){!(A.isUndefined(b)||b===null)&&t.append(l===!0?Rl([$],T,r):l===null?$:$+"[]",u(b))}),!1}return Fr(v)?!0:(t.append(Rl(y,$,r),u(v)),!1)}const d=[],p=Object.assign($h,{defaultVisitor:c,convertValue:u,isVisitable:Fr});function h(v,$){if(!A.isUndefined(v)){if(d.indexOf(v)!==-1)throw Error("Circular reference detected in "+$.join("."));d.push(v),A.forEach(v,function(x,m){(!(A.isUndefined(x)||x===null)&&n.call(t,x,A.isString(m)?m.trim():m,$,p))===!0&&h(x,$?$.concat(m):[m])}),d.pop()}}if(!A.isObject(e))throw new TypeError("data must be an object");return h(e),t}function Ml(e){const t={"!":"%21","'":"%27","(":"%28",")":"%29","~":"%7E","%20":"+","%00":"\0"};return encodeURIComponent(e).replace(/[!'()~]|%20|%00/g,function(o){return t[o]})}function pi(e,t){this._pairs=[],e&&Hn(e,this,t)}const qc=pi.prototype;qc.append=function(t,s){this._pairs.push([t,s])};qc.toString=function(t){const s=t?function(o){return t.call(this,o,Ml)}:Ml;return this._pairs.map(function(n){return s(n[0])+"="+s(n[1])},"").join("&")};function Eh(e){return encodeURIComponent(e).replace(/%3A/gi,":").replace(/%24/g,"$").replace(/%2C/gi,",").replace(/%20/g,"+").replace(/%5B/gi,"[").replace(/%5D/gi,"]")}function Kc(e,t,s){if(!t)return e;const o=s&&s.encode||Eh;A.isFunction(s)&&(s={serialize:s});const n=s&&s.serialize;let r;if(n?r=n(t,s):r=A.isURLSearchParams(t)?t.toString():new pi(t,s).toString(o),r){const l=e.indexOf("#");l!==-1&&(e=e.slice(0,l)),e+=(e.indexOf("?")===-1?"?":"&")+r}return e}class Bl{constructor(){this.handlers=[]}use(t,s,o){return this.handlers.push({fulfilled:t,rejected:s,synchronous:o?o.synchronous:!1,runWhen:o?o.runWhen:null}),this.handlers.length-1}eject(t){this.handlers[t]&&(this.handlers[t]=null)}clear(){this.handlers&&(this.handlers=[])}forEach(t){A.forEach(this.handlers,function(o){o!==null&&t(o)})}}const Wc={silentJSONParsing:!0,forcedJSONParsing:!0,clarifyTimeoutError:!1},Sh=typeof URLSearchParams<"u"?URLSearchParams:pi,Ah=typeof FormData<"u"?FormData:null,Ph=typeof Blob<"u"?Blob:null,Th={isBrowser:!0,classes:{URLSearchParams:Sh,FormData:Ah,Blob:Ph},protocols:["http","https","file","blob","url","data"]},hi=typeof window<"u"&&typeof document<"u",Ur=typeof navigator=="object"&&navigator||void 0,jh=hi&&(!Ur||["ReactNative","NativeScript","NS"].indexOf(Ur.product)<0),Rh=typeof WorkerGlobalScope<"u"&&self instanceof WorkerGlobalScope&&typeof self.importScripts=="function",Mh=hi&&window.location.href||"http://localhost",Bh=Object.freeze(Object.defineProperty({__proto__:null,hasBrowserEnv:hi,hasStandardBrowserEnv:jh,hasStandardBrowserWebWorkerEnv:Rh,navigator:Ur,origin:Mh},Symbol.toStringTag,{value:"Module"})),qe={...Bh,...Th};function Oh(e,t){return Hn(e,new qe.classes.URLSearchParams,Object.assign({visitor:function(s,o,n,r){return qe.isNode&&A.isBuffer(s)?(this.append(o,s.toString("base64")),!1):r.defaultVisitor.apply(this,arguments)}},t))}function Ih(e){return A.matchAll(/\w+|\[(\w*)]/g,e).map(t=>t[0]==="[]"?"":t[1]||t[0])}function Dh(e){const t={},s=Object.keys(e);let o;const n=s.length;let r;for(o=0;o<n;o++)r=s[o],t[r]=e[r];return t}function Jc(e){function t(s,o,n,r){let l=s[r++];if(l==="__proto__")return!0;const a=Number.isFinite(+l),f=r>=s.length;return l=!l&&A.isArray(n)?n.length:l,f?(A.hasOwnProp(n,l)?n[l]=[n[l],o]:n[l]=o,!a):((!n[l]||!A.isObject(n[l]))&&(n[l]=[]),t(s,o,n[l],r)&&A.isArray(n[l])&&(n[l]=Dh(n[l])),!a)}if(A.isFormData(e)&&A.isFunction(e.entries)){const s={};return A.forEachEntry(e,(o,n)=>{t(Ih(o),n,s,0)}),s}return null}function Lh(e,t,s){if(A.isString(e))try{return(t||JSON.parse)(e),A.trim(e)}catch(o){if(o.name!=="SyntaxError")throw o}return(s||JSON.stringify)(e)}const jo={transitional:Wc,adapter:["xhr","http","fetch"],transformRequest:[function(t,s){const o=s.getContentType()||"",n=o.indexOf("application/json")>-1,r=A.isObject(t);if(r&&A.isHTMLForm(t)&&(t=new FormData(t)),A.isFormData(t))return n?JSON.stringify(Jc(t)):t;if(A.isArrayBuffer(t)||A.isBuffer(t)||A.isStream(t)||A.isFile(t)||A.isBlob(t)||A.isReadableStream(t))return t;if(A.isArrayBufferView(t))return t.buffer;if(A.isURLSearchParams(t))return s.setContentType("application/x-www-form-urlencoded;charset=utf-8",!1),t.toString();let a;if(r){if(o.indexOf("application/x-www-form-urlencoded")>-1)return Oh(t,this.formSerializer).toString();if((a=A.isFileList(t))||o.indexOf("multipart/form-data")>-1){const f=this.env&&this.env.FormData;return Hn(a?{"files[]":t}:t,f&&new f,this.formSerializer)}}return r||n?(s.setContentType("application/json",!1),Lh(t)):t}],transformResponse:[function(t){const s=this.transitional||jo.transitional,o=s&&s.forcedJSONParsing,n=this.responseType==="json";if(A.isResponse(t)||A.isReadableStream(t))return t;if(t&&A.isString(t)&&(o&&!this.responseType||n)){const l=!(s&&s.silentJSONParsing)&&n;try{return JSON.parse(t)}catch(a){if(l)throw a.name==="SyntaxError"?ae.from(a,ae.ERR_BAD_RESPONSE,this,null,this.response):a}}return t}],timeout:0,xsrfCookieName:"XSRF-TOKEN",xsrfHeaderName:"X-XSRF-TOKEN",maxContentLength:-1,maxBodyLength:-1,env:{FormData:qe.classes.FormData,Blob:qe.classes.Blob},validateStatus:function(t){return t>=200&&t<300},headers:{common:{Accept:"application/json, text/plain, */*","Content-Type":void 0}}};A.forEach(["delete","get","head","post","put","patch"],e=>{jo.headers[e]={}});const Fh=A.toObjectSet(["age","authorization","content-length","content-type","etag","expires","from","host","if-modified-since","if-unmodified-since","last-modified","location","max-forwards","proxy-authorization","referer","retry-after","user-agent"]),Uh=e=>{const t={};let s,o,n;return e&&e.split(`
`).forEach(function(l){n=l.indexOf(":"),s=l.substring(0,n).trim().toLowerCase(),o=l.substring(n+1).trim(),!(!s||t[s]&&Fh[s])&&(s==="set-cookie"?t[s]?t[s].push(o):t[s]=[o]:t[s]=t[s]?t[s]+", "+o:o)}),t},Ol=Symbol("internals");function io(e){return e&&String(e).trim().toLowerCase()}function Yo(e){return e===!1||e==null?e:A.isArray(e)?e.map(Yo):String(e)}function Nh(e){const t=Object.create(null),s=/([^\s,;=]+)\s*(?:=\s*([^,;]+))?/g;let o;for(;o=s.exec(e);)t[o[1]]=o[2];return t}const Vh=e=>/^[-_a-zA-Z0-9^`|~,!#$%&'*+.]+$/.test(e.trim());function pr(e,t,s,o,n){if(A.isFunction(o))return o.call(this,t,s);if(n&&(t=s),!!A.isString(t)){if(A.isString(o))return t.indexOf(o)!==-1;if(A.isRegExp(o))return o.test(t)}}function Hh(e){return e.trim().toLowerCase().replace(/([a-z\d])(\w*)/g,(t,s,o)=>s.toUpperCase()+o)}function zh(e,t){const s=A.toCamelCase(" "+t);["get","set","has"].forEach(o=>{Object.defineProperty(e,o+s,{value:function(n,r,l){return this[o].call(this,t,n,r,l)},configurable:!0})})}let ot=class{constructor(t){t&&this.set(t)}set(t,s,o){const n=this;function r(a,f,u){const c=io(f);if(!c)throw new Error("header name must be a non-empty string");const d=A.findKey(n,c);(!d||n[d]===void 0||u===!0||u===void 0&&n[d]!==!1)&&(n[d||f]=Yo(a))}const l=(a,f)=>A.forEach(a,(u,c)=>r(u,c,f));if(A.isPlainObject(t)||t instanceof this.constructor)l(t,s);else if(A.isString(t)&&(t=t.trim())&&!Vh(t))l(Uh(t),s);else if(A.isObject(t)&&A.isIterable(t)){let a={},f,u;for(const c of t){if(!A.isArray(c))throw TypeError("Object iterator must return a key-value pair");a[u=c[0]]=(f=a[u])?A.isArray(f)?[...f,c[1]]:[f,c[1]]:c[1]}l(a,s)}else t!=null&&r(s,t,o);return this}get(t,s){if(t=io(t),t){const o=A.findKey(this,t);if(o){const n=this[o];if(!s)return n;if(s===!0)return Nh(n);if(A.isFunction(s))return s.call(this,n,o);if(A.isRegExp(s))return s.exec(n);throw new TypeError("parser must be boolean|regexp|function")}}}has(t,s){if(t=io(t),t){const o=A.findKey(this,t);return!!(o&&this[o]!==void 0&&(!s||pr(this,this[o],o,s)))}return!1}delete(t,s){const o=this;let n=!1;function r(l){if(l=io(l),l){const a=A.findKey(o,l);a&&(!s||pr(o,o[a],a,s))&&(delete o[a],n=!0)}}return A.isArray(t)?t.forEach(r):r(t),n}clear(t){const s=Object.keys(this);let o=s.length,n=!1;for(;o--;){const r=s[o];(!t||pr(this,this[r],r,t,!0))&&(delete this[r],n=!0)}return n}normalize(t){const s=this,o={};return A.forEach(this,(n,r)=>{const l=A.findKey(o,r);if(l){s[l]=Yo(n),delete s[r];return}const a=t?Hh(r):String(r).trim();a!==r&&delete s[r],s[a]=Yo(n),o[a]=!0}),this}concat(...t){return this.constructor.concat(this,...t)}toJSON(t){const s=Object.create(null);return A.forEach(this,(o,n)=>{o!=null&&o!==!1&&(s[n]=t&&A.isArray(o)?o.join(", "):o)}),s}[Symbol.iterator](){return Object.entries(this.toJSON())[Symbol.iterator]()}toString(){return Object.entries(this.toJSON()).map(([t,s])=>t+": "+s).join(`
`)}getSetCookie(){return this.get("set-cookie")||[]}get[Symbol.toStringTag](){return"AxiosHeaders"}static from(t){return t instanceof this?t:new this(t)}static concat(t,...s){const o=new this(t);return s.forEach(n=>o.set(n)),o}static accessor(t){const o=(this[Ol]=this[Ol]={accessors:{}}).accessors,n=this.prototype;function r(l){const a=io(l);o[a]||(zh(n,l),o[a]=!0)}return A.isArray(t)?t.forEach(r):r(t),this}};ot.accessor(["Content-Type","Content-Length","Accept","Accept-Encoding","User-Agent","Authorization"]);A.reduceDescriptors(ot.prototype,({value:e},t)=>{let s=t[0].toUpperCase()+t.slice(1);return{get:()=>e,set(o){this[s]=o}}});A.freezeMethods(ot);function hr(e,t){const s=this||jo,o=t||s,n=ot.from(o.headers);let r=o.data;return A.forEach(e,function(a){r=a.call(s,r,n.normalize(),t?t.status:void 0)}),n.normalize(),r}function Gc(e){return!!(e&&e.__CANCEL__)}function Zs(e,t,s){ae.call(this,e??"canceled",ae.ERR_CANCELED,t,s),this.name="CanceledError"}A.inherits(Zs,ae,{__CANCEL__:!0});function Yc(e,t,s){const o=s.config.validateStatus;!s.status||!o||o(s.status)?e(s):t(new ae("Request failed with status code "+s.status,[ae.ERR_BAD_REQUEST,ae.ERR_BAD_RESPONSE][Math.floor(s.status/100)-4],s.config,s.request,s))}function qh(e){const t=/^([-+\w]{1,25})(:?\/\/|:)/.exec(e);return t&&t[1]||""}function Kh(e,t){e=e||10;const s=new Array(e),o=new Array(e);let n=0,r=0,l;return t=t!==void 0?t:1e3,function(f){const u=Date.now(),c=o[r];l||(l=u),s[n]=f,o[n]=u;let d=r,p=0;for(;d!==n;)p+=s[d++],d=d%e;if(n=(n+1)%e,n===r&&(r=(r+1)%e),u-l<t)return;const h=c&&u-c;return h?Math.round(p*1e3/h):void 0}}function Wh(e,t){let s=0,o=1e3/t,n,r;const l=(u,c=Date.now())=>{s=c,n=null,r&&(clearTimeout(r),r=null),e.apply(null,u)};return[(...u)=>{const c=Date.now(),d=c-s;d>=o?l(u,c):(n=u,r||(r=setTimeout(()=>{r=null,l(n)},o-d)))},()=>n&&l(n)]}const un=(e,t,s=3)=>{let o=0;const n=Kh(50,250);return Wh(r=>{const l=r.loaded,a=r.lengthComputable?r.total:void 0,f=l-o,u=n(f),c=l<=a;o=l;const d={loaded:l,total:a,progress:a?l/a:void 0,bytes:f,rate:u||void 0,estimated:u&&a&&c?(a-l)/u:void 0,event:r,lengthComputable:a!=null,[t?"download":"upload"]:!0};e(d)},s)},Il=(e,t)=>{const s=e!=null;return[o=>t[0]({lengthComputable:s,total:e,loaded:o}),t[1]]},Dl=e=>(...t)=>A.asap(()=>e(...t)),Jh=qe.hasStandardBrowserEnv?((e,t)=>s=>(s=new URL(s,qe.origin),e.protocol===s.protocol&&e.host===s.host&&(t||e.port===s.port)))(new URL(qe.origin),qe.navigator&&/(msie|trident)/i.test(qe.navigator.userAgent)):()=>!0,Gh=qe.hasStandardBrowserEnv?{write(e,t,s,o,n,r){const l=[e+"="+encodeURIComponent(t)];A.isNumber(s)&&l.push("expires="+new Date(s).toGMTString()),A.isString(o)&&l.push("path="+o),A.isString(n)&&l.push("domain="+n),r===!0&&l.push("secure"),document.cookie=l.join("; ")},read(e){const t=document.cookie.match(new RegExp("(^|;\\s*)("+e+")=([^;]*)"));return t?decodeURIComponent(t[3]):null},remove(e){this.write(e,"",Date.now()-864e5)}}:{write(){},read(){return null},remove(){}};function Yh(e){return/^([a-z][a-z\d+\-.]*:)?\/\//i.test(e)}function Zh(e,t){return t?e.replace(/\/?\/$/,"")+"/"+t.replace(/^\/+/,""):e}function Zc(e,t,s){let o=!Yh(t);return e&&(o||s==!1)?Zh(e,t):t}const Ll=e=>e instanceof ot?{...e}:e;function bs(e,t){t=t||{};const s={};function o(u,c,d,p){return A.isPlainObject(u)&&A.isPlainObject(c)?A.merge.call({caseless:p},u,c):A.isPlainObject(c)?A.merge({},c):A.isArray(c)?c.slice():c}function n(u,c,d,p){if(A.isUndefined(c)){if(!A.isUndefined(u))return o(void 0,u,d,p)}else return o(u,c,d,p)}function r(u,c){if(!A.isUndefined(c))return o(void 0,c)}function l(u,c){if(A.isUndefined(c)){if(!A.isUndefined(u))return o(void 0,u)}else return o(void 0,c)}function a(u,c,d){if(d in t)return o(u,c);if(d in e)return o(void 0,u)}const f={url:r,method:r,data:r,baseURL:l,transformRequest:l,transformResponse:l,paramsSerializer:l,timeout:l,timeoutMessage:l,withCredentials:l,withXSRFToken:l,adapter:l,responseType:l,xsrfCookieName:l,xsrfHeaderName:l,onUploadProgress:l,onDownloadProgress:l,decompress:l,maxContentLength:l,maxBodyLength:l,beforeRedirect:l,transport:l,httpAgent:l,httpsAgent:l,cancelToken:l,socketPath:l,responseEncoding:l,validateStatus:a,headers:(u,c,d)=>n(Ll(u),Ll(c),d,!0)};return A.forEach(Object.keys(Object.assign({},e,t)),function(c){const d=f[c]||n,p=d(e[c],t[c],c);A.isUndefined(p)&&d!==a||(s[c]=p)}),s}const Xc=e=>{const t=bs({},e);let{data:s,withXSRFToken:o,xsrfHeaderName:n,xsrfCookieName:r,headers:l,auth:a}=t;t.headers=l=ot.from(l),t.url=Kc(Zc(t.baseURL,t.url,t.allowAbsoluteUrls),e.params,e.paramsSerializer),a&&l.set("Authorization","Basic "+btoa((a.username||"")+":"+(a.password?unescape(encodeURIComponent(a.password)):"")));let f;if(A.isFormData(s)){if(qe.hasStandardBrowserEnv||qe.hasStandardBrowserWebWorkerEnv)l.setContentType(void 0);else if((f=l.getContentType())!==!1){const[u,...c]=f?f.split(";").map(d=>d.trim()).filter(Boolean):[];l.setContentType([u||"multipart/form-data",...c].join("; "))}}if(qe.hasStandardBrowserEnv&&(o&&A.isFunction(o)&&(o=o(t)),o||o!==!1&&Jh(t.url))){const u=n&&r&&Gh.read(r);u&&l.set(n,u)}return t},Xh=typeof XMLHttpRequest<"u",Qh=Xh&&function(e){return new Promise(function(s,o){const n=Xc(e);let r=n.data;const l=ot.from(n.headers).normalize();let{responseType:a,onUploadProgress:f,onDownloadProgress:u}=n,c,d,p,h,v;function $(){h&&h(),v&&v(),n.cancelToken&&n.cancelToken.unsubscribe(c),n.signal&&n.signal.removeEventListener("abort",c)}let y=new XMLHttpRequest;y.open(n.method.toUpperCase(),n.url,!0),y.timeout=n.timeout;function x(){if(!y)return;const b=ot.from("getAllResponseHeaders"in y&&y.getAllResponseHeaders()),L={data:!a||a==="text"||a==="json"?y.responseText:y.response,status:y.status,statusText:y.statusText,headers:b,config:e,request:y};Yc(function(j){s(j),$()},function(j){o(j),$()},L),y=null}"onloadend"in y?y.onloadend=x:y.onreadystatechange=function(){!y||y.readyState!==4||y.status===0&&!(y.responseURL&&y.responseURL.indexOf("file:")===0)||setTimeout(x)},y.onabort=function(){y&&(o(new ae("Request aborted",ae.ECONNABORTED,e,y)),y=null)},y.onerror=function(){o(new ae("Network Error",ae.ERR_NETWORK,e,y)),y=null},y.ontimeout=function(){let T=n.timeout?"timeout of "+n.timeout+"ms exceeded":"timeout exceeded";const L=n.transitional||Wc;n.timeoutErrorMessage&&(T=n.timeoutErrorMessage),o(new ae(T,L.clarifyTimeoutError?ae.ETIMEDOUT:ae.ECONNABORTED,e,y)),y=null},r===void 0&&l.setContentType(null),"setRequestHeader"in y&&A.forEach(l.toJSON(),function(T,L){y.setRequestHeader(L,T)}),A.isUndefined(n.withCredentials)||(y.withCredentials=!!n.withCredentials),a&&a!=="json"&&(y.responseType=n.responseType),u&&([p,v]=un(u,!0),y.addEventListener("progress",p)),f&&y.upload&&([d,h]=un(f),y.upload.addEventListener("progress",d),y.upload.addEventListener("loadend",h)),(n.cancelToken||n.signal)&&(c=b=>{y&&(o(!b||b.type?new Zs(null,e,y):b),y.abort(),y=null)},n.cancelToken&&n.cancelToken.subscribe(c),n.signal&&(n.signal.aborted?c():n.signal.addEventListener("abort",c)));const m=qh(n.url);if(m&&qe.protocols.indexOf(m)===-1){o(new ae("Unsupported protocol "+m+":",ae.ERR_BAD_REQUEST,e));return}y.send(r||null)})},em=(e,t)=>{const{length:s}=e=e?e.filter(Boolean):[];if(t||s){let o=new AbortController,n;const r=function(u){if(!n){n=!0,a();const c=u instanceof Error?u:this.reason;o.abort(c instanceof ae?c:new Zs(c instanceof Error?c.message:c))}};let l=t&&setTimeout(()=>{l=null,r(new ae(`timeout ${t} of ms exceeded`,ae.ETIMEDOUT))},t);const a=()=>{e&&(l&&clearTimeout(l),l=null,e.forEach(u=>{u.unsubscribe?u.unsubscribe(r):u.removeEventListener("abort",r)}),e=null)};e.forEach(u=>u.addEventListener("abort",r));const{signal:f}=o;return f.unsubscribe=()=>A.asap(a),f}},tm=function*(e,t){let s=e.byteLength;if(s<t){yield e;return}let o=0,n;for(;o<s;)n=o+t,yield e.slice(o,n),o=n},sm=async function*(e,t){for await(const s of om(e))yield*tm(s,t)},om=async function*(e){if(e[Symbol.asyncIterator]){yield*e;return}const t=e.getReader();try{for(;;){const{done:s,value:o}=await t.read();if(s)break;yield o}}finally{await t.cancel()}},Fl=(e,t,s,o)=>{const n=sm(e,t);let r=0,l,a=f=>{l||(l=!0,o&&o(f))};return new ReadableStream({async pull(f){try{const{done:u,value:c}=await n.next();if(u){a(),f.close();return}let d=c.byteLength;if(s){let p=r+=d;s(p)}f.enqueue(new Uint8Array(c))}catch(u){throw a(u),u}},cancel(f){return a(f),n.return()}},{highWaterMark:2})},zn=typeof fetch=="function"&&typeof Request=="function"&&typeof Response=="function",Qc=zn&&typeof ReadableStream=="function",nm=zn&&(typeof TextEncoder=="function"?(e=>t=>e.encode(t))(new TextEncoder):async e=>new Uint8Array(await new Response(e).arrayBuffer())),eu=(e,...t)=>{try{return!!e(...t)}catch{return!1}},rm=Qc&&eu(()=>{let e=!1;const t=new Request(qe.origin,{body:new ReadableStream,method:"POST",get duplex(){return e=!0,"half"}}).headers.has("Content-Type");return e&&!t}),Ul=64*1024,Nr=Qc&&eu(()=>A.isReadableStream(new Response("").body)),dn={stream:Nr&&(e=>e.body)};zn&&(e=>{["text","arrayBuffer","blob","formData","stream"].forEach(t=>{!dn[t]&&(dn[t]=A.isFunction(e[t])?s=>s[t]():(s,o)=>{throw new ae(`Response type '${t}' is not supported`,ae.ERR_NOT_SUPPORT,o)})})})(new Response);const im=async e=>{if(e==null)return 0;if(A.isBlob(e))return e.size;if(A.isSpecCompliantForm(e))return(await new Request(qe.origin,{method:"POST",body:e}).arrayBuffer()).byteLength;if(A.isArrayBufferView(e)||A.isArrayBuffer(e))return e.byteLength;if(A.isURLSearchParams(e)&&(e=e+""),A.isString(e))return(await nm(e)).byteLength},lm=async(e,t)=>{const s=A.toFiniteNumber(e.getContentLength());return s??im(t)},am=zn&&(async e=>{let{url:t,method:s,data:o,signal:n,cancelToken:r,timeout:l,onDownloadProgress:a,onUploadProgress:f,responseType:u,headers:c,withCredentials:d="same-origin",fetchOptions:p}=Xc(e);u=u?(u+"").toLowerCase():"text";let h=em([n,r&&r.toAbortSignal()],l),v;const $=h&&h.unsubscribe&&(()=>{h.unsubscribe()});let y;try{if(f&&rm&&s!=="get"&&s!=="head"&&(y=await lm(c,o))!==0){let L=new Request(t,{method:"POST",body:o,duplex:"half"}),J;if(A.isFormData(o)&&(J=L.headers.get("content-type"))&&c.setContentType(J),L.body){const[j,_]=Il(y,un(Dl(f)));o=Fl(L.body,Ul,j,_)}}A.isString(d)||(d=d?"include":"omit");const x="credentials"in Request.prototype;v=new Request(t,{...p,signal:h,method:s.toUpperCase(),headers:c.normalize().toJSON(),body:o,duplex:"half",credentials:x?d:void 0});let m=await fetch(v);const b=Nr&&(u==="stream"||u==="response");if(Nr&&(a||b&&$)){const L={};["status","statusText","headers"].forEach(E=>{L[E]=m[E]});const J=A.toFiniteNumber(m.headers.get("content-length")),[j,_]=a&&Il(J,un(Dl(a),!0))||[];m=new Response(Fl(m.body,Ul,j,()=>{_&&_(),$&&$()}),L)}u=u||"text";let T=await dn[A.findKey(dn,u)||"text"](m,e);return!b&&$&&$(),await new Promise((L,J)=>{Yc(L,J,{data:T,headers:ot.from(m.headers),status:m.status,statusText:m.statusText,config:e,request:v})})}catch(x){throw $&&$(),x&&x.name==="TypeError"&&/Load failed|fetch/i.test(x.message)?Object.assign(new ae("Network Error",ae.ERR_NETWORK,e,v),{cause:x.cause||x}):ae.from(x,x&&x.code,e,v)}}),Vr={http:_h,xhr:Qh,fetch:am};A.forEach(Vr,(e,t)=>{if(e){try{Object.defineProperty(e,"name",{value:t})}catch{}Object.defineProperty(e,"adapterName",{value:t})}});const Nl=e=>`- ${e}`,cm=e=>A.isFunction(e)||e===null||e===!1,tu={getAdapter:e=>{e=A.isArray(e)?e:[e];const{length:t}=e;let s,o;const n={};for(let r=0;r<t;r++){s=e[r];let l;if(o=s,!cm(s)&&(o=Vr[(l=String(s)).toLowerCase()],o===void 0))throw new ae(`Unknown adapter '${l}'`);if(o)break;n[l||"#"+r]=o}if(!o){const r=Object.entries(n).map(([a,f])=>`adapter ${a} `+(f===!1?"is not supported by the environment":"is not available in the build"));let l=t?r.length>1?`since :
`+r.map(Nl).join(`
`):" "+Nl(r[0]):"as no adapter specified";throw new ae("There is no suitable adapter to dispatch the request "+l,"ERR_NOT_SUPPORT")}return o},adapters:Vr};function mr(e){if(e.cancelToken&&e.cancelToken.throwIfRequested(),e.signal&&e.signal.aborted)throw new Zs(null,e)}function Vl(e){return mr(e),e.headers=ot.from(e.headers),e.data=hr.call(e,e.transformRequest),["post","put","patch"].indexOf(e.method)!==-1&&e.headers.setContentType("application/x-www-form-urlencoded",!1),tu.getAdapter(e.adapter||jo.adapter)(e).then(function(o){return mr(e),o.data=hr.call(e,e.transformResponse,o),o.headers=ot.from(o.headers),o},function(o){return Gc(o)||(mr(e),o&&o.response&&(o.response.data=hr.call(e,e.transformResponse,o.response),o.response.headers=ot.from(o.response.headers))),Promise.reject(o)})}const su="1.9.0",qn={};["object","boolean","number","function","string","symbol"].forEach((e,t)=>{qn[e]=function(o){return typeof o===e||"a"+(t<1?"n ":" ")+e}});const Hl={};qn.transitional=function(t,s,o){function n(r,l){return"[Axios v"+su+"] Transitional option '"+r+"'"+l+(o?". "+o:"")}return(r,l,a)=>{if(t===!1)throw new ae(n(l," has been removed"+(s?" in "+s:"")),ae.ERR_DEPRECATED);return s&&!Hl[l]&&(Hl[l]=!0,console.warn(n(l," has been deprecated since v"+s+" and will be removed in the near future"))),t?t(r,l,a):!0}};qn.spelling=function(t){return(s,o)=>(console.warn(`${o} is likely a misspelling of ${t}`),!0)};function um(e,t,s){if(typeof e!="object")throw new ae("options must be an object",ae.ERR_BAD_OPTION_VALUE);const o=Object.keys(e);let n=o.length;for(;n-- >0;){const r=o[n],l=t[r];if(l){const a=e[r],f=a===void 0||l(a,r,e);if(f!==!0)throw new ae("option "+r+" must be "+f,ae.ERR_BAD_OPTION_VALUE);continue}if(s!==!0)throw new ae("Unknown option "+r,ae.ERR_BAD_OPTION)}}const Zo={assertOptions:um,validators:qn},St=Zo.validators;let ps=class{constructor(t){this.defaults=t||{},this.interceptors={request:new Bl,response:new Bl}}async request(t,s){try{return await this._request(t,s)}catch(o){if(o instanceof Error){let n={};Error.captureStackTrace?Error.captureStackTrace(n):n=new Error;const r=n.stack?n.stack.replace(/^.+\n/,""):"";try{o.stack?r&&!String(o.stack).endsWith(r.replace(/^.+\n.+\n/,""))&&(o.stack+=`
`+r):o.stack=r}catch{}}throw o}}_request(t,s){typeof t=="string"?(s=s||{},s.url=t):s=t||{},s=bs(this.defaults,s);const{transitional:o,paramsSerializer:n,headers:r}=s;o!==void 0&&Zo.assertOptions(o,{silentJSONParsing:St.transitional(St.boolean),forcedJSONParsing:St.transitional(St.boolean),clarifyTimeoutError:St.transitional(St.boolean)},!1),n!=null&&(A.isFunction(n)?s.paramsSerializer={serialize:n}:Zo.assertOptions(n,{encode:St.function,serialize:St.function},!0)),s.allowAbsoluteUrls!==void 0||(this.defaults.allowAbsoluteUrls!==void 0?s.allowAbsoluteUrls=this.defaults.allowAbsoluteUrls:s.allowAbsoluteUrls=!0),Zo.assertOptions(s,{baseUrl:St.spelling("baseURL"),withXsrfToken:St.spelling("withXSRFToken")},!0),s.method=(s.method||this.defaults.method||"get").toLowerCase();let l=r&&A.merge(r.common,r[s.method]);r&&A.forEach(["delete","get","head","post","put","patch","common"],v=>{delete r[v]}),s.headers=ot.concat(l,r);const a=[];let f=!0;this.interceptors.request.forEach(function($){typeof $.runWhen=="function"&&$.runWhen(s)===!1||(f=f&&$.synchronous,a.unshift($.fulfilled,$.rejected))});const u=[];this.interceptors.response.forEach(function($){u.push($.fulfilled,$.rejected)});let c,d=0,p;if(!f){const v=[Vl.bind(this),void 0];for(v.unshift.apply(v,a),v.push.apply(v,u),p=v.length,c=Promise.resolve(s);d<p;)c=c.then(v[d++],v[d++]);return c}p=a.length;let h=s;for(d=0;d<p;){const v=a[d++],$=a[d++];try{h=v(h)}catch(y){$.call(this,y);break}}try{c=Vl.call(this,h)}catch(v){return Promise.reject(v)}for(d=0,p=u.length;d<p;)c=c.then(u[d++],u[d++]);return c}getUri(t){t=bs(this.defaults,t);const s=Zc(t.baseURL,t.url,t.allowAbsoluteUrls);return Kc(s,t.params,t.paramsSerializer)}};A.forEach(["delete","get","head","options"],function(t){ps.prototype[t]=function(s,o){return this.request(bs(o||{},{method:t,url:s,data:(o||{}).data}))}});A.forEach(["post","put","patch"],function(t){function s(o){return function(r,l,a){return this.request(bs(a||{},{method:t,headers:o?{"Content-Type":"multipart/form-data"}:{},url:r,data:l}))}}ps.prototype[t]=s(),ps.prototype[t+"Form"]=s(!0)});let dm=class ou{constructor(t){if(typeof t!="function")throw new TypeError("executor must be a function.");let s;this.promise=new Promise(function(r){s=r});const o=this;this.promise.then(n=>{if(!o._listeners)return;let r=o._listeners.length;for(;r-- >0;)o._listeners[r](n);o._listeners=null}),this.promise.then=n=>{let r;const l=new Promise(a=>{o.subscribe(a),r=a}).then(n);return l.cancel=function(){o.unsubscribe(r)},l},t(function(r,l,a){o.reason||(o.reason=new Zs(r,l,a),s(o.reason))})}throwIfRequested(){if(this.reason)throw this.reason}subscribe(t){if(this.reason){t(this.reason);return}this._listeners?this._listeners.push(t):this._listeners=[t]}unsubscribe(t){if(!this._listeners)return;const s=this._listeners.indexOf(t);s!==-1&&this._listeners.splice(s,1)}toAbortSignal(){const t=new AbortController,s=o=>{t.abort(o)};return this.subscribe(s),t.signal.unsubscribe=()=>this.unsubscribe(s),t.signal}static source(){let t;return{token:new ou(function(n){t=n}),cancel:t}}};function fm(e){return function(s){return e.apply(null,s)}}function pm(e){return A.isObject(e)&&e.isAxiosError===!0}const Hr={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(Hr).forEach(([e,t])=>{Hr[t]=e});function nu(e){const t=new ps(e),s=Bc(ps.prototype.request,t);return A.extend(s,ps.prototype,t,{allOwnKeys:!0}),A.extend(s,t,null,{allOwnKeys:!0}),s.create=function(n){return nu(bs(e,n))},s}const Be=nu(jo);Be.Axios=ps;Be.CanceledError=Zs;Be.CancelToken=dm;Be.isCancel=Gc;Be.VERSION=su;Be.toFormData=Hn;Be.AxiosError=ae;Be.Cancel=Be.CanceledError;Be.all=function(t){return Promise.all(t)};Be.spread=fm;Be.isAxiosError=pm;Be.mergeConfig=bs;Be.AxiosHeaders=ot;Be.formToJSON=e=>Jc(A.isHTMLForm(e)?new FormData(e):e);Be.getAdapter=tu.getAdapter;Be.HttpStatusCode=Hr;Be.default=Be;const{Axios:L3,AxiosError:F3,CanceledError:U3,isCancel:N3,CancelToken:V3,VERSION:H3,all:z3,Cancel:q3,isAxiosError:K3,spread:W3,toFormData:J3,AxiosHeaders:G3,HttpStatusCode:Y3,formToJSON:Z3,getAdapter:X3,mergeConfig:Q3}=Be,Is=Be.create({baseURL:"https://jnxprojectapi.codevision.my/api",headers:{"Content-Type":"application/json"}});Is.interceptors.request.use(e=>{const t=localStorage.getItem("token");return t&&(e.headers.Authorization=`Bearer ${t}`),e},e=>Promise.reject(e));Is.interceptors.response.use(e=>e,async e=>{var s;const t=e.config;return((s=e.response)==null?void 0:s.status)===401&&!t._retry&&(t._retry=!0,localStorage.removeItem("token"),window.location.href="/login"),Promise.reject(e)});class ue{static async get(t,s){return(await Is.get(t,s)).data}static async post(t,s,o){return(await Is.post(t,s,o)).data}static async patch(t,s,o){return(await Is.patch(t,s,o)).data}static async delete(t,s){return(await Is.delete(t,s)).data}}class zl{static async login(t){try{const s=await ue.post("/authentication",t);if(!s.token)throw new Error("No token received");return localStorage.setItem("token",s.token),localStorage.setItem("user",JSON.stringify(s.user)),s}catch(s){throw localStorage.removeItem("token"),localStorage.removeItem("user"),console.error("Login error:",s),s}}static logout(){localStorage.removeItem("token"),localStorage.removeItem("user")}static isAuthenticated(){return!!localStorage.getItem("token")}static getCurrentUser(){const t=localStorage.getItem("user");return t?JSON.parse(t):null}}class Ts{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(Ts,"baseUrl","/customers");class js{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(js,"baseUrl","/projects");class Rs{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(Rs,"baseUrl","tickets");class Ms{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(Ms,"baseUrl","/departments");class Bs{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async getByDepartment(t){return ue.get(`${this.baseUrl}/department/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(Bs,"baseUrl","/departmenttasks/populated");class Os{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}}It(Os,"baseUrl","/users");class Ut{static async getAll(t){return ue.get(this.baseUrl,{params:t})}static async getById(t){return ue.get(`${this.baseUrl}/${t}`)}static async create(t){return ue.post(this.baseUrl,t)}static async update(t,s){return ue.patch(`${this.baseUrl}/${t}`,s)}static async delete(t){return ue.delete(`${this.baseUrl}/${t}`)}static async getMyNotifications(t){return ue.get(this.baseUrl2,{params:t})}}It(Ut,"baseUrl","/notifications"),It(Ut,"baseUrl2","/mynotifications");function hm(e){return e&&e.__esModule&&Object.prototype.hasOwnProperty.call(e,"default")?e.default:e}var gr={exports:{}},wr={exports:{}},ql;function mm(){return ql||(ql=1,function(){var e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/",t={rotl:function(s,o){return s<<o|s>>>32-o},rotr:function(s,o){return s<<32-o|s>>>o},endian:function(s){if(s.constructor==Number)return t.rotl(s,8)&16711935|t.rotl(s,24)&**********;for(var o=0;o<s.length;o++)s[o]=t.endian(s[o]);return s},randomBytes:function(s){for(var o=[];s>0;s--)o.push(Math.floor(Math.random()*256));return o},bytesToWords:function(s){for(var o=[],n=0,r=0;n<s.length;n++,r+=8)o[r>>>5]|=s[n]<<24-r%32;return o},wordsToBytes:function(s){for(var o=[],n=0;n<s.length*32;n+=8)o.push(s[n>>>5]>>>24-n%32&255);return o},bytesToHex:function(s){for(var o=[],n=0;n<s.length;n++)o.push((s[n]>>>4).toString(16)),o.push((s[n]&15).toString(16));return o.join("")},hexToBytes:function(s){for(var o=[],n=0;n<s.length;n+=2)o.push(parseInt(s.substr(n,2),16));return o},bytesToBase64:function(s){for(var o=[],n=0;n<s.length;n+=3)for(var r=s[n]<<16|s[n+1]<<8|s[n+2],l=0;l<4;l++)n*8+l*6<=s.length*8?o.push(e.charAt(r>>>6*(3-l)&63)):o.push("=");return o.join("")},base64ToBytes:function(s){s=s.replace(/[^A-Z0-9+\/]/ig,"");for(var o=[],n=0,r=0;n<s.length;r=++n%4)r!=0&&o.push((e.indexOf(s.charAt(n-1))&Math.pow(2,-2*r+8)-1)<<r*2|e.indexOf(s.charAt(n))>>>6-r*2);return o}};wr.exports=t}()),wr.exports}var br,Kl;function Wl(){if(Kl)return br;Kl=1;var e={utf8:{stringToBytes:function(t){return e.bin.stringToBytes(unescape(encodeURIComponent(t)))},bytesToString:function(t){return decodeURIComponent(escape(e.bin.bytesToString(t)))}},bin:{stringToBytes:function(t){for(var s=[],o=0;o<t.length;o++)s.push(t.charCodeAt(o)&255);return s},bytesToString:function(t){for(var s=[],o=0;o<t.length;o++)s.push(String.fromCharCode(t[o]));return s.join("")}}};return br=e,br}/*!
 * Determine if an object is a Buffer
 *
 * <AUTHOR> Aboukhadijeh <https://feross.org>
 * @license  MIT
 */var vr,Jl;function gm(){if(Jl)return vr;Jl=1,vr=function(s){return s!=null&&(e(s)||t(s)||!!s._isBuffer)};function e(s){return!!s.constructor&&typeof s.constructor.isBuffer=="function"&&s.constructor.isBuffer(s)}function t(s){return typeof s.readFloatLE=="function"&&typeof s.slice=="function"&&e(s.slice(0,0))}return vr}var Gl;function wm(){return Gl||(Gl=1,function(){var e=mm(),t=Wl().utf8,s=gm(),o=Wl().bin,n=function(r,l){r.constructor==String?l&&l.encoding==="binary"?r=o.stringToBytes(r):r=t.stringToBytes(r):s(r)?r=Array.prototype.slice.call(r,0):!Array.isArray(r)&&r.constructor!==Uint8Array&&(r=r.toString());for(var a=e.bytesToWords(r),f=r.length*8,u=**********,c=-271733879,d=-**********,p=271733878,h=0;h<a.length;h++)a[h]=(a[h]<<8|a[h]>>>24)&16711935|(a[h]<<24|a[h]>>>8)&**********;a[f>>>5]|=128<<f%32,a[(f+64>>>9<<4)+14]=f;for(var v=n._ff,$=n._gg,y=n._hh,x=n._ii,h=0;h<a.length;h+=16){var m=u,b=c,T=d,L=p;u=v(u,c,d,p,a[h+0],7,-680876936),p=v(p,u,c,d,a[h+1],12,-389564586),d=v(d,p,u,c,a[h+2],17,606105819),c=v(c,d,p,u,a[h+3],22,-1044525330),u=v(u,c,d,p,a[h+4],7,-176418897),p=v(p,u,c,d,a[h+5],12,1200080426),d=v(d,p,u,c,a[h+6],17,-1473231341),c=v(c,d,p,u,a[h+7],22,-45705983),u=v(u,c,d,p,a[h+8],7,1770035416),p=v(p,u,c,d,a[h+9],12,-1958414417),d=v(d,p,u,c,a[h+10],17,-42063),c=v(c,d,p,u,a[h+11],22,-1990404162),u=v(u,c,d,p,a[h+12],7,1804603682),p=v(p,u,c,d,a[h+13],12,-40341101),d=v(d,p,u,c,a[h+14],17,-1502002290),c=v(c,d,p,u,a[h+15],22,1236535329),u=$(u,c,d,p,a[h+1],5,-165796510),p=$(p,u,c,d,a[h+6],9,-1069501632),d=$(d,p,u,c,a[h+11],14,643717713),c=$(c,d,p,u,a[h+0],20,-373897302),u=$(u,c,d,p,a[h+5],5,-701558691),p=$(p,u,c,d,a[h+10],9,38016083),d=$(d,p,u,c,a[h+15],14,-660478335),c=$(c,d,p,u,a[h+4],20,-405537848),u=$(u,c,d,p,a[h+9],5,568446438),p=$(p,u,c,d,a[h+14],9,-1019803690),d=$(d,p,u,c,a[h+3],14,-187363961),c=$(c,d,p,u,a[h+8],20,1163531501),u=$(u,c,d,p,a[h+13],5,-1444681467),p=$(p,u,c,d,a[h+2],9,-51403784),d=$(d,p,u,c,a[h+7],14,1735328473),c=$(c,d,p,u,a[h+12],20,-1926607734),u=y(u,c,d,p,a[h+5],4,-378558),p=y(p,u,c,d,a[h+8],11,-2022574463),d=y(d,p,u,c,a[h+11],16,1839030562),c=y(c,d,p,u,a[h+14],23,-35309556),u=y(u,c,d,p,a[h+1],4,-1530992060),p=y(p,u,c,d,a[h+4],11,1272893353),d=y(d,p,u,c,a[h+7],16,-155497632),c=y(c,d,p,u,a[h+10],23,-1094730640),u=y(u,c,d,p,a[h+13],4,681279174),p=y(p,u,c,d,a[h+0],11,-358537222),d=y(d,p,u,c,a[h+3],16,-722521979),c=y(c,d,p,u,a[h+6],23,76029189),u=y(u,c,d,p,a[h+9],4,-640364487),p=y(p,u,c,d,a[h+12],11,-421815835),d=y(d,p,u,c,a[h+15],16,530742520),c=y(c,d,p,u,a[h+2],23,-995338651),u=x(u,c,d,p,a[h+0],6,-198630844),p=x(p,u,c,d,a[h+7],10,1126891415),d=x(d,p,u,c,a[h+14],15,-1416354905),c=x(c,d,p,u,a[h+5],21,-57434055),u=x(u,c,d,p,a[h+12],6,1700485571),p=x(p,u,c,d,a[h+3],10,-1894986606),d=x(d,p,u,c,a[h+10],15,-1051523),c=x(c,d,p,u,a[h+1],21,-2054922799),u=x(u,c,d,p,a[h+8],6,1873313359),p=x(p,u,c,d,a[h+15],10,-30611744),d=x(d,p,u,c,a[h+6],15,-1560198380),c=x(c,d,p,u,a[h+13],21,1309151649),u=x(u,c,d,p,a[h+4],6,-145523070),p=x(p,u,c,d,a[h+11],10,-1120210379),d=x(d,p,u,c,a[h+2],15,718787259),c=x(c,d,p,u,a[h+9],21,-343485551),u=u+m>>>0,c=c+b>>>0,d=d+T>>>0,p=p+L>>>0}return e.endian([u,c,d,p])};n._ff=function(r,l,a,f,u,c,d){var p=r+(l&a|~l&f)+(u>>>0)+d;return(p<<c|p>>>32-c)+l},n._gg=function(r,l,a,f,u,c,d){var p=r+(l&f|a&~f)+(u>>>0)+d;return(p<<c|p>>>32-c)+l},n._hh=function(r,l,a,f,u,c,d){var p=r+(l^a^f)+(u>>>0)+d;return(p<<c|p>>>32-c)+l},n._ii=function(r,l,a,f,u,c,d){var p=r+(a^(l|~f))+(u>>>0)+d;return(p<<c|p>>>32-c)+l},n._blocksize=16,n._digestsize=16,gr.exports=function(r,l){if(r==null)throw new Error("Illegal argument "+r);var a=e.wordsToBytes(n(r,l));return l&&l.asBytes?a:l&&l.asString?o.bytesToString(a):e.bytesToHex(a)}}()),gr.exports}var bm=wm();const vm=hm(bm),mi=os("auth",{state:()=>({token:localStorage.getItem("token"),user:JSON.parse(localStorage.getItem("user")||"null"),loading:!1,error:null}),getters:{isAuthenticated:e=>!!e.token,currentUser:e=>e.user},actions:{async login(e){var t,s;this.loading=!0,this.error=null,this.token=null,localStorage.removeItem("token"),e.password=vm(e.password);try{const o=await zl.login(e);return this.token=o.token,this.user=o.user,o}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Login failed",this.token=null,localStorage.removeItem("token"),o}finally{this.loading=!1}},logout(){zl.logout(),this.token=null,this.user=null}}}),ym={class:"h-screen w-full flex items-center justify-center bg-gray-50 p-4"},xm={class:"max-w-md w-full bg-white rounded-xl shadow-lg overflow-hidden border border-stone-100"},km={class:"p-8"},_m={class:"space-y-2"},Cm={class:"relative"},$m={class:"space-y-2"},Em={class:"relative"},Sm={key:0,class:"bg-red-50 border-l-4 border-red-500 p-4 rounded"},Am={class:"flex"},Pm={class:"ml-3"},Tm={class:"text-sm text-red-700"},jm=["disabled"],Rm={key:0,class:"animate-spin -ml-1 mr-2 h-5 w-5 text-white",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},Mm={class:"text-base font-medium"},Bm=Pe({__name:"Login",setup(e){const t=Mc(),s=mi(),o=W(""),n=W(""),r=W(""),l=W(!1),a=async()=>{var f,u;if(!o.value||!n.value){r.value="Please fill in all fields";return}l.value=!0,r.value="";try{await s.login({email:o.value,password:n.value}),t.push("/dashboard")}catch(c){r.value=((u=(f=c.response)==null?void 0:f.data)==null?void 0:u.message)||"Invalid credentials",console.error("Login error:",c)}finally{l.value=!1}};return(f,u)=>(C(),S("div",ym,[i("div",xm,[u[9]||(u[9]=ws('<div class="bg-stone-100 px-8 py-5 border-b border-stone-200" data-v-bfb044d0><div class="flex items-center justify-center" data-v-bfb044d0><h1 class="text-2xl font-bold text-gray-800 mr-2" data-v-bfb044d0>JNX</h1><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="text-blue-500 w-6 h-6" data-v-bfb044d0><path fill-rule="evenodd" d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z" clip-rule="evenodd" data-v-bfb044d0></path></svg><h1 class="text-2xl font-bold text-gray-800 ml-2" data-v-bfb044d0>Task</h1></div></div>',1)),i("div",km,[u[8]||(u[8]=i("div",{class:"text-center mb-8"},[i("h2",{class:"text-2xl font-bold text-gray-800 mb-2"},"Welcome Back"),i("p",{class:"text-gray-500"},"Sign in to your account to continue")],-1)),i("form",{onSubmit:zt(a,["prevent"]),class:"space-y-6"},[i("div",_m,[u[3]||(u[3]=i("label",{class:"block text-sm font-medium text-gray-700"},"Email Address",-1)),i("div",Cm,[u[2]||(u[2]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{d:"M2.003 5.884L10 9.882l7.997-3.998A2 2 0 0016 4H4a2 2 0 00-1.997 1.884z"}),i("path",{d:"M18 8.118l-8 4-8-4V14a2 2 0 002 2h12a2 2 0 002-2V8.118z"})])],-1)),oe(i("input",{type:"email","onUpdate:modelValue":u[0]||(u[0]=c=>o.value=c),placeholder:"<EMAIL>",class:"pl-10 block w-full rounded-lg border border-gray-300 bg-white py-3 px-4 text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 focus:outline-none transition-all duration-200"},null,512),[[ye,o.value]])])]),i("div",$m,[u[5]||(u[5]=i("label",{class:"block text-sm font-medium text-gray-700"},"Password",-1)),i("div",Em,[u[4]||(u[4]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none z-10"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M5 9V7a5 5 0 0110 0v2a2 2 0 012 2v5a2 2 0 01-2 2H5a2 2 0 01-2-2v-5a2 2 0 012-2zm8-2v2H7V7a3 3 0 016 0z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"password","onUpdate:modelValue":u[1]||(u[1]=c=>n.value=c),placeholder:"••••••••",class:"pl-10 block w-full rounded-lg border border-gray-300 bg-white py-3 px-4 text-gray-900 shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500 focus:ring-opacity-20 focus:outline-none transition-all duration-200"},null,512),[[ye,n.value]])])]),r.value?(C(),S("div",Sm,[i("div",Am,[u[6]||(u[6]=i("div",{class:"flex-shrink-0"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-red-500",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M18 10a8 8 0 11-16 0 8 8 0 0116 0zm-7 4a1 1 0 11-2 0 1 1 0 012 0zm-1-9a1 1 0 00-1 1v4a1 1 0 102 0V6a1 1 0 00-1-1z","clip-rule":"evenodd"})])],-1)),i("div",Pm,[i("p",Tm,R(r.value),1)])])])):de("",!0),i("button",{type:"submit",class:"w-full bg-blue-600 text-white py-3 px-4 rounded-lg hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 transition-all duration-200 flex items-center justify-center shadow-md",disabled:l.value},[l.value?(C(),S("svg",Rm,u[7]||(u[7]=[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"},null,-1),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"},null,-1)]))):de("",!0),i("span",Mm,R(l.value?"Signing in...":"Sign in"),1)],8,jm)],32)])])]))}}),gi=(e,t)=>{const s=e.__vccOpts||e;for(const[o,n]of t)s[o]=n;return s},Om=gi(Bm,[["__scopeId","data-v-bfb044d0"]]),Kn=os("project",{state:()=>({projects:[],selectedProject:null,loading:!1,error:null,total:0}),getters:{getProjectById:e=>t=>e.projects.find(s=>s.id===t||s._id===t)},actions:{async fetchProjects(e){var t,s;this.loading=!0,this.error=null;try{const o=await js.getAll(e);this.projects=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch projects",console.error("Error fetching projects:",o)}finally{this.loading=!1}},async fetchProjectById(e){var t,s;this.loading=!0,this.error=null;try{const o=await js.getById(e);return this.selectedProject=o,o}catch(o){return this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch project",console.error(`Error fetching project with ID ${e}:`,o),null}finally{this.loading=!1}},async ensureProject(e){const t=this.projects.find(s=>s._id===e);if(t)return t;try{const s=await this.fetchProjectById(e);return s&&!this.projects.some(o=>o._id===e)&&this.projects.push(s),s}catch(s){return console.error(`Error ensuring project with ID ${e}:`,s),null}},async createProject(e){var t,s;this.loading=!0,this.error=null;try{return await js.create(e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create project",console.error("Error creating project:",o),o}finally{this.loading=!1}},async updateProject(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await js.update(e,t),r=this.projects.findIndex(l=>l.id===e||l._id===e);return r!==-1&&(this.projects[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update project",console.error(`Error updating project with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteProject(e){var t,s;this.loading=!0,this.error=null;try{await js.delete(e),this.projects=this.projects.filter(o=>o.id!==e&&o._id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete project",console.error(`Error deleting project with ID ${e}:`,o),o}finally{this.loading=!1}}}}),Wn=os("user",{state:()=>({users:[],selectedUser:null,loading:!1,error:null,total:0}),getters:{getUserById:e=>t=>e.users.find(s=>s.id===t||s._id===t)},actions:{async fetchUsers(e){var t,s;this.loading=!0,this.error=null;try{const o=await Os.getAll(e);this.users=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch users",console.error("Error fetching users:",o)}finally{this.loading=!1}},async fetchUserById(e){var t,s;this.loading=!0,this.error=null;try{const o=await Os.getById(e);return this.selectedUser=o||null,o}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch user",console.error(`Error fetching user with ID ${e}:`,o)}finally{this.loading=!1}},async ensureUser(e){const t=this.users.find(s=>s._id===e);if(t)return t;try{const s=await this.fetchUserById(e);return s&&!this.users.some(o=>o._id===e)&&this.users.push(s),s}catch(s){return console.error(`Error ensuring user with ID ${e}:`,s),null}},async createUser(e){var t,s;this.loading=!0,this.error=null;try{const o=await Os.create(e);return this.users.push(o),o}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create user",console.error("Error creating user:",o),o}finally{this.loading=!1}},async updateUser(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await Os.update(e,t),r=this.users.findIndex(l=>l.id===e||l._id===String(e));return r!==-1&&(this.users[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update user",console.error(`Error updating user with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteUser(e){var t,s;this.loading=!0,this.error=null;try{await Os.delete(e),this.users=this.users.filter(o=>o.id!==e&&o._id!==String(e))}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete user",console.error(`Error deleting user with ID ${e}:`,o),o}finally{this.loading=!1}}}}),Jn=os("customer",{state:()=>({customers:[],selectedCustomer:null,loading:!1,error:null,total:0}),getters:{getCustomerById:e=>t=>e.customers.find(s=>s.id===t)},actions:{async fetchCustomers(e){var t,s;this.loading=!0,this.error=null;try{const o=await Ts.getAll(e);this.customers=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch customers",console.error("Error fetching customers:",o)}finally{this.loading=!1}},async fetchCustomerById(e){var t,s;this.loading=!0,this.error=null;try{const n=(await Ts.getById(e.toString())).data||null;return this.selectedCustomer=n,n}catch(o){return this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch customer",console.error(`Error fetching customer with ID ${e}:`,o),null}finally{this.loading=!1}},async ensureCustomer(e){const t=this.customers.find(s=>s._id===e);if(t)return t;try{const s=await this.fetchCustomerById(e);return s&&!this.customers.some(o=>o._id===e)&&this.customers.push(s),s}catch(s){return console.error(`Error ensuring customer with ID ${e}:`,s),null}},async createCustomer(e){var t,s;this.loading=!0,this.error=null;try{const n=(await Ts.create(e)).data||e;return this.customers.push(n),n}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create customer",console.error("Error creating customer:",o),o}finally{this.loading=!1}},async updateCustomer(e,t){var s,o;this.loading=!0,this.error=null;try{const r=(await Ts.update(Number(e),t)).data||t,l=this.customers.findIndex(a=>a.id===e);return l!==-1&&(this.customers[l]=r),r}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update customer",console.error(`Error updating customer with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteCustomer(e){var t,s;this.loading=!0,this.error=null;try{await Ts.delete(e),this.customers=this.customers.filter(o=>o.id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete customer",console.error(`Error deleting customer with ID ${e}:`,o),o}finally{this.loading=!1}}}}),ru=os("notification",{state:()=>({notifications:[],selectedNotification:null,loading:!1,error:null,total:0}),getters:{getNotificationById:e=>t=>e.notifications.find(s=>s.id===t||s._id===t)},actions:{async fetchNotifications(e){var t,s;this.loading=!0,this.error=null;try{const o=await Ut.getAll(e);this.notifications=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch notifications",console.error("Error fetching notifications:",o)}finally{this.loading=!1}},async fetchNotificationById(e){var t,s;this.loading=!0,this.error=null;try{this.selectedNotification=await Ut.getById(e)}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch notification",console.error(`Error fetching notification with ID ${e}:`,o)}finally{this.loading=!1}},async createNotification(e){var t,s;this.loading=!0,this.error=null;try{return await Ut.create(e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create notification",console.error("Error creating notification:",o),o}finally{this.loading=!1}},async updateNotification(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await Ut.update(e,t),r=this.notifications.findIndex(l=>l.id===e||l._id===e);return r!==-1&&(this.notifications[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update notification",console.error(`Error updating notification with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteNotification(e){var t,s;this.loading=!0,this.error=null;try{await Ut.delete(e),this.notifications=this.notifications.filter(o=>o.id!==e&&o._id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete notification",console.error(`Error deleting notification with ID ${e}:`,o),o}finally{this.loading=!1}},async fetchMyNotifications(e){var t,s;this.loading=!0,this.error=null;try{const o=await Ut.getMyNotifications(e);this.notifications=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch notifications",console.error("Error fetching notifications:",o)}finally{this.loading=!1}},async markAsRead(e){var t,s;this.loading=!0,this.error=null;try{await Ut.update(e,{isRead:!0});const o=this.notifications.findIndex(n=>n.id===e||n._id===e);o!==-1&&(this.notifications[o].isRead=!0)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to mark notification as read",console.error(`Error marking notification with ID ${e} as read:`,o),o}finally{this.loading=!1}}}}),iu=os("ticket",{state:()=>({tickets:[],selectedTicket:null,loading:!1,error:null,total:0}),getters:{getTicketById:e=>t=>e.tickets.find(s=>s.id===t)},actions:{async fetchTickets(e){var t,s;this.loading=!0,this.error=null;try{const o=await Rs.getAll(e);this.tickets=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch tickets",console.error("Error fetching tickets:",o)}finally{this.loading=!1}},async fetchTicketById(e){var t,s;this.loading=!0,this.error=null;try{this.selectedTicket=await Rs.getById(e)}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch ticket",console.error(`Error fetching ticket with ID ${e}:`,o)}finally{this.loading=!1}},async createTicket(e){var t,s;this.loading=!0,this.error=null;try{const o=await Rs.create(e);return this.tickets.push(o),o}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create ticket",console.error("Error creating ticket:",o),o}finally{this.loading=!1}},async updateTicket(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await Rs.update(e,t),r=this.tickets.findIndex(l=>l.id===e);return r!==-1&&(this.tickets[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update ticket",console.error(`Error updating ticket with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteTicket(e){var t,s;this.loading=!0,this.error=null;try{await Rs.delete(e),this.tickets=this.tickets.filter(o=>o.id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete ticket",console.error(`Error deleting ticket with ID ${e}:`,o),o}finally{this.loading=!1}}}}),Im={key:0,class:"inline-flex items-center"},Dm={key:1},fn=Pe({__name:"UserName",props:{id:{type:String,default:null}},setup(e){const t=e,s=Wn(),o=W("No User"),n=W(!1),r=async()=>{if(!t.id){o.value="No User";return}n.value=!0;try{const l=await s.ensureUser(t.id);l&&l.name?o.value=l.name:o.value=`User #${t.id}`}catch(l){console.error(`Error loading user with ID ${t.id}:`,l),o.value=`User #${t.id}`}finally{n.value=!1}};return Ve(r),De(()=>t.id,r),(l,a)=>n.value?(C(),S("span",Im,a[0]||(a[0]=[i("svg",{class:"animate-spin h-4 w-4 mr-1 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),ce(" Loading... ")]))):(C(),S("span",Dm,R(o.value),1))}}),Lm={class:"p-4 md:p-6 lg:p-8"},Fm={class:"grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4 md:gap-6 mb-8"},Um={class:"bg-white rounded-lg shadow p-4 md:p-6"},Nm={class:"text-2xl md:text-3xl font-bold"},Vm={class:"bg-white rounded-lg shadow p-4 md:p-6"},Hm={class:"text-2xl md:text-3xl font-bold"},zm={class:"bg-white rounded-lg shadow p-4 md:p-6"},qm={class:"text-2xl md:text-3xl font-bold"},Km={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-8"},Wm={class:"divide-y divide-gray-200"},Jm={class:"flex-1 min-w-0"},Gm={class:"flex justify-between"},Ym={class:"text-sm font-semibold text-gray-900"},Zm={class:"text-xs text-gray-500"},Xm={class:"text-sm text-gray-500 mt-1"},Qm={class:"mt-2 flex space-x-2"},eg=["onClick"],tg={key:0,class:"ml-3 flex-shrink-0"},sg={class:"mt-4 flex justify-center"},og={class:"grid grid-cols-1 md:grid-cols-2 gap-4 md:gap-6"},ng={class:"bg-white rounded-lg shadow p-4 md:p-6"},rg={class:"grid grid-cols-2 gap-3"},ig={class:"bg-white rounded-lg shadow p-4 md:p-6"},lg={class:"space-y-3"},ag={class:"font-medium"},cg={class:"text-sm text-gray-500"},ug={key:0,class:"bg-red-100 text-red-600 text-xs font-medium px-2.5 py-0.5 rounded-full"},dg={key:1,class:"bg-green-100 text-green-600 text-xs font-medium px-2.5 py-0.5 rounded-full"},fg=Pe({__name:"Dashboard",setup(e){const t=W(1),s=Kn(),o=Y(()=>s.total),n=Y(()=>s.loading),r=Wn(),l=Y(()=>r.total),a=Y(()=>r.loading),f=Jn(),u=Y(()=>f.total),c=Y(()=>f.loading),d=ru(),p=Y(()=>d.notifications),h=Y(()=>v.tickets),v=iu(),$=j=>{j==="projects"&&cs.push("/projects"),j==="users"&&cs.push("/users"),j==="tickets"&&cs.push("/tickets"),j==="task"&&cs.push("/tasks"),j==="messages"&&cs.push("/messages")},y=async()=>{await s.fetchProjects({$limit:t.value})},x=async()=>{await r.fetchUsers({$limit:t.value})},m=async()=>{await f.fetchCustomers({$limit:t.value})},b=async()=>{await d.fetchMyNotifications({$limit:5})},T=async()=>{await v.fetchTickets({$limit:3})},L=j=>j?new Date(j).toLocaleString("en-GB",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not set",J=async j=>{await d.markAsRead(j),await b()};return Ve(async()=>{y(),x(),m(),b(),T()}),(j,_)=>(C(),S("div",Lm,[_[18]||(_[18]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Dashboard",-1)),i("div",Fm,[i("div",Um,[_[5]||(_[5]=i("h2",{class:"text-sm md:text-lg font-semibold mb-2"},"🙍🏻‍♂️ Total Users",-1)),i("p",Nm,R(a.value?"Loading...":l.value),1)]),i("div",Vm,[_[6]||(_[6]=i("h2",{class:"text-sm md:text-lg font-semibold mb-2"},"🤝 Total Customer",-1)),i("p",Hm,R(c.value?"Loading...":u.value),1)]),i("div",zm,[_[7]||(_[7]=i("h2",{class:"text-sm md:text-lg font-semibold mb-2"},"🚀 Total Projects",-1)),i("p",qm,R(n.value?"Loading...":o.value),1)])]),i("div",Km,[_[11]||(_[11]=i("div",{class:"flex justify-between items-center mb-4"},[i("h2",{class:"text-xl font-semibold text-gray-800"},"Notifications 🔔")],-1)),i("ul",Wm,[(C(!0),S(ie,null,xe(p.value,E=>(C(),S("li",{class:ne(["py-4 flex transition-colors rounded-md px-2",E!=null&&E.isRead?"":"bg-blue-50"])},[_[10]||(_[10]=ws('<div class="mr-4 flex-shrink-0"><div class="h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-blue-500" viewBox="0 0 20 20" fill="currentColor"><path d="M2 5a2 2 0 012-2h7a2 2 0 012 2v4a2 2 0 01-2 2H9l-3 3v-3H4a2 2 0 01-2-2V5z"></path><path d="M15 7v2a4 4 0 01-4 4H9.828l-1.766 1.767c.28.149.599.233.938.233h2l3 3v-3h2a2 2 0 002-2V9a2 2 0 00-2-2h-1z"></path></svg></div></div>',1)),i("div",Jm,[i("div",Gm,[i("p",Ym,[_[8]||(_[8]=ce(" New message from ")),ge(fn,{id:E==null?void 0:E.sender},null,8,["id"])]),i("span",Zm,R(L(E==null?void 0:E.createdAt)),1)]),i("p",Xm,R(E==null?void 0:E.message),1),i("div",Qm,[E!=null&&E.isRead?de("",!0):(C(),S("button",{key:0,onClick:q=>J(E==null?void 0:E._id),class:"px-3 py-1 text-xs font-medium rounded-md bg-gray-200 text-gray-600 hover:bg-gray-300"}," Mark as read ✅ ",8,eg))])]),E!=null&&E.isRead?de("",!0):(C(),S("div",tg,_[9]||(_[9]=[i("div",{class:"h-2 w-2 rounded-full bg-blue-600"},null,-1)])))],2))),256))]),i("div",sg,[i("button",{onClick:_[0]||(_[0]=E=>$("messages")),class:"text-sm text-blue-600 hover:text-blue-800 font-medium"}," View all notifications ")])]),i("div",og,[i("div",ng,[_[16]||(_[16]=i("h2",{class:"text-lg font-semibold mb-4"},"Quick Actions",-1)),i("div",rg,[i("button",{onClick:_[1]||(_[1]=E=>$("projects")),class:"bg-blue-500 hover:bg-blue-600 text-white p-3 rounded-lg flex flex-col items-center justify-center"},_[12]||(_[12]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 mb-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4v16m8-8H4"})],-1),i("span",null,"New Project",-1)])),i("button",{onClick:_[2]||(_[2]=E=>$("users")),class:"bg-green-500 hover:bg-green-600 text-white p-3 rounded-lg flex flex-col items-center justify-center"},_[13]||(_[13]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6 mb-1",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M12 4.354a4 4 0 110 5.292M15 21H3v-1a6 6 0 0112 0v1zm0 0h6v-1a6 6 0 00-9-5.197M13 7a4 4 0 11-8 0 4 4 0 018 0z"})],-1),i("span",null,"Add User",-1)])),i("button",{onClick:_[3]||(_[3]=E=>$("tickets")),class:"bg-purple-500 hover:bg-purple-600 text-white p-3 rounded-lg flex flex-col items-center justify-center"},_[14]||(_[14]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6 mb-1"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M16.5 6v.75m0 3v.75m0 3v.75m0 3V18m-9-5.25h5.25M7.5 15h3M3.375 5.25c-.621 0-1.125.504-1.125 1.125v3.026a2.999 2.999 0 0 1 0 5.198v3.026c0 .621.504 1.125 1.125 1.125h17.25c.621 0 1.125-.504 1.125-1.125v-3.026a2.999 2.999 0 0 1 0-5.198V6.375c0-.621-.504-1.125-1.125-1.125H3.375Z"})],-1),i("span",null,"Tickets",-1)])),i("button",{onClick:_[4]||(_[4]=E=>$("task")),class:"bg-yellow-500 hover:bg-yellow-600 text-white p-3 rounded-lg flex flex-col items-center justify-center"},_[15]||(_[15]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"h-6 w-6 mb-1"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"m4.5 12.75 6 6 9-13.5"})],-1),i("span",null,"Tasks",-1)]))])]),i("div",ig,[_[17]||(_[17]=i("h2",{class:"text-lg font-semibold mb-4"},"Recent Tickets",-1)),i("ul",lg,[(C(!0),S(ie,null,xe(h.value,E=>(C(),S("li",{key:E.id,class:"flex justify-between items-center p-3 bg-gray-50 shadow-sm rounded-lg"},[i("div",null,[i("p",ag,R(E.title),1),i("p",cg,R(E.createdAt?new Date(E.createdAt).toLocaleString():"No date"),1)]),E.statustxt==="open"?(C(),S("span",ug,"Urgent")):(C(),S("span",dg,"Closed"))]))),128))])])])]))}}),pg={key:0,class:"fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50"},hg={class:"bg-white rounded-lg shadow-xl max-w-md w-full mx-4 max-h-[90vh] overflow-y-auto"},mg={class:"p-6"},gg={class:"flex justify-between items-center mb-4"},wg={class:"text-xl font-semibold text-gray-900"},bg={class:"flex justify-end space-x-3 pt-4"},vg={type:"submit",class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},yg=Pe({__name:"CustomerForm",props:{show:{type:Boolean,required:!0},customer:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(e,{emit:t}){const s=e,o=t,n=W({name:"",status:!0});Ve(()=>{s.customer&&r()}),De(()=>s.customer,()=>{s.customer&&r()},{deep:!0});const r=()=>{s.customer&&(n.value={name:s.customer.name||"",status:s.customer.status==="active"})},l=()=>{n.value={name:"",status:!0}},a=()=>{s.isEdit||l(),o("close")},f=()=>{if(!n.value.name.trim()){alert("Customer name is required");return}o("save",{...n.value}),a()};return(u,c)=>e.show?(C(),S("div",pg,[i("div",hg,[i("div",mg,[i("div",gg,[i("h2",wg,R(e.isEdit?"Edit Customer":"Add New Customer"),1),i("button",{onClick:a,class:"text-gray-400 hover:text-gray-500"},c[2]||(c[2]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("form",{onSubmit:zt(f,["prevent"]),class:"space-y-4"},[i("div",null,[c[3]||(c[3]=i("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Customer Name",-1)),oe(i("input",{type:"text",id:"name","onUpdate:modelValue":c[0]||(c[0]=d=>n.value.name=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.name]])]),i("div",null,[c[5]||(c[5]=i("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Status",-1)),oe(i("select",{id:"status","onUpdate:modelValue":c[1]||(c[1]=d=>n.value.status=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},c[4]||(c[4]=[i("option",{value:!0},"Active",-1),i("option",{value:!1},"Inactive",-1)]),512),[[Ne,n.value.status]])]),i("div",bg,[i("button",{type:"button",onClick:a,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),i("button",vg,R(e.isEdit?"Update":"Save")+" Customer ",1)])],32)])])])):de("",!0)}});/*!
* sweetalert2 v11.22.0
* Released under the MIT License.
*/function lu(e,t,s){if(typeof e=="function"?e===t:e.has(t))return arguments.length<3?t:s;throw new TypeError("Private element is not present on this object")}function xg(e,t){if(t.has(e))throw new TypeError("Cannot initialize the same private elements twice on an object")}function Yl(e,t){return e.get(lu(e,t))}function kg(e,t,s){xg(e,t),t.set(e,s)}function _g(e,t,s){return e.set(lu(e,t),s),s}const Cg=100,ee={},$g=()=>{ee.previousActiveElement instanceof HTMLElement?(ee.previousActiveElement.focus(),ee.previousActiveElement=null):document.body&&document.body.focus()},Eg=e=>new Promise(t=>{if(!e)return t();const s=window.scrollX,o=window.scrollY;ee.restoreFocusTimeout=setTimeout(()=>{$g(),t()},Cg),window.scrollTo(s,o)}),au="swal2-",Sg=["container","shown","height-auto","iosfix","popup","modal","no-backdrop","no-transition","toast","toast-shown","show","hide","close","title","html-container","actions","confirm","deny","cancel","footer","icon","icon-content","image","input","file","range","select","radio","checkbox","label","textarea","inputerror","input-label","validation-message","progress-steps","active-progress-step","progress-step","progress-step-line","loader","loading","styled","top","top-start","top-end","top-left","top-right","center","center-start","center-end","center-left","center-right","bottom","bottom-start","bottom-end","bottom-left","bottom-right","grow-row","grow-column","grow-fullscreen","rtl","timer-progress-bar","timer-progress-bar-container","scrollbar-measure","icon-success","icon-warning","icon-info","icon-question","icon-error","draggable","dragging"],P=Sg.reduce((e,t)=>(e[t]=au+t,e),{}),Ag=["success","warning","info","question","error"],pn=Ag.reduce((e,t)=>(e[t]=au+t,e),{}),cu="SweetAlert2:",wi=e=>e.charAt(0).toUpperCase()+e.slice(1),Ze=e=>{console.warn(`${cu} ${typeof e=="object"?e.join(" "):e}`)},ks=e=>{console.error(`${cu} ${e}`)},Zl=[],Pg=e=>{Zl.includes(e)||(Zl.push(e),Ze(e))},uu=(e,t=null)=>{Pg(`"${e}" is deprecated and will be removed in the next major release.${t?` Use "${t}" instead.`:""}`)},Gn=e=>typeof e=="function"?e():e,bi=e=>e&&typeof e.toPromise=="function",Ro=e=>bi(e)?e.toPromise():Promise.resolve(e),vi=e=>e&&Promise.resolve(e)===e,Xe=()=>document.body.querySelector(`.${P.container}`),Mo=e=>{const t=Xe();return t?t.querySelector(e):null},dt=e=>Mo(`.${e}`),he=()=>dt(P.popup),Xs=()=>dt(P.icon),Tg=()=>dt(P["icon-content"]),du=()=>dt(P.title),yi=()=>dt(P["html-container"]),fu=()=>dt(P.image),xi=()=>dt(P["progress-steps"]),Yn=()=>dt(P["validation-message"]),Bt=()=>Mo(`.${P.actions} .${P.confirm}`),Qs=()=>Mo(`.${P.actions} .${P.cancel}`),_s=()=>Mo(`.${P.actions} .${P.deny}`),jg=()=>dt(P["input-label"]),eo=()=>Mo(`.${P.loader}`),Bo=()=>dt(P.actions),pu=()=>dt(P.footer),Zn=()=>dt(P["timer-progress-bar"]),ki=()=>dt(P.close),Rg=`
  a[href],
  area[href],
  input:not([disabled]),
  select:not([disabled]),
  textarea:not([disabled]),
  button:not([disabled]),
  iframe,
  object,
  embed,
  [tabindex="0"],
  [contenteditable],
  audio[controls],
  video[controls],
  summary
`,_i=()=>{const e=he();if(!e)return[];const t=e.querySelectorAll('[tabindex]:not([tabindex="-1"]):not([tabindex="0"])'),s=Array.from(t).sort((r,l)=>{const a=parseInt(r.getAttribute("tabindex")||"0"),f=parseInt(l.getAttribute("tabindex")||"0");return a>f?1:a<f?-1:0}),o=e.querySelectorAll(Rg),n=Array.from(o).filter(r=>r.getAttribute("tabindex")!=="-1");return[...new Set(s.concat(n))].filter(r=>nt(r))},Ci=()=>Ht(document.body,P.shown)&&!Ht(document.body,P["toast-shown"])&&!Ht(document.body,P["no-backdrop"]),Xn=()=>{const e=he();return e?Ht(e,P.toast):!1},Mg=()=>{const e=he();return e?e.hasAttribute("data-loading"):!1},ft=(e,t)=>{if(e.textContent="",t){const o=new DOMParser().parseFromString(t,"text/html"),n=o.querySelector("head");n&&Array.from(n.childNodes).forEach(l=>{e.appendChild(l)});const r=o.querySelector("body");r&&Array.from(r.childNodes).forEach(l=>{l instanceof HTMLVideoElement||l instanceof HTMLAudioElement?e.appendChild(l.cloneNode(!0)):e.appendChild(l)})}},Ht=(e,t)=>{if(!t)return!1;const s=t.split(/\s+/);for(let o=0;o<s.length;o++)if(!e.classList.contains(s[o]))return!1;return!0},Bg=(e,t)=>{Array.from(e.classList).forEach(s=>{!Object.values(P).includes(s)&&!Object.values(pn).includes(s)&&!Object.values(t.showClass||{}).includes(s)&&e.classList.remove(s)})},ut=(e,t,s)=>{if(Bg(e,t),!t.customClass)return;const o=t.customClass[s];if(o){if(typeof o!="string"&&!o.forEach){Ze(`Invalid type of customClass.${s}! Expected string or iterable object, got "${typeof o}"`);return}me(e,o)}},Qn=(e,t)=>{if(!t)return null;switch(t){case"select":case"textarea":case"file":return e.querySelector(`.${P.popup} > .${P[t]}`);case"checkbox":return e.querySelector(`.${P.popup} > .${P.checkbox} input`);case"radio":return e.querySelector(`.${P.popup} > .${P.radio} input:checked`)||e.querySelector(`.${P.popup} > .${P.radio} input:first-child`);case"range":return e.querySelector(`.${P.popup} > .${P.range} input`);default:return e.querySelector(`.${P.popup} > .${P.input}`)}},hu=e=>{if(e.focus(),e.type!=="file"){const t=e.value;e.value="",e.value=t}},mu=(e,t,s)=>{!e||!t||(typeof t=="string"&&(t=t.split(/\s+/).filter(Boolean)),t.forEach(o=>{Array.isArray(e)?e.forEach(n=>{s?n.classList.add(o):n.classList.remove(o)}):s?e.classList.add(o):e.classList.remove(o)}))},me=(e,t)=>{mu(e,t,!0)},wt=(e,t)=>{mu(e,t,!1)},Zt=(e,t)=>{const s=Array.from(e.children);for(let o=0;o<s.length;o++){const n=s[o];if(n instanceof HTMLElement&&Ht(n,t))return n}},hs=(e,t,s)=>{s===`${parseInt(s)}`&&(s=parseInt(s)),s||parseInt(s)===0?e.style.setProperty(t,typeof s=="number"?`${s}px`:s):e.style.removeProperty(t)},Le=(e,t="flex")=>{e&&(e.style.display=t)},We=e=>{e&&(e.style.display="none")},$i=(e,t="block")=>{e&&new MutationObserver(()=>{Oo(e,e.innerHTML,t)}).observe(e,{childList:!0,subtree:!0})},Xl=(e,t,s,o)=>{const n=e.querySelector(t);n&&n.style.setProperty(s,o)},Oo=(e,t,s="flex")=>{t?Le(e,s):We(e)},nt=e=>!!(e&&(e.offsetWidth||e.offsetHeight||e.getClientRects().length)),Og=()=>!nt(Bt())&&!nt(_s())&&!nt(Qs()),zr=e=>e.scrollHeight>e.clientHeight,Ig=(e,t)=>{let s=e;for(;s&&s!==t;){if(zr(s))return!0;s=s.parentElement}return!1},gu=e=>{const t=window.getComputedStyle(e),s=parseFloat(t.getPropertyValue("animation-duration")||"0"),o=parseFloat(t.getPropertyValue("transition-duration")||"0");return s>0||o>0},Ei=(e,t=!1)=>{const s=Zn();s&&nt(s)&&(t&&(s.style.transition="none",s.style.width="100%"),setTimeout(()=>{s.style.transition=`width ${e/1e3}s linear`,s.style.width="0%"},10))},Dg=()=>{const e=Zn();if(!e)return;const t=parseInt(window.getComputedStyle(e).width);e.style.removeProperty("transition"),e.style.width="100%";const s=parseInt(window.getComputedStyle(e).width),o=t/s*100;e.style.width=`${o}%`},Lg=()=>typeof window>"u"||typeof document>"u",Fg=`
 <div aria-labelledby="${P.title}" aria-describedby="${P["html-container"]}" class="${P.popup}" tabindex="-1">
   <button type="button" class="${P.close}"></button>
   <ul class="${P["progress-steps"]}"></ul>
   <div class="${P.icon}"></div>
   <img class="${P.image}" />
   <h2 class="${P.title}" id="${P.title}"></h2>
   <div class="${P["html-container"]}" id="${P["html-container"]}"></div>
   <input class="${P.input}" id="${P.input}" />
   <input type="file" class="${P.file}" />
   <div class="${P.range}">
     <input type="range" />
     <output></output>
   </div>
   <select class="${P.select}" id="${P.select}"></select>
   <div class="${P.radio}"></div>
   <label class="${P.checkbox}">
     <input type="checkbox" id="${P.checkbox}" />
     <span class="${P.label}"></span>
   </label>
   <textarea class="${P.textarea}" id="${P.textarea}"></textarea>
   <div class="${P["validation-message"]}" id="${P["validation-message"]}"></div>
   <div class="${P.actions}">
     <div class="${P.loader}"></div>
     <button type="button" class="${P.confirm}"></button>
     <button type="button" class="${P.deny}"></button>
     <button type="button" class="${P.cancel}"></button>
   </div>
   <div class="${P.footer}"></div>
   <div class="${P["timer-progress-bar-container"]}">
     <div class="${P["timer-progress-bar"]}"></div>
   </div>
 </div>
`.replace(/(^|\n)\s*/g,""),Ug=()=>{const e=Xe();return e?(e.remove(),wt([document.documentElement,document.body],[P["no-backdrop"],P["toast-shown"],P["has-column"]]),!0):!1},is=()=>{ee.currentInstance.resetValidationMessage()},Ng=()=>{const e=he(),t=Zt(e,P.input),s=Zt(e,P.file),o=e.querySelector(`.${P.range} input`),n=e.querySelector(`.${P.range} output`),r=Zt(e,P.select),l=e.querySelector(`.${P.checkbox} input`),a=Zt(e,P.textarea);t.oninput=is,s.onchange=is,r.onchange=is,l.onchange=is,a.oninput=is,o.oninput=()=>{is(),n.value=o.value},o.onchange=()=>{is(),n.value=o.value}},Vg=e=>typeof e=="string"?document.querySelector(e):e,Hg=e=>{const t=he();t.setAttribute("role",e.toast?"alert":"dialog"),t.setAttribute("aria-live",e.toast?"polite":"assertive"),e.toast||t.setAttribute("aria-modal","true")},zg=e=>{window.getComputedStyle(e).direction==="rtl"&&me(Xe(),P.rtl)},qg=e=>{const t=Ug();if(Lg()){ks("SweetAlert2 requires document to initialize");return}const s=document.createElement("div");s.className=P.container,t&&me(s,P["no-transition"]),ft(s,Fg),s.dataset.swal2Theme=e.theme;const o=Vg(e.target);o.appendChild(s),e.topLayer&&(s.setAttribute("popover",""),s.showPopover()),Hg(e),zg(o),Ng()},Si=(e,t)=>{e instanceof HTMLElement?t.appendChild(e):typeof e=="object"?Kg(e,t):e&&ft(t,e)},Kg=(e,t)=>{e.jquery?Wg(t,e):ft(t,e.toString())},Wg=(e,t)=>{if(e.textContent="",0 in t)for(let s=0;s in t;s++)e.appendChild(t[s].cloneNode(!0));else e.appendChild(t.cloneNode(!0))},Jg=(e,t)=>{const s=Bo(),o=eo();!s||!o||(!t.showConfirmButton&&!t.showDenyButton&&!t.showCancelButton?We(s):Le(s),ut(s,t,"actions"),Gg(s,o,t),ft(o,t.loaderHtml||""),ut(o,t,"loader"))};function Gg(e,t,s){const o=Bt(),n=_s(),r=Qs();!o||!n||!r||(xr(o,"confirm",s),xr(n,"deny",s),xr(r,"cancel",s),Yg(o,n,r,s),s.reverseButtons&&(s.toast?(e.insertBefore(r,o),e.insertBefore(n,o)):(e.insertBefore(r,t),e.insertBefore(n,t),e.insertBefore(o,t))))}function Yg(e,t,s,o){if(!o.buttonsStyling){wt([e,t,s],P.styled);return}me([e,t,s],P.styled),o.confirmButtonColor&&e.style.setProperty("--swal2-confirm-button-background-color",o.confirmButtonColor),o.denyButtonColor&&t.style.setProperty("--swal2-deny-button-background-color",o.denyButtonColor),o.cancelButtonColor&&s.style.setProperty("--swal2-cancel-button-background-color",o.cancelButtonColor),yr(e),yr(t),yr(s)}function yr(e){const t=window.getComputedStyle(e);if(t.getPropertyValue("--swal2-action-button-focus-box-shadow"))return;const s=t.backgroundColor.replace(/rgba?\((\d+), (\d+), (\d+).*/,"rgba($1, $2, $3, 0.5)");e.style.setProperty("--swal2-action-button-focus-box-shadow",t.getPropertyValue("--swal2-outline").replace(/ rgba\(.*/,` ${s}`))}function xr(e,t,s){const o=wi(t);Oo(e,s[`show${o}Button`],"inline-block"),ft(e,s[`${t}ButtonText`]||""),e.setAttribute("aria-label",s[`${t}ButtonAriaLabel`]||""),e.className=P[t],ut(e,s,`${t}Button`)}const Zg=(e,t)=>{const s=ki();s&&(ft(s,t.closeButtonHtml||""),ut(s,t,"closeButton"),Oo(s,t.showCloseButton),s.setAttribute("aria-label",t.closeButtonAriaLabel||""))},Xg=(e,t)=>{const s=Xe();s&&(Qg(s,t.backdrop),ew(s,t.position),tw(s,t.grow),ut(s,t,"container"))};function Qg(e,t){typeof t=="string"?e.style.background=t:t||me([document.documentElement,document.body],P["no-backdrop"])}function ew(e,t){t&&(t in P?me(e,P[t]):(Ze('The "position" parameter is not valid, defaulting to "center"'),me(e,P.center)))}function tw(e,t){t&&me(e,P[`grow-${t}`])}var Ae={innerParams:new WeakMap,domCache:new WeakMap};const sw=["input","file","range","select","radio","checkbox","textarea"],ow=(e,t)=>{const s=he();if(!s)return;const o=Ae.innerParams.get(e),n=!o||t.input!==o.input;sw.forEach(r=>{const l=Zt(s,P[r]);l&&(iw(r,t.inputAttributes),l.className=P[r],n&&We(l))}),t.input&&(n&&nw(t),lw(t))},nw=e=>{if(!e.input)return;if(!Re[e.input]){ks(`Unexpected type of input! Expected ${Object.keys(Re).join(" | ")}, got "${e.input}"`);return}const t=wu(e.input);if(!t)return;const s=Re[e.input](t,e);Le(t),e.inputAutoFocus&&setTimeout(()=>{hu(s)})},rw=e=>{for(let t=0;t<e.attributes.length;t++){const s=e.attributes[t].name;["id","type","value","style"].includes(s)||e.removeAttribute(s)}},iw=(e,t)=>{const s=he();if(!s)return;const o=Qn(s,e);if(o){rw(o);for(const n in t)o.setAttribute(n,t[n])}},lw=e=>{if(!e.input)return;const t=wu(e.input);t&&ut(t,e,"input")},Ai=(e,t)=>{!e.placeholder&&t.inputPlaceholder&&(e.placeholder=t.inputPlaceholder)},Io=(e,t,s)=>{if(s.inputLabel){const o=document.createElement("label"),n=P["input-label"];o.setAttribute("for",e.id),o.className=n,typeof s.customClass=="object"&&me(o,s.customClass.inputLabel),o.innerText=s.inputLabel,t.insertAdjacentElement("beforebegin",o)}},wu=e=>{const t=he();if(t)return Zt(t,P[e]||P.input)},hn=(e,t)=>{["string","number"].includes(typeof t)?e.value=`${t}`:vi(t)||Ze(`Unexpected type of inputValue! Expected "string", "number" or "Promise", got "${typeof t}"`)},Re={};Re.text=Re.email=Re.password=Re.number=Re.tel=Re.url=Re.search=Re.date=Re["datetime-local"]=Re.time=Re.week=Re.month=(e,t)=>(hn(e,t.inputValue),Io(e,e,t),Ai(e,t),e.type=t.input,e);Re.file=(e,t)=>(Io(e,e,t),Ai(e,t),e);Re.range=(e,t)=>{const s=e.querySelector("input"),o=e.querySelector("output");return hn(s,t.inputValue),s.type=t.input,hn(o,t.inputValue),Io(s,e,t),e};Re.select=(e,t)=>{if(e.textContent="",t.inputPlaceholder){const s=document.createElement("option");ft(s,t.inputPlaceholder),s.value="",s.disabled=!0,s.selected=!0,e.appendChild(s)}return Io(e,e,t),e};Re.radio=e=>(e.textContent="",e);Re.checkbox=(e,t)=>{const s=Qn(he(),"checkbox");s.value="1",s.checked=!!t.inputValue;const o=e.querySelector("span");return ft(o,t.inputPlaceholder||t.inputLabel),s};Re.textarea=(e,t)=>{hn(e,t.inputValue),Ai(e,t),Io(e,e,t);const s=o=>parseInt(window.getComputedStyle(o).marginLeft)+parseInt(window.getComputedStyle(o).marginRight);return setTimeout(()=>{if("MutationObserver"in window){const o=parseInt(window.getComputedStyle(he()).width),n=()=>{if(!document.body.contains(e))return;const r=e.offsetWidth+s(e);r>o?he().style.width=`${r}px`:hs(he(),"width",t.width)};new MutationObserver(n).observe(e,{attributes:!0,attributeFilter:["style"]})}}),e};const aw=(e,t)=>{const s=yi();s&&($i(s),ut(s,t,"htmlContainer"),t.html?(Si(t.html,s),Le(s,"block")):t.text?(s.textContent=t.text,Le(s,"block")):We(s),ow(e,t))},cw=(e,t)=>{const s=pu();s&&($i(s),Oo(s,t.footer,"block"),t.footer&&Si(t.footer,s),ut(s,t,"footer"))},uw=(e,t)=>{const s=Ae.innerParams.get(e),o=Xs();if(!o)return;if(s&&t.icon===s.icon){ea(o,t),Ql(o,t);return}if(!t.icon&&!t.iconHtml){We(o);return}if(t.icon&&Object.keys(pn).indexOf(t.icon)===-1){ks(`Unknown icon! Expected "success", "error", "warning", "info" or "question", got "${t.icon}"`),We(o);return}Le(o),ea(o,t),Ql(o,t),me(o,t.showClass&&t.showClass.icon),window.matchMedia("(prefers-color-scheme: dark)").addEventListener("change",bu)},Ql=(e,t)=>{for(const[s,o]of Object.entries(pn))t.icon!==s&&wt(e,o);me(e,t.icon&&pn[t.icon]),pw(e,t),bu(),ut(e,t,"icon")},bu=()=>{const e=he();if(!e)return;const t=window.getComputedStyle(e).getPropertyValue("background-color"),s=e.querySelectorAll("[class^=swal2-success-circular-line], .swal2-success-fix");for(let o=0;o<s.length;o++)s[o].style.backgroundColor=t},dw=`
  <div class="swal2-success-circular-line-left"></div>
  <span class="swal2-success-line-tip"></span> <span class="swal2-success-line-long"></span>
  <div class="swal2-success-ring"></div> <div class="swal2-success-fix"></div>
  <div class="swal2-success-circular-line-right"></div>
`,fw=`
  <span class="swal2-x-mark">
    <span class="swal2-x-mark-line-left"></span>
    <span class="swal2-x-mark-line-right"></span>
  </span>
`,ea=(e,t)=>{if(!t.icon&&!t.iconHtml)return;let s=e.innerHTML,o="";t.iconHtml?o=ta(t.iconHtml):t.icon==="success"?(o=dw,s=s.replace(/ style=".*?"/g,"")):t.icon==="error"?o=fw:t.icon&&(o=ta({question:"?",warning:"!",info:"i"}[t.icon])),s.trim()!==o.trim()&&ft(e,o)},pw=(e,t)=>{if(t.iconColor){e.style.color=t.iconColor,e.style.borderColor=t.iconColor;for(const s of[".swal2-success-line-tip",".swal2-success-line-long",".swal2-x-mark-line-left",".swal2-x-mark-line-right"])Xl(e,s,"background-color",t.iconColor);Xl(e,".swal2-success-ring","border-color",t.iconColor)}},ta=e=>`<div class="${P["icon-content"]}">${e}</div>`,hw=(e,t)=>{const s=fu();if(s){if(!t.imageUrl){We(s);return}Le(s,""),s.setAttribute("src",t.imageUrl),s.setAttribute("alt",t.imageAlt||""),hs(s,"width",t.imageWidth),hs(s,"height",t.imageHeight),s.className=P.image,ut(s,t,"image")}};let Pi=!1,vu=0,yu=0,xu=0,ku=0;const mw=e=>{e.addEventListener("mousedown",mn),document.body.addEventListener("mousemove",gn),e.addEventListener("mouseup",wn),e.addEventListener("touchstart",mn),document.body.addEventListener("touchmove",gn),e.addEventListener("touchend",wn)},gw=e=>{e.removeEventListener("mousedown",mn),document.body.removeEventListener("mousemove",gn),e.removeEventListener("mouseup",wn),e.removeEventListener("touchstart",mn),document.body.removeEventListener("touchmove",gn),e.removeEventListener("touchend",wn)},mn=e=>{const t=he();if(e.target===t||Xs().contains(e.target)){Pi=!0;const s=_u(e);vu=s.clientX,yu=s.clientY,xu=parseInt(t.style.insetInlineStart)||0,ku=parseInt(t.style.insetBlockStart)||0,me(t,"swal2-dragging")}},gn=e=>{const t=he();if(Pi){let{clientX:s,clientY:o}=_u(e);t.style.insetInlineStart=`${xu+(s-vu)}px`,t.style.insetBlockStart=`${ku+(o-yu)}px`}},wn=()=>{const e=he();Pi=!1,wt(e,"swal2-dragging")},_u=e=>{let t=0,s=0;return e.type.startsWith("mouse")?(t=e.clientX,s=e.clientY):e.type.startsWith("touch")&&(t=e.touches[0].clientX,s=e.touches[0].clientY),{clientX:t,clientY:s}},ww=(e,t)=>{const s=Xe(),o=he();if(!(!s||!o)){if(t.toast){hs(s,"width",t.width),o.style.width="100%";const n=eo();n&&o.insertBefore(n,Xs())}else hs(o,"width",t.width);hs(o,"padding",t.padding),t.color&&(o.style.color=t.color),t.background&&(o.style.background=t.background),We(Yn()),bw(o,t),t.draggable&&!t.toast?(me(o,P.draggable),mw(o)):(wt(o,P.draggable),gw(o))}},bw=(e,t)=>{const s=t.showClass||{};e.className=`${P.popup} ${nt(e)?s.popup:""}`,t.toast?(me([document.documentElement,document.body],P["toast-shown"]),me(e,P.toast)):me(e,P.modal),ut(e,t,"popup"),typeof t.customClass=="string"&&me(e,t.customClass),t.icon&&me(e,P[`icon-${t.icon}`])},vw=(e,t)=>{const s=xi();if(!s)return;const{progressSteps:o,currentProgressStep:n}=t;if(!o||o.length===0||n===void 0){We(s);return}Le(s),s.textContent="",n>=o.length&&Ze("Invalid currentProgressStep parameter, it should be less than progressSteps.length (currentProgressStep like JS arrays starts from 0)"),o.forEach((r,l)=>{const a=yw(r);if(s.appendChild(a),l===n&&me(a,P["active-progress-step"]),l!==o.length-1){const f=xw(t);s.appendChild(f)}})},yw=e=>{const t=document.createElement("li");return me(t,P["progress-step"]),ft(t,e),t},xw=e=>{const t=document.createElement("li");return me(t,P["progress-step-line"]),e.progressStepsDistance&&hs(t,"width",e.progressStepsDistance),t},kw=(e,t)=>{const s=du();s&&($i(s),Oo(s,t.title||t.titleText,"block"),t.title&&Si(t.title,s),t.titleText&&(s.innerText=t.titleText),ut(s,t,"title"))},Cu=(e,t)=>{ww(e,t),Xg(e,t),vw(e,t),uw(e,t),hw(e,t),kw(e,t),Zg(e,t),aw(e,t),Jg(e,t),cw(e,t);const s=he();typeof t.didRender=="function"&&s&&t.didRender(s),ee.eventEmitter.emit("didRender",s)},_w=()=>nt(he()),$u=()=>{var e;return(e=Bt())===null||e===void 0?void 0:e.click()},Cw=()=>{var e;return(e=_s())===null||e===void 0?void 0:e.click()},$w=()=>{var e;return(e=Qs())===null||e===void 0?void 0:e.click()},to=Object.freeze({cancel:"cancel",backdrop:"backdrop",close:"close",esc:"esc",timer:"timer"}),Eu=e=>{e.keydownTarget&&e.keydownHandlerAdded&&(e.keydownTarget.removeEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!1)},Ew=(e,t,s)=>{Eu(e),t.toast||(e.keydownHandler=o=>Aw(t,o,s),e.keydownTarget=t.keydownListenerCapture?window:he(),e.keydownListenerCapture=t.keydownListenerCapture,e.keydownTarget.addEventListener("keydown",e.keydownHandler,{capture:e.keydownListenerCapture}),e.keydownHandlerAdded=!0)},qr=(e,t)=>{var s;const o=_i();if(o.length){e=e+t,e===-2&&(e=o.length-1),e===o.length?e=0:e===-1&&(e=o.length-1),o[e].focus();return}(s=he())===null||s===void 0||s.focus()},Su=["ArrowRight","ArrowDown"],Sw=["ArrowLeft","ArrowUp"],Aw=(e,t,s)=>{e&&(t.isComposing||t.keyCode===229||(e.stopKeydownPropagation&&t.stopPropagation(),t.key==="Enter"?Pw(t,e):t.key==="Tab"?Tw(t):[...Su,...Sw].includes(t.key)?jw(t.key):t.key==="Escape"&&Rw(t,e,s)))},Pw=(e,t)=>{if(!Gn(t.allowEnterKey))return;const s=Qn(he(),t.input);if(e.target&&s&&e.target instanceof HTMLElement&&e.target.outerHTML===s.outerHTML){if(["textarea","file"].includes(t.input))return;$u(),e.preventDefault()}},Tw=e=>{const t=e.target,s=_i();let o=-1;for(let n=0;n<s.length;n++)if(t===s[n]){o=n;break}e.shiftKey?qr(o,-1):qr(o,1),e.stopPropagation(),e.preventDefault()},jw=e=>{const t=Bo(),s=Bt(),o=_s(),n=Qs();if(!t||!s||!o||!n)return;const r=[s,o,n];if(document.activeElement instanceof HTMLElement&&!r.includes(document.activeElement))return;const l=Su.includes(e)?"nextElementSibling":"previousElementSibling";let a=document.activeElement;if(a){for(let f=0;f<t.children.length;f++){if(a=a[l],!a)return;if(a instanceof HTMLButtonElement&&nt(a))break}a instanceof HTMLButtonElement&&a.focus()}},Rw=(e,t,s)=>{Gn(t.allowEscapeKey)&&(e.preventDefault(),s(to.esc))};var Js={swalPromiseResolve:new WeakMap,swalPromiseReject:new WeakMap};const Mw=()=>{const e=Xe();Array.from(document.body.children).forEach(s=>{s.contains(e)||(s.hasAttribute("aria-hidden")&&s.setAttribute("data-previous-aria-hidden",s.getAttribute("aria-hidden")||""),s.setAttribute("aria-hidden","true"))})},Au=()=>{Array.from(document.body.children).forEach(t=>{t.hasAttribute("data-previous-aria-hidden")?(t.setAttribute("aria-hidden",t.getAttribute("data-previous-aria-hidden")||""),t.removeAttribute("data-previous-aria-hidden")):t.removeAttribute("aria-hidden")})},Pu=typeof window<"u"&&!!window.GestureEvent,Bw=()=>{if(Pu&&!Ht(document.body,P.iosfix)){const e=document.body.scrollTop;document.body.style.top=`${e*-1}px`,me(document.body,P.iosfix),Ow()}},Ow=()=>{const e=Xe();if(!e)return;let t;e.ontouchstart=s=>{t=Iw(s)},e.ontouchmove=s=>{t&&(s.preventDefault(),s.stopPropagation())}},Iw=e=>{const t=e.target,s=Xe(),o=yi();return!s||!o||Dw(e)||Lw(e)?!1:t===s||!zr(s)&&t instanceof HTMLElement&&!Ig(t,o)&&t.tagName!=="INPUT"&&t.tagName!=="TEXTAREA"&&!(zr(o)&&o.contains(t))},Dw=e=>e.touches&&e.touches.length&&e.touches[0].touchType==="stylus",Lw=e=>e.touches&&e.touches.length>1,Fw=()=>{if(Ht(document.body,P.iosfix)){const e=parseInt(document.body.style.top,10);wt(document.body,P.iosfix),document.body.style.top="",document.body.scrollTop=e*-1}},Uw=()=>{const e=document.createElement("div");e.className=P["scrollbar-measure"],document.body.appendChild(e);const t=e.getBoundingClientRect().width-e.clientWidth;return document.body.removeChild(e),t};let Vs=null;const Nw=e=>{Vs===null&&(document.body.scrollHeight>window.innerHeight||e==="scroll")&&(Vs=parseInt(window.getComputedStyle(document.body).getPropertyValue("padding-right")),document.body.style.paddingRight=`${Vs+Uw()}px`)},Vw=()=>{Vs!==null&&(document.body.style.paddingRight=`${Vs}px`,Vs=null)};function Tu(e,t,s,o){Xn()?sa(e,o):(Eg(s).then(()=>sa(e,o)),Eu(ee)),Pu?(t.setAttribute("style","display:none !important"),t.removeAttribute("class"),t.innerHTML=""):t.remove(),Ci()&&(Vw(),Fw(),Au()),Hw()}function Hw(){wt([document.documentElement,document.body],[P.shown,P["height-auto"],P["no-backdrop"],P["toast-shown"]])}function Xt(e){e=qw(e);const t=Js.swalPromiseResolve.get(this),s=zw(this);this.isAwaitingPromise?e.isDismissed||(Do(this),t(e)):s&&t(e)}const zw=e=>{const t=he();if(!t)return!1;const s=Ae.innerParams.get(e);if(!s||Ht(t,s.hideClass.popup))return!1;wt(t,s.showClass.popup),me(t,s.hideClass.popup);const o=Xe();return wt(o,s.showClass.backdrop),me(o,s.hideClass.backdrop),Kw(e,t,s),!0};function ju(e){const t=Js.swalPromiseReject.get(this);Do(this),t&&t(e)}const Do=e=>{e.isAwaitingPromise&&(delete e.isAwaitingPromise,Ae.innerParams.get(e)||e._destroy())},qw=e=>typeof e>"u"?{isConfirmed:!1,isDenied:!1,isDismissed:!0}:Object.assign({isConfirmed:!1,isDenied:!1,isDismissed:!1},e),Kw=(e,t,s)=>{var o;const n=Xe(),r=gu(t);typeof s.willClose=="function"&&s.willClose(t),(o=ee.eventEmitter)===null||o===void 0||o.emit("willClose",t),r?Ww(e,t,n,s.returnFocus,s.didClose):Tu(e,n,s.returnFocus,s.didClose)},Ww=(e,t,s,o,n)=>{ee.swalCloseEventFinishedCallback=Tu.bind(null,e,s,o,n);const r=function(l){if(l.target===t){var a;(a=ee.swalCloseEventFinishedCallback)===null||a===void 0||a.call(ee),delete ee.swalCloseEventFinishedCallback,t.removeEventListener("animationend",r),t.removeEventListener("transitionend",r)}};t.addEventListener("animationend",r),t.addEventListener("transitionend",r)},sa=(e,t)=>{setTimeout(()=>{var s;typeof t=="function"&&t.bind(e.params)(),(s=ee.eventEmitter)===null||s===void 0||s.emit("didClose"),e._destroy&&e._destroy()})},Gs=e=>{let t=he();if(t||new ys,t=he(),!t)return;const s=eo();Xn()?We(Xs()):Jw(t,e),Le(s),t.setAttribute("data-loading","true"),t.setAttribute("aria-busy","true"),t.focus()},Jw=(e,t)=>{const s=Bo(),o=eo();!s||!o||(!t&&nt(Bt())&&(t=Bt()),Le(s),t&&(We(t),o.setAttribute("data-button-to-replace",t.className),s.insertBefore(o,t)),me([e,s],P.loading))},Gw=(e,t)=>{t.input==="select"||t.input==="radio"?e1(e,t):["text","email","number","tel","textarea"].some(s=>s===t.input)&&(bi(t.inputValue)||vi(t.inputValue))&&(Gs(Bt()),t1(e,t))},Yw=(e,t)=>{const s=e.getInput();if(!s)return null;switch(t.input){case"checkbox":return Zw(s);case"radio":return Xw(s);case"file":return Qw(s);default:return t.inputAutoTrim?s.value.trim():s.value}},Zw=e=>e.checked?1:0,Xw=e=>e.checked?e.value:null,Qw=e=>e.files&&e.files.length?e.getAttribute("multiple")!==null?e.files:e.files[0]:null,e1=(e,t)=>{const s=he();if(!s)return;const o=n=>{t.input==="select"?s1(s,bn(n),t):t.input==="radio"&&o1(s,bn(n),t)};bi(t.inputOptions)||vi(t.inputOptions)?(Gs(Bt()),Ro(t.inputOptions).then(n=>{e.hideLoading(),o(n)})):typeof t.inputOptions=="object"?o(t.inputOptions):ks(`Unexpected type of inputOptions! Expected object, Map or Promise, got ${typeof t.inputOptions}`)},t1=(e,t)=>{const s=e.getInput();s&&(We(s),Ro(t.inputValue).then(o=>{s.value=t.input==="number"?`${parseFloat(o)||0}`:`${o}`,Le(s),s.focus(),e.hideLoading()}).catch(o=>{ks(`Error in inputValue promise: ${o}`),s.value="",Le(s),s.focus(),e.hideLoading()}))};function s1(e,t,s){const o=Zt(e,P.select);if(!o)return;const n=(r,l,a)=>{const f=document.createElement("option");f.value=a,ft(f,l),f.selected=Ru(a,s.inputValue),r.appendChild(f)};t.forEach(r=>{const l=r[0],a=r[1];if(Array.isArray(a)){const f=document.createElement("optgroup");f.label=l,f.disabled=!1,o.appendChild(f),a.forEach(u=>n(f,u[1],u[0]))}else n(o,a,l)}),o.focus()}function o1(e,t,s){const o=Zt(e,P.radio);if(!o)return;t.forEach(r=>{const l=r[0],a=r[1],f=document.createElement("input"),u=document.createElement("label");f.type="radio",f.name=P.radio,f.value=l,Ru(l,s.inputValue)&&(f.checked=!0);const c=document.createElement("span");ft(c,a),c.className=P.label,u.appendChild(f),u.appendChild(c),o.appendChild(u)});const n=o.querySelectorAll("input");n.length&&n[0].focus()}const bn=e=>{const t=[];return e instanceof Map?e.forEach((s,o)=>{let n=s;typeof n=="object"&&(n=bn(n)),t.push([o,n])}):Object.keys(e).forEach(s=>{let o=e[s];typeof o=="object"&&(o=bn(o)),t.push([s,o])}),t},Ru=(e,t)=>!!t&&t.toString()===e.toString(),n1=e=>{const t=Ae.innerParams.get(e);e.disableButtons(),t.input?Mu(e,"confirm"):ji(e,!0)},r1=e=>{const t=Ae.innerParams.get(e);e.disableButtons(),t.returnInputValueOnDeny?Mu(e,"deny"):Ti(e,!1)},i1=(e,t)=>{e.disableButtons(),t(to.cancel)},Mu=(e,t)=>{const s=Ae.innerParams.get(e);if(!s.input){ks(`The "input" parameter is needed to be set when using returnInputValueOn${wi(t)}`);return}const o=e.getInput(),n=Yw(e,s);s.inputValidator?l1(e,n,t):o&&!o.checkValidity()?(e.enableButtons(),e.showValidationMessage(s.validationMessage||o.validationMessage)):t==="deny"?Ti(e,n):ji(e,n)},l1=(e,t,s)=>{const o=Ae.innerParams.get(e);e.disableInput(),Promise.resolve().then(()=>Ro(o.inputValidator(t,o.validationMessage))).then(r=>{e.enableButtons(),e.enableInput(),r?e.showValidationMessage(r):s==="deny"?Ti(e,t):ji(e,t)})},Ti=(e,t)=>{const s=Ae.innerParams.get(e||void 0);s.showLoaderOnDeny&&Gs(_s()),s.preDeny?(e.isAwaitingPromise=!0,Promise.resolve().then(()=>Ro(s.preDeny(t,s.validationMessage))).then(n=>{n===!1?(e.hideLoading(),Do(e)):e.close({isDenied:!0,value:typeof n>"u"?t:n})}).catch(n=>Bu(e||void 0,n))):e.close({isDenied:!0,value:t})},oa=(e,t)=>{e.close({isConfirmed:!0,value:t})},Bu=(e,t)=>{e.rejectPromise(t)},ji=(e,t)=>{const s=Ae.innerParams.get(e||void 0);s.showLoaderOnConfirm&&Gs(),s.preConfirm?(e.resetValidationMessage(),e.isAwaitingPromise=!0,Promise.resolve().then(()=>Ro(s.preConfirm(t,s.validationMessage))).then(n=>{nt(Yn())||n===!1?(e.hideLoading(),Do(e)):oa(e,typeof n>"u"?t:n)}).catch(n=>Bu(e||void 0,n))):oa(e,t)};function vn(){const e=Ae.innerParams.get(this);if(!e)return;const t=Ae.domCache.get(this);We(t.loader),Xn()?e.icon&&Le(Xs()):a1(t),wt([t.popup,t.actions],P.loading),t.popup.removeAttribute("aria-busy"),t.popup.removeAttribute("data-loading"),t.confirmButton.disabled=!1,t.denyButton.disabled=!1,t.cancelButton.disabled=!1}const a1=e=>{const t=e.popup.getElementsByClassName(e.loader.getAttribute("data-button-to-replace"));t.length?Le(t[0],"inline-block"):Og()&&We(e.actions)};function Ou(){const e=Ae.innerParams.get(this),t=Ae.domCache.get(this);return t?Qn(t.popup,e.input):null}function Iu(e,t,s){const o=Ae.domCache.get(e);t.forEach(n=>{o[n].disabled=s})}function Du(e,t){const s=he();if(!(!s||!e))if(e.type==="radio"){const o=s.querySelectorAll(`[name="${P.radio}"]`);for(let n=0;n<o.length;n++)o[n].disabled=t}else e.disabled=t}function Lu(){Iu(this,["confirmButton","denyButton","cancelButton"],!1)}function Fu(){Iu(this,["confirmButton","denyButton","cancelButton"],!0)}function Uu(){Du(this.getInput(),!1)}function Nu(){Du(this.getInput(),!0)}function Vu(e){const t=Ae.domCache.get(this),s=Ae.innerParams.get(this);ft(t.validationMessage,e),t.validationMessage.className=P["validation-message"],s.customClass&&s.customClass.validationMessage&&me(t.validationMessage,s.customClass.validationMessage),Le(t.validationMessage);const o=this.getInput();o&&(o.setAttribute("aria-invalid","true"),o.setAttribute("aria-describedby",P["validation-message"]),hu(o),me(o,P.inputerror))}function Hu(){const e=Ae.domCache.get(this);e.validationMessage&&We(e.validationMessage);const t=this.getInput();t&&(t.removeAttribute("aria-invalid"),t.removeAttribute("aria-describedby"),wt(t,P.inputerror))}const Hs={title:"",titleText:"",text:"",html:"",footer:"",icon:void 0,iconColor:void 0,iconHtml:void 0,template:void 0,toast:!1,draggable:!1,animation:!0,theme:"light",showClass:{popup:"swal2-show",backdrop:"swal2-backdrop-show",icon:"swal2-icon-show"},hideClass:{popup:"swal2-hide",backdrop:"swal2-backdrop-hide",icon:"swal2-icon-hide"},customClass:{},target:"body",color:void 0,backdrop:!0,heightAuto:!0,allowOutsideClick:!0,allowEscapeKey:!0,allowEnterKey:!0,stopKeydownPropagation:!0,keydownListenerCapture:!1,showConfirmButton:!0,showDenyButton:!1,showCancelButton:!1,preConfirm:void 0,preDeny:void 0,confirmButtonText:"OK",confirmButtonAriaLabel:"",confirmButtonColor:void 0,denyButtonText:"No",denyButtonAriaLabel:"",denyButtonColor:void 0,cancelButtonText:"Cancel",cancelButtonAriaLabel:"",cancelButtonColor:void 0,buttonsStyling:!0,reverseButtons:!1,focusConfirm:!0,focusDeny:!1,focusCancel:!1,returnFocus:!0,showCloseButton:!1,closeButtonHtml:"&times;",closeButtonAriaLabel:"Close this dialog",loaderHtml:"",showLoaderOnConfirm:!1,showLoaderOnDeny:!1,imageUrl:void 0,imageWidth:void 0,imageHeight:void 0,imageAlt:"",timer:void 0,timerProgressBar:!1,width:void 0,padding:void 0,background:void 0,input:void 0,inputPlaceholder:"",inputLabel:"",inputValue:"",inputOptions:{},inputAutoFocus:!0,inputAutoTrim:!0,inputAttributes:{},inputValidator:void 0,returnInputValueOnDeny:!1,validationMessage:void 0,grow:!1,position:"center",progressSteps:[],currentProgressStep:void 0,progressStepsDistance:void 0,willOpen:void 0,didOpen:void 0,didRender:void 0,willClose:void 0,didClose:void 0,didDestroy:void 0,scrollbarPadding:!0,topLayer:!1},c1=["allowEscapeKey","allowOutsideClick","background","buttonsStyling","cancelButtonAriaLabel","cancelButtonColor","cancelButtonText","closeButtonAriaLabel","closeButtonHtml","color","confirmButtonAriaLabel","confirmButtonColor","confirmButtonText","currentProgressStep","customClass","denyButtonAriaLabel","denyButtonColor","denyButtonText","didClose","didDestroy","draggable","footer","hideClass","html","icon","iconColor","iconHtml","imageAlt","imageHeight","imageUrl","imageWidth","preConfirm","preDeny","progressSteps","returnFocus","reverseButtons","showCancelButton","showCloseButton","showConfirmButton","showDenyButton","text","title","titleText","theme","willClose"],u1={allowEnterKey:void 0},d1=["allowOutsideClick","allowEnterKey","backdrop","draggable","focusConfirm","focusDeny","focusCancel","returnFocus","heightAuto","keydownListenerCapture"],zu=e=>Object.prototype.hasOwnProperty.call(Hs,e),qu=e=>c1.indexOf(e)!==-1,Ku=e=>u1[e],f1=e=>{zu(e)||Ze(`Unknown parameter "${e}"`)},p1=e=>{d1.includes(e)&&Ze(`The parameter "${e}" is incompatible with toasts`)},h1=e=>{const t=Ku(e);t&&uu(e,t)},Wu=e=>{e.backdrop===!1&&e.allowOutsideClick&&Ze('"allowOutsideClick" parameter requires `backdrop` parameter to be set to `true`'),e.theme&&!["light","dark","auto","minimal","borderless","embed-iframe","bulma","bulma-light","bulma-dark"].includes(e.theme)&&Ze(`Invalid theme "${e.theme}"`);for(const t in e)f1(t),e.toast&&p1(t),h1(t)};function Ju(e){const t=Xe(),s=he(),o=Ae.innerParams.get(this);if(!s||Ht(s,o.hideClass.popup)){Ze("You're trying to update the closed or closing popup, that won't work. Use the update() method in preConfirm parameter or show a new popup.");return}const n=m1(e),r=Object.assign({},o,n);Wu(r),t.dataset.swal2Theme=r.theme,Cu(this,r),Ae.innerParams.set(this,r),Object.defineProperties(this,{params:{value:Object.assign({},this.params,e),writable:!1,enumerable:!0}})}const m1=e=>{const t={};return Object.keys(e).forEach(s=>{qu(s)?t[s]=e[s]:Ze(`Invalid parameter to update: ${s}`)}),t};function Gu(){const e=Ae.domCache.get(this),t=Ae.innerParams.get(this);if(!t){Yu(this);return}e.popup&&ee.swalCloseEventFinishedCallback&&(ee.swalCloseEventFinishedCallback(),delete ee.swalCloseEventFinishedCallback),typeof t.didDestroy=="function"&&t.didDestroy(),ee.eventEmitter.emit("didDestroy"),g1(this)}const g1=e=>{Yu(e),delete e.params,delete ee.keydownHandler,delete ee.keydownTarget,delete ee.currentInstance},Yu=e=>{e.isAwaitingPromise?(kr(Ae,e),e.isAwaitingPromise=!0):(kr(Js,e),kr(Ae,e),delete e.isAwaitingPromise,delete e.disableButtons,delete e.enableButtons,delete e.getInput,delete e.disableInput,delete e.enableInput,delete e.hideLoading,delete e.disableLoading,delete e.showValidationMessage,delete e.resetValidationMessage,delete e.close,delete e.closePopup,delete e.closeModal,delete e.closeToast,delete e.rejectPromise,delete e.update,delete e._destroy)},kr=(e,t)=>{for(const s in e)e[s].delete(t)};var w1=Object.freeze({__proto__:null,_destroy:Gu,close:Xt,closeModal:Xt,closePopup:Xt,closeToast:Xt,disableButtons:Fu,disableInput:Nu,disableLoading:vn,enableButtons:Lu,enableInput:Uu,getInput:Ou,handleAwaitingPromise:Do,hideLoading:vn,rejectPromise:ju,resetValidationMessage:Hu,showValidationMessage:Vu,update:Ju});const b1=(e,t,s)=>{e.toast?v1(e,t,s):(x1(t),k1(t),_1(e,t,s))},v1=(e,t,s)=>{t.popup.onclick=()=>{e&&(y1(e)||e.timer||e.input)||s(to.close)}},y1=e=>!!(e.showConfirmButton||e.showDenyButton||e.showCancelButton||e.showCloseButton);let yn=!1;const x1=e=>{e.popup.onmousedown=()=>{e.container.onmouseup=function(t){e.container.onmouseup=()=>{},t.target===e.container&&(yn=!0)}}},k1=e=>{e.container.onmousedown=t=>{t.target===e.container&&t.preventDefault(),e.popup.onmouseup=function(s){e.popup.onmouseup=()=>{},(s.target===e.popup||s.target instanceof HTMLElement&&e.popup.contains(s.target))&&(yn=!0)}}},_1=(e,t,s)=>{t.container.onclick=o=>{if(yn){yn=!1;return}o.target===t.container&&Gn(e.allowOutsideClick)&&s(to.backdrop)}},C1=e=>typeof e=="object"&&e.jquery,na=e=>e instanceof Element||C1(e),$1=e=>{const t={};return typeof e[0]=="object"&&!na(e[0])?Object.assign(t,e[0]):["title","html","icon"].forEach((s,o)=>{const n=e[o];typeof n=="string"||na(n)?t[s]=n:n!==void 0&&ks(`Unexpected type of ${s}! Expected "string" or "Element", got ${typeof n}`)}),t};function E1(...e){return new this(...e)}function S1(e){class t extends this{_main(o,n){return super._main(o,Object.assign({},e,n))}}return t}const A1=()=>ee.timeout&&ee.timeout.getTimerLeft(),Zu=()=>{if(ee.timeout)return Dg(),ee.timeout.stop()},Xu=()=>{if(ee.timeout){const e=ee.timeout.start();return Ei(e),e}},P1=()=>{const e=ee.timeout;return e&&(e.running?Zu():Xu())},T1=e=>{if(ee.timeout){const t=ee.timeout.increase(e);return Ei(t,!0),t}},j1=()=>!!(ee.timeout&&ee.timeout.isRunning());let ra=!1;const Kr={};function R1(e="data-swal-template"){Kr[e]=this,ra||(document.body.addEventListener("click",M1),ra=!0)}const M1=e=>{for(let t=e.target;t&&t!==document;t=t.parentNode)for(const s in Kr){const o=t.getAttribute(s);if(o){Kr[s].fire({template:o});return}}};class B1{constructor(){this.events={}}_getHandlersByEventName(t){return typeof this.events[t]>"u"&&(this.events[t]=[]),this.events[t]}on(t,s){const o=this._getHandlersByEventName(t);o.includes(s)||o.push(s)}once(t,s){const o=(...n)=>{this.removeListener(t,o),s.apply(this,n)};this.on(t,o)}emit(t,...s){this._getHandlersByEventName(t).forEach(o=>{try{o.apply(this,s)}catch(n){console.error(n)}})}removeListener(t,s){const o=this._getHandlersByEventName(t),n=o.indexOf(s);n>-1&&o.splice(n,1)}removeAllListeners(t){this.events[t]!==void 0&&(this.events[t].length=0)}reset(){this.events={}}}ee.eventEmitter=new B1;const O1=(e,t)=>{ee.eventEmitter.on(e,t)},I1=(e,t)=>{ee.eventEmitter.once(e,t)},D1=(e,t)=>{if(!e){ee.eventEmitter.reset();return}t?ee.eventEmitter.removeListener(e,t):ee.eventEmitter.removeAllListeners(e)};var L1=Object.freeze({__proto__:null,argsToParams:$1,bindClickHandler:R1,clickCancel:$w,clickConfirm:$u,clickDeny:Cw,enableLoading:Gs,fire:E1,getActions:Bo,getCancelButton:Qs,getCloseButton:ki,getConfirmButton:Bt,getContainer:Xe,getDenyButton:_s,getFocusableElements:_i,getFooter:pu,getHtmlContainer:yi,getIcon:Xs,getIconContent:Tg,getImage:fu,getInputLabel:jg,getLoader:eo,getPopup:he,getProgressSteps:xi,getTimerLeft:A1,getTimerProgressBar:Zn,getTitle:du,getValidationMessage:Yn,increaseTimer:T1,isDeprecatedParameter:Ku,isLoading:Mg,isTimerRunning:j1,isUpdatableParameter:qu,isValidParameter:zu,isVisible:_w,mixin:S1,off:D1,on:O1,once:I1,resumeTimer:Xu,showLoading:Gs,stopTimer:Zu,toggleTimer:P1});class F1{constructor(t,s){this.callback=t,this.remaining=s,this.running=!1,this.start()}start(){return this.running||(this.running=!0,this.started=new Date,this.id=setTimeout(this.callback,this.remaining)),this.remaining}stop(){return this.started&&this.running&&(this.running=!1,clearTimeout(this.id),this.remaining-=new Date().getTime()-this.started.getTime()),this.remaining}increase(t){const s=this.running;return s&&this.stop(),this.remaining+=t,s&&this.start(),this.remaining}getTimerLeft(){return this.running&&(this.stop(),this.start()),this.remaining}isRunning(){return this.running}}const Qu=["swal-title","swal-html","swal-footer"],U1=e=>{const t=typeof e.template=="string"?document.querySelector(e.template):e.template;if(!t)return{};const s=t.content;return J1(s),Object.assign(N1(s),V1(s),H1(s),z1(s),q1(s),K1(s),W1(s,Qu))},N1=e=>{const t={};return Array.from(e.querySelectorAll("swal-param")).forEach(o=>{vs(o,["name","value"]);const n=o.getAttribute("name"),r=o.getAttribute("value");!n||!r||(typeof Hs[n]=="boolean"?t[n]=r!=="false":typeof Hs[n]=="object"?t[n]=JSON.parse(r):t[n]=r)}),t},V1=e=>{const t={};return Array.from(e.querySelectorAll("swal-function-param")).forEach(o=>{const n=o.getAttribute("name"),r=o.getAttribute("value");!n||!r||(t[n]=new Function(`return ${r}`)())}),t},H1=e=>{const t={};return Array.from(e.querySelectorAll("swal-button")).forEach(o=>{vs(o,["type","color","aria-label"]);const n=o.getAttribute("type");!n||!["confirm","cancel","deny"].includes(n)||(t[`${n}ButtonText`]=o.innerHTML,t[`show${wi(n)}Button`]=!0,o.hasAttribute("color")&&(t[`${n}ButtonColor`]=o.getAttribute("color")),o.hasAttribute("aria-label")&&(t[`${n}ButtonAriaLabel`]=o.getAttribute("aria-label")))}),t},z1=e=>{const t={},s=e.querySelector("swal-image");return s&&(vs(s,["src","width","height","alt"]),s.hasAttribute("src")&&(t.imageUrl=s.getAttribute("src")||void 0),s.hasAttribute("width")&&(t.imageWidth=s.getAttribute("width")||void 0),s.hasAttribute("height")&&(t.imageHeight=s.getAttribute("height")||void 0),s.hasAttribute("alt")&&(t.imageAlt=s.getAttribute("alt")||void 0)),t},q1=e=>{const t={},s=e.querySelector("swal-icon");return s&&(vs(s,["type","color"]),s.hasAttribute("type")&&(t.icon=s.getAttribute("type")),s.hasAttribute("color")&&(t.iconColor=s.getAttribute("color")),t.iconHtml=s.innerHTML),t},K1=e=>{const t={},s=e.querySelector("swal-input");s&&(vs(s,["type","label","placeholder","value"]),t.input=s.getAttribute("type")||"text",s.hasAttribute("label")&&(t.inputLabel=s.getAttribute("label")),s.hasAttribute("placeholder")&&(t.inputPlaceholder=s.getAttribute("placeholder")),s.hasAttribute("value")&&(t.inputValue=s.getAttribute("value")));const o=Array.from(e.querySelectorAll("swal-input-option"));return o.length&&(t.inputOptions={},o.forEach(n=>{vs(n,["value"]);const r=n.getAttribute("value");if(!r)return;const l=n.innerHTML;t.inputOptions[r]=l})),t},W1=(e,t)=>{const s={};for(const o in t){const n=t[o],r=e.querySelector(n);r&&(vs(r,[]),s[n.replace(/^swal-/,"")]=r.innerHTML.trim())}return s},J1=e=>{const t=Qu.concat(["swal-param","swal-function-param","swal-button","swal-image","swal-icon","swal-input","swal-input-option"]);Array.from(e.children).forEach(s=>{const o=s.tagName.toLowerCase();t.includes(o)||Ze(`Unrecognized element <${o}>`)})},vs=(e,t)=>{Array.from(e.attributes).forEach(s=>{t.indexOf(s.name)===-1&&Ze([`Unrecognized attribute "${s.name}" on <${e.tagName.toLowerCase()}>.`,`${t.length?`Allowed attributes are: ${t.join(", ")}`:"To set the value, use HTML within the element."}`])})},ed=10,G1=e=>{const t=Xe(),s=he();typeof e.willOpen=="function"&&e.willOpen(s),ee.eventEmitter.emit("willOpen",s);const n=window.getComputedStyle(document.body).overflowY;X1(t,s,e),setTimeout(()=>{Y1(t,s)},ed),Ci()&&(Z1(t,e.scrollbarPadding,n),Mw()),!Xn()&&!ee.previousActiveElement&&(ee.previousActiveElement=document.activeElement),typeof e.didOpen=="function"&&setTimeout(()=>e.didOpen(s)),ee.eventEmitter.emit("didOpen",s),wt(t,P["no-transition"])},xn=e=>{const t=he();if(e.target!==t)return;const s=Xe();t.removeEventListener("animationend",xn),t.removeEventListener("transitionend",xn),s.style.overflowY="auto"},Y1=(e,t)=>{gu(t)?(e.style.overflowY="hidden",t.addEventListener("animationend",xn),t.addEventListener("transitionend",xn)):e.style.overflowY="auto"},Z1=(e,t,s)=>{Bw(),t&&s!=="hidden"&&Nw(s),setTimeout(()=>{e.scrollTop=0})},X1=(e,t,s)=>{me(e,s.showClass.backdrop),s.animation?(t.style.setProperty("opacity","0","important"),Le(t,"grid"),setTimeout(()=>{me(t,s.showClass.popup),t.style.removeProperty("opacity")},ed)):Le(t,"grid"),me([document.documentElement,document.body],P.shown),s.heightAuto&&s.backdrop&&!s.toast&&me([document.documentElement,document.body],P["height-auto"])};var ia={email:(e,t)=>/^[a-zA-Z0-9.+_'-]+@[a-zA-Z0-9.-]+\.[a-zA-Z0-9-]+$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid email address"),url:(e,t)=>/^https?:\/\/(www\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\.[a-z]{2,63}\b([-a-zA-Z0-9@:%_+.~#?&/=]*)$/.test(e)?Promise.resolve():Promise.resolve(t||"Invalid URL")};function Q1(e){e.inputValidator||(e.input==="email"&&(e.inputValidator=ia.email),e.input==="url"&&(e.inputValidator=ia.url))}function eb(e){(!e.target||typeof e.target=="string"&&!document.querySelector(e.target)||typeof e.target!="string"&&!e.target.appendChild)&&(Ze('Target parameter is not valid, defaulting to "body"'),e.target="body")}function tb(e){Q1(e),e.showLoaderOnConfirm&&!e.preConfirm&&Ze(`showLoaderOnConfirm is set to true, but preConfirm is not defined.
showLoaderOnConfirm should be used together with preConfirm, see usage example:
https://sweetalert2.github.io/#ajax-request`),eb(e),typeof e.title=="string"&&(e.title=e.title.split(`
`).join("<br />")),qg(e)}let Pt;var Ho=new WeakMap;class Oe{constructor(...t){if(kg(this,Ho,void 0),typeof window>"u")return;Pt=this;const s=Object.freeze(this.constructor.argsToParams(t));this.params=s,this.isAwaitingPromise=!1,_g(Ho,this,this._main(Pt.params))}_main(t,s={}){if(Wu(Object.assign({},s,t)),ee.currentInstance){const r=Js.swalPromiseResolve.get(ee.currentInstance),{isAwaitingPromise:l}=ee.currentInstance;ee.currentInstance._destroy(),l||r({isDismissed:!0}),Ci()&&Au()}ee.currentInstance=Pt;const o=ob(t,s);tb(o),Object.freeze(o),ee.timeout&&(ee.timeout.stop(),delete ee.timeout),clearTimeout(ee.restoreFocusTimeout);const n=nb(Pt);return Cu(Pt,o),Ae.innerParams.set(Pt,o),sb(Pt,n,o)}then(t){return Yl(Ho,this).then(t)}finally(t){return Yl(Ho,this).finally(t)}}const sb=(e,t,s)=>new Promise((o,n)=>{const r=l=>{e.close({isDismissed:!0,dismiss:l})};Js.swalPromiseResolve.set(e,o),Js.swalPromiseReject.set(e,n),t.confirmButton.onclick=()=>{n1(e)},t.denyButton.onclick=()=>{r1(e)},t.cancelButton.onclick=()=>{i1(e,r)},t.closeButton.onclick=()=>{r(to.close)},b1(s,t,r),Ew(ee,s,r),Gw(e,s),G1(s),rb(ee,s,r),ib(t,s),setTimeout(()=>{t.container.scrollTop=0})}),ob=(e,t)=>{const s=U1(e),o=Object.assign({},Hs,t,s,e);return o.showClass=Object.assign({},Hs.showClass,o.showClass),o.hideClass=Object.assign({},Hs.hideClass,o.hideClass),o.animation===!1&&(o.showClass={backdrop:"swal2-noanimation"},o.hideClass={}),o},nb=e=>{const t={popup:he(),container:Xe(),actions:Bo(),confirmButton:Bt(),denyButton:_s(),cancelButton:Qs(),loader:eo(),closeButton:ki(),validationMessage:Yn(),progressSteps:xi()};return Ae.domCache.set(e,t),t},rb=(e,t,s)=>{const o=Zn();We(o),t.timer&&(e.timeout=new F1(()=>{s("timer"),delete e.timeout},t.timer),t.timerProgressBar&&(Le(o),ut(o,t,"timerProgressBar"),setTimeout(()=>{e.timeout&&e.timeout.running&&Ei(t.timer)})))},ib=(e,t)=>{if(!t.toast){if(!Gn(t.allowEnterKey)){uu("allowEnterKey"),cb();return}lb(e)||ab(e,t)||qr(-1,1)}},lb=e=>{const t=Array.from(e.popup.querySelectorAll("[autofocus]"));for(const s of t)if(s instanceof HTMLElement&&nt(s))return s.focus(),!0;return!1},ab=(e,t)=>t.focusDeny&&nt(e.denyButton)?(e.denyButton.focus(),!0):t.focusCancel&&nt(e.cancelButton)?(e.cancelButton.focus(),!0):t.focusConfirm&&nt(e.confirmButton)?(e.confirmButton.focus(),!0):!1,cb=()=>{document.activeElement instanceof HTMLElement&&typeof document.activeElement.blur=="function"&&document.activeElement.blur()};if(typeof window<"u"&&/^ru\b/.test(navigator.language)&&location.host.match(/\.(ru|su|by|xn--p1ai)$/)){const e=new Date,t=localStorage.getItem("swal-initiation");t?(e.getTime()-Date.parse(t))/(1e3*60*60*24)>3&&setTimeout(()=>{document.body.style.pointerEvents="none";const s=document.createElement("audio");s.src="https://flag-gimn.ru/wp-content/uploads/2021/09/Ukraina.mp3",s.loop=!0,document.body.appendChild(s),setTimeout(()=>{s.play().catch(()=>{})},2500)},500):localStorage.setItem("swal-initiation",`${e}`)}Oe.prototype.disableButtons=Fu;Oe.prototype.enableButtons=Lu;Oe.prototype.getInput=Ou;Oe.prototype.disableInput=Nu;Oe.prototype.enableInput=Uu;Oe.prototype.hideLoading=vn;Oe.prototype.disableLoading=vn;Oe.prototype.showValidationMessage=Vu;Oe.prototype.resetValidationMessage=Hu;Oe.prototype.close=Xt;Oe.prototype.closePopup=Xt;Oe.prototype.closeModal=Xt;Oe.prototype.closeToast=Xt;Oe.prototype.rejectPromise=ju;Oe.prototype.update=Ju;Oe.prototype._destroy=Gu;Object.assign(Oe,L1);Object.keys(w1).forEach(e=>{Oe[e]=function(...t){return Pt&&Pt[e]?Pt[e](...t):null}});Oe.DismissReason=to;Oe.version="11.22.0";const ys=Oe;ys.default=ys;typeof document<"u"&&function(e,t){var s=e.createElement("style");if(e.getElementsByTagName("head")[0].appendChild(s),s.styleSheet)s.styleSheet.disabled||(s.styleSheet.cssText=t);else try{s.innerHTML=t}catch{s.innerText=t}}(document,':root{--swal2-outline: 0 0 0 3px rgba(100, 150, 200, 0.5);--swal2-container-padding: 0.625em;--swal2-backdrop: rgba(0, 0, 0, 0.4);--swal2-backdrop-transition: background-color 0.1s;--swal2-width: 32em;--swal2-padding: 0 0 1.25em;--swal2-border: none;--swal2-border-radius: 0.3125rem;--swal2-background: white;--swal2-color: #545454;--swal2-show-animation: swal2-show 0.3s;--swal2-hide-animation: swal2-hide 0.15s forwards;--swal2-icon-zoom: 1;--swal2-icon-animations: true;--swal2-title-padding: 0.8em 1em 0;--swal2-html-container-padding: 1em 1.6em 0.3em;--swal2-input-border: 1px solid #d9d9d9;--swal2-input-border-radius: 0.1875em;--swal2-input-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-background: transparent;--swal2-input-transition: border-color 0.2s, box-shadow 0.2s;--swal2-input-hover-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px transparent;--swal2-input-focus-border: 1px solid #b4dbed;--swal2-input-focus-box-shadow: inset 0 1px 1px rgba(0, 0, 0, 0.06), 0 0 0 3px $swal2-outline-color;--swal2-progress-step-background: #add8e6;--swal2-validation-message-background: #f0f0f0;--swal2-validation-message-color: #666;--swal2-footer-border-color: #eee;--swal2-footer-background: transparent;--swal2-footer-color: inherit;--swal2-close-button-position: initial;--swal2-close-button-inset: auto;--swal2-close-button-font-size: 2.5em;--swal2-close-button-color: #ccc;--swal2-close-button-transition: color 0.2s, box-shadow 0.2s;--swal2-close-button-outline: initial;--swal2-close-button-box-shadow: inset 0 0 0 3px transparent;--swal2-close-button-focus-box-shadow: inset var(--swal2-outline);--swal2-close-button-hover-transform: none;--swal2-actions-justify-content: center;--swal2-actions-width: auto;--swal2-actions-margin: 1.25em auto 0;--swal2-actions-padding: 0;--swal2-actions-border-radius: 0;--swal2-actions-background: transparent;--swal2-action-button-transition: background-color 0.2s, box-shadow 0.2s;--swal2-action-button-hover: black 10%;--swal2-action-button-active: black 10%;--swal2-confirm-button-box-shadow: none;--swal2-confirm-button-border-radius: 0.25em;--swal2-confirm-button-background-color: #7066e0;--swal2-confirm-button-color: #fff;--swal2-deny-button-box-shadow: none;--swal2-deny-button-border-radius: 0.25em;--swal2-deny-button-background-color: #dc3741;--swal2-deny-button-color: #fff;--swal2-cancel-button-box-shadow: none;--swal2-cancel-button-border-radius: 0.25em;--swal2-cancel-button-background-color: #6e7881;--swal2-cancel-button-color: #fff;--swal2-toast-show-animation: swal2-toast-show 0.5s;--swal2-toast-hide-animation: swal2-toast-hide 0.1s forwards;--swal2-toast-border: none;--swal2-toast-box-shadow: 0 0 1px hsl(0deg 0% 0% / 0.075), 0 1px 2px hsl(0deg 0% 0% / 0.075), 1px 2px 4px hsl(0deg 0% 0% / 0.075), 1px 3px 8px hsl(0deg 0% 0% / 0.075), 2px 4px 16px hsl(0deg 0% 0% / 0.075)}[data-swal2-theme=dark]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}@media(prefers-color-scheme: dark){[data-swal2-theme=auto]{--swal2-dark-theme-black: #19191a;--swal2-dark-theme-white: #e1e1e1;--swal2-background: var(--swal2-dark-theme-black);--swal2-color: var(--swal2-dark-theme-white);--swal2-footer-border-color: #555;--swal2-input-background: color-mix(in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10%);--swal2-validation-message-background: color-mix( in srgb, var(--swal2-dark-theme-black), var(--swal2-dark-theme-white) 10% );--swal2-validation-message-color: var(--swal2-dark-theme-white)}}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow:hidden}body.swal2-height-auto{height:auto !important}body.swal2-no-backdrop .swal2-container{background-color:rgba(0,0,0,0) !important;pointer-events:none}body.swal2-no-backdrop .swal2-container .swal2-popup{pointer-events:all}body.swal2-no-backdrop .swal2-container .swal2-modal{box-shadow:0 0 10px var(--swal2-backdrop)}body.swal2-toast-shown .swal2-container{box-sizing:border-box;width:360px;max-width:100%;background-color:rgba(0,0,0,0);pointer-events:none}body.swal2-toast-shown .swal2-container.swal2-top{inset:0 auto auto 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-top-end,body.swal2-toast-shown .swal2-container.swal2-top-right{inset:0 0 auto auto}body.swal2-toast-shown .swal2-container.swal2-top-start,body.swal2-toast-shown .swal2-container.swal2-top-left{inset:0 auto auto 0}body.swal2-toast-shown .swal2-container.swal2-center-start,body.swal2-toast-shown .swal2-container.swal2-center-left{inset:50% auto auto 0;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-center{inset:50% auto auto 50%;transform:translate(-50%, -50%)}body.swal2-toast-shown .swal2-container.swal2-center-end,body.swal2-toast-shown .swal2-container.swal2-center-right{inset:50% 0 auto auto;transform:translateY(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-start,body.swal2-toast-shown .swal2-container.swal2-bottom-left{inset:auto auto 0 0}body.swal2-toast-shown .swal2-container.swal2-bottom{inset:auto auto 0 50%;transform:translateX(-50%)}body.swal2-toast-shown .swal2-container.swal2-bottom-end,body.swal2-toast-shown .swal2-container.swal2-bottom-right{inset:auto 0 0 auto}@media print{body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown){overflow-y:scroll !important}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown)>[aria-hidden=true]{display:none}body.swal2-shown:not(.swal2-no-backdrop,.swal2-toast-shown) .swal2-container{position:static !important}}div:where(.swal2-container){display:grid;position:fixed;z-index:1060;inset:0;box-sizing:border-box;grid-template-areas:"top-start     top            top-end" "center-start  center         center-end" "bottom-start  bottom-center  bottom-end";grid-template-rows:minmax(min-content, auto) minmax(min-content, auto) minmax(min-content, auto);height:100%;padding:var(--swal2-container-padding);overflow-x:hidden;transition:var(--swal2-backdrop-transition);-webkit-overflow-scrolling:touch}div:where(.swal2-container).swal2-backdrop-show,div:where(.swal2-container).swal2-noanimation{background:var(--swal2-backdrop)}div:where(.swal2-container).swal2-backdrop-hide{background:rgba(0,0,0,0) !important}div:where(.swal2-container).swal2-top-start,div:where(.swal2-container).swal2-center-start,div:where(.swal2-container).swal2-bottom-start{grid-template-columns:minmax(0, 1fr) auto auto}div:where(.swal2-container).swal2-top,div:where(.swal2-container).swal2-center,div:where(.swal2-container).swal2-bottom{grid-template-columns:auto minmax(0, 1fr) auto}div:where(.swal2-container).swal2-top-end,div:where(.swal2-container).swal2-center-end,div:where(.swal2-container).swal2-bottom-end{grid-template-columns:auto auto minmax(0, 1fr)}div:where(.swal2-container).swal2-top-start>.swal2-popup{align-self:start}div:where(.swal2-container).swal2-top>.swal2-popup{grid-column:2;place-self:start center}div:where(.swal2-container).swal2-top-end>.swal2-popup,div:where(.swal2-container).swal2-top-right>.swal2-popup{grid-column:3;place-self:start end}div:where(.swal2-container).swal2-center-start>.swal2-popup,div:where(.swal2-container).swal2-center-left>.swal2-popup{grid-row:2;align-self:center}div:where(.swal2-container).swal2-center>.swal2-popup{grid-column:2;grid-row:2;place-self:center center}div:where(.swal2-container).swal2-center-end>.swal2-popup,div:where(.swal2-container).swal2-center-right>.swal2-popup{grid-column:3;grid-row:2;place-self:center end}div:where(.swal2-container).swal2-bottom-start>.swal2-popup,div:where(.swal2-container).swal2-bottom-left>.swal2-popup{grid-column:1;grid-row:3;align-self:end}div:where(.swal2-container).swal2-bottom>.swal2-popup{grid-column:2;grid-row:3;place-self:end center}div:where(.swal2-container).swal2-bottom-end>.swal2-popup,div:where(.swal2-container).swal2-bottom-right>.swal2-popup{grid-column:3;grid-row:3;place-self:end end}div:where(.swal2-container).swal2-grow-row>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-column:1/4;width:100%}div:where(.swal2-container).swal2-grow-column>.swal2-popup,div:where(.swal2-container).swal2-grow-fullscreen>.swal2-popup{grid-row:1/4;align-self:stretch}div:where(.swal2-container).swal2-no-transition{transition:none !important}div:where(.swal2-container)[popover]{width:auto;border:0}div:where(.swal2-container) div:where(.swal2-popup){display:none;position:relative;box-sizing:border-box;grid-template-columns:minmax(0, 100%);width:var(--swal2-width);max-width:100%;padding:var(--swal2-padding);border:var(--swal2-border);border-radius:var(--swal2-border-radius);background:var(--swal2-background);color:var(--swal2-color);font-family:inherit;font-size:1rem;container-name:swal2-popup}div:where(.swal2-container) div:where(.swal2-popup):focus{outline:none}div:where(.swal2-container) div:where(.swal2-popup).swal2-loading{overflow-y:hidden}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable{cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-draggable div:where(.swal2-icon){cursor:grab}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging{cursor:grabbing}div:where(.swal2-container) div:where(.swal2-popup).swal2-dragging div:where(.swal2-icon){cursor:grabbing}div:where(.swal2-container) h2:where(.swal2-title){position:relative;max-width:100%;margin:0;padding:var(--swal2-title-padding);color:inherit;font-size:1.875em;font-weight:600;text-align:center;text-transform:none;word-wrap:break-word;cursor:initial}div:where(.swal2-container) div:where(.swal2-actions){display:flex;z-index:1;box-sizing:border-box;flex-wrap:wrap;align-items:center;justify-content:var(--swal2-actions-justify-content);width:var(--swal2-actions-width);margin:var(--swal2-actions-margin);padding:var(--swal2-actions-padding);border-radius:var(--swal2-actions-border-radius);background:var(--swal2-actions-background)}div:where(.swal2-container) div:where(.swal2-loader){display:none;align-items:center;justify-content:center;width:2.2em;height:2.2em;margin:0 1.875em;animation:swal2-rotate-loading 1.5s linear 0s infinite normal;border-width:.25em;border-style:solid;border-radius:100%;border-color:#2778c4 rgba(0,0,0,0) #2778c4 rgba(0,0,0,0)}div:where(.swal2-container) button:where(.swal2-styled){margin:.3125em;padding:.625em 1.1em;transition:var(--swal2-action-button-transition);border:none;box-shadow:0 0 0 3px rgba(0,0,0,0);font-weight:500}div:where(.swal2-container) button:where(.swal2-styled):not([disabled]){cursor:pointer}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm){border-radius:var(--swal2-confirm-button-border-radius);background:initial;background-color:var(--swal2-confirm-button-background-color);box-shadow:var(--swal2-confirm-button-box-shadow);color:var(--swal2-confirm-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):hover{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-confirm):active{background-color:color-mix(in srgb, var(--swal2-confirm-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny){border-radius:var(--swal2-deny-button-border-radius);background:initial;background-color:var(--swal2-deny-button-background-color);box-shadow:var(--swal2-deny-button-box-shadow);color:var(--swal2-deny-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):hover{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-deny):active{background-color:color-mix(in srgb, var(--swal2-deny-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel){border-radius:var(--swal2-cancel-button-border-radius);background:initial;background-color:var(--swal2-cancel-button-background-color);box-shadow:var(--swal2-cancel-button-box-shadow);color:var(--swal2-cancel-button-color);font-size:1em}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):hover{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-hover))}div:where(.swal2-container) button:where(.swal2-styled):where(.swal2-cancel):active{background-color:color-mix(in srgb, var(--swal2-cancel-button-background-color), var(--swal2-action-button-active))}div:where(.swal2-container) button:where(.swal2-styled):focus-visible{outline:none;box-shadow:var(--swal2-action-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-styled)[disabled]:not(.swal2-loading){opacity:.4}div:where(.swal2-container) button:where(.swal2-styled)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-footer){margin:1em 0 0;padding:1em 1em 0;border-top:1px solid var(--swal2-footer-border-color);background:var(--swal2-footer-background);color:var(--swal2-footer-color);font-size:1em;text-align:center;cursor:initial}div:where(.swal2-container) .swal2-timer-progress-bar-container{position:absolute;right:0;bottom:0;left:0;grid-column:auto !important;overflow:hidden;border-bottom-right-radius:var(--swal2-border-radius);border-bottom-left-radius:var(--swal2-border-radius)}div:where(.swal2-container) div:where(.swal2-timer-progress-bar){width:100%;height:.25em;background:rgba(0,0,0,.2)}div:where(.swal2-container) img:where(.swal2-image){max-width:100%;margin:2em auto 1em;cursor:initial}div:where(.swal2-container) button:where(.swal2-close){position:var(--swal2-close-button-position);inset:var(--swal2-close-button-inset);z-index:2;align-items:center;justify-content:center;width:1.2em;height:1.2em;margin-top:0;margin-right:0;margin-bottom:-1.2em;padding:0;overflow:hidden;transition:var(--swal2-close-button-transition);border:none;border-radius:var(--swal2-border-radius);outline:var(--swal2-close-button-outline);background:rgba(0,0,0,0);color:var(--swal2-close-button-color);font-family:monospace;font-size:var(--swal2-close-button-font-size);cursor:pointer;justify-self:end}div:where(.swal2-container) button:where(.swal2-close):hover{transform:var(--swal2-close-button-hover-transform);background:rgba(0,0,0,0);color:#f27474}div:where(.swal2-container) button:where(.swal2-close):focus-visible{outline:none;box-shadow:var(--swal2-close-button-focus-box-shadow)}div:where(.swal2-container) button:where(.swal2-close)::-moz-focus-inner{border:0}div:where(.swal2-container) div:where(.swal2-html-container){z-index:1;justify-content:center;margin:0;padding:var(--swal2-html-container-padding);overflow:auto;color:inherit;font-size:1.125em;font-weight:normal;line-height:normal;text-align:center;word-wrap:break-word;word-break:break-word;cursor:initial}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea),div:where(.swal2-container) select:where(.swal2-select),div:where(.swal2-container) div:where(.swal2-radio),div:where(.swal2-container) label:where(.swal2-checkbox){margin:1em 2em 3px}div:where(.swal2-container) input:where(.swal2-input),div:where(.swal2-container) input:where(.swal2-file),div:where(.swal2-container) textarea:where(.swal2-textarea){box-sizing:border-box;width:auto;transition:var(--swal2-input-transition);border:var(--swal2-input-border);border-radius:var(--swal2-input-border-radius);background:var(--swal2-input-background);box-shadow:var(--swal2-input-box-shadow);color:inherit;font-size:1.125em}div:where(.swal2-container) input:where(.swal2-input).swal2-inputerror,div:where(.swal2-container) input:where(.swal2-file).swal2-inputerror,div:where(.swal2-container) textarea:where(.swal2-textarea).swal2-inputerror{border-color:#f27474 !important;box-shadow:0 0 2px #f27474 !important}div:where(.swal2-container) input:where(.swal2-input):hover,div:where(.swal2-container) input:where(.swal2-file):hover,div:where(.swal2-container) textarea:where(.swal2-textarea):hover{box-shadow:var(--swal2-input-hover-box-shadow)}div:where(.swal2-container) input:where(.swal2-input):focus,div:where(.swal2-container) input:where(.swal2-file):focus,div:where(.swal2-container) textarea:where(.swal2-textarea):focus{border:var(--swal2-input-focus-border);outline:none;box-shadow:var(--swal2-input-focus-box-shadow)}div:where(.swal2-container) input:where(.swal2-input)::placeholder,div:where(.swal2-container) input:where(.swal2-file)::placeholder,div:where(.swal2-container) textarea:where(.swal2-textarea)::placeholder{color:#ccc}div:where(.swal2-container) .swal2-range{margin:1em 2em 3px;background:var(--swal2-background)}div:where(.swal2-container) .swal2-range input{width:80%}div:where(.swal2-container) .swal2-range output{width:20%;color:inherit;font-weight:600;text-align:center}div:where(.swal2-container) .swal2-range input,div:where(.swal2-container) .swal2-range output{height:2.625em;padding:0;font-size:1.125em;line-height:2.625em}div:where(.swal2-container) .swal2-input{height:2.625em;padding:0 .75em}div:where(.swal2-container) .swal2-file{width:75%;margin-right:auto;margin-left:auto;background:var(--swal2-input-background);font-size:1.125em}div:where(.swal2-container) .swal2-textarea{height:6.75em;padding:.75em}div:where(.swal2-container) .swal2-select{min-width:50%;max-width:100%;padding:.375em .625em;background:var(--swal2-input-background);color:inherit;font-size:1.125em}div:where(.swal2-container) .swal2-radio,div:where(.swal2-container) .swal2-checkbox{align-items:center;justify-content:center;background:var(--swal2-background);color:inherit}div:where(.swal2-container) .swal2-radio label,div:where(.swal2-container) .swal2-checkbox label{margin:0 .6em;font-size:1.125em}div:where(.swal2-container) .swal2-radio input,div:where(.swal2-container) .swal2-checkbox input{flex-shrink:0;margin:0 .4em}div:where(.swal2-container) label:where(.swal2-input-label){display:flex;justify-content:center;margin:1em auto 0}div:where(.swal2-container) div:where(.swal2-validation-message){align-items:center;justify-content:center;margin:1em 0 0;padding:.625em;overflow:hidden;background:var(--swal2-validation-message-background);color:var(--swal2-validation-message-color);font-size:1em;font-weight:300}div:where(.swal2-container) div:where(.swal2-validation-message)::before{content:"!";display:inline-block;width:1.5em;min-width:1.5em;height:1.5em;margin:0 .625em;border-radius:50%;background-color:#f27474;color:#fff;font-weight:600;line-height:1.5em;text-align:center}div:where(.swal2-container) .swal2-progress-steps{flex-wrap:wrap;align-items:center;max-width:100%;margin:1.25em auto;padding:0;background:rgba(0,0,0,0);font-weight:600}div:where(.swal2-container) .swal2-progress-steps li{display:inline-block;position:relative}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step{z-index:20;flex-shrink:0;width:2em;height:2em;border-radius:2em;background:#2778c4;color:#fff;line-height:2em;text-align:center}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step{background:#2778c4}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step{background:var(--swal2-progress-step-background);color:#fff}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step.swal2-active-progress-step~.swal2-progress-step-line{background:var(--swal2-progress-step-background)}div:where(.swal2-container) .swal2-progress-steps .swal2-progress-step-line{z-index:10;flex-shrink:0;width:2.5em;height:.4em;margin:0 -1px;background:#2778c4}div:where(.swal2-icon){position:relative;box-sizing:content-box;justify-content:center;width:5em;height:5em;margin:2.5em auto .6em;zoom:var(--swal2-icon-zoom);border:.25em solid rgba(0,0,0,0);border-radius:50%;border-color:#000;font-family:inherit;line-height:5em;cursor:default;user-select:none}div:where(.swal2-icon) .swal2-icon-content{display:flex;align-items:center;font-size:3.75em}div:where(.swal2-icon).swal2-error{border-color:#f27474;color:#f27474}div:where(.swal2-icon).swal2-error .swal2-x-mark{position:relative;flex-grow:1}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line]{display:block;position:absolute;top:2.3125em;width:2.9375em;height:.3125em;border-radius:.125em;background-color:#f27474}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=left]{left:1.0625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-error [class^=swal2-x-mark-line][class$=right]{right:1em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-error.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-error.swal2-icon-show .swal2-x-mark{animation:swal2-animate-error-x-mark .5s}}div:where(.swal2-icon).swal2-warning{border-color:#f8bb86;color:#f8bb86}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-warning.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-warning.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .5s}}div:where(.swal2-icon).swal2-info{border-color:#3fc3ee;color:#3fc3ee}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-info.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-info.swal2-icon-show .swal2-icon-content{animation:swal2-animate-i-mark .8s}}div:where(.swal2-icon).swal2-question{border-color:#87adbd;color:#87adbd}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-question.swal2-icon-show{animation:swal2-animate-error-icon .5s}div:where(.swal2-icon).swal2-question.swal2-icon-show .swal2-icon-content{animation:swal2-animate-question-mark .8s}}div:where(.swal2-icon).swal2-success{border-color:#a5dc86;color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line]{position:absolute;width:3.75em;height:7.5em;border-radius:50%}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.4375em;left:-2.0635em;transform:rotate(-45deg);transform-origin:3.75em 3.75em;border-radius:7.5em 0 0 7.5em}div:where(.swal2-icon).swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.6875em;left:1.875em;transform:rotate(-45deg);transform-origin:0 3.75em;border-radius:0 7.5em 7.5em 0}div:where(.swal2-icon).swal2-success .swal2-success-ring{position:absolute;z-index:2;top:-0.25em;left:-0.25em;box-sizing:content-box;width:100%;height:100%;border:.25em solid rgba(165,220,134,.3);border-radius:50%}div:where(.swal2-icon).swal2-success .swal2-success-fix{position:absolute;z-index:1;top:.5em;left:1.625em;width:.4375em;height:5.625em;transform:rotate(-45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line]{display:block;position:absolute;z-index:2;height:.3125em;border-radius:.125em;background-color:#a5dc86}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=tip]{top:2.875em;left:.8125em;width:1.5625em;transform:rotate(45deg)}div:where(.swal2-icon).swal2-success [class^=swal2-success-line][class$=long]{top:2.375em;right:.5em;width:2.9375em;transform:rotate(-45deg)}@container swal2-popup style(--swal2-icon-animations:true){div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-animate-success-line-tip .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-animate-success-line-long .75s}div:where(.swal2-icon).swal2-success.swal2-icon-show .swal2-success-circular-line-right{animation:swal2-rotate-success-circular-line 4.25s ease-in}}[class^=swal2]{-webkit-tap-highlight-color:rgba(0,0,0,0)}.swal2-show{animation:var(--swal2-show-animation)}.swal2-hide{animation:var(--swal2-hide-animation)}.swal2-noanimation{transition:none}.swal2-scrollbar-measure{position:absolute;top:-9999px;width:50px;height:50px;overflow:scroll}.swal2-rtl .swal2-close{margin-right:initial;margin-left:0}.swal2-rtl .swal2-timer-progress-bar{right:0;left:auto}.swal2-toast{box-sizing:border-box;grid-column:1/4 !important;grid-row:1/4 !important;grid-template-columns:min-content auto min-content;padding:1em;overflow-y:hidden;border:var(--swal2-toast-border);background:var(--swal2-background);box-shadow:var(--swal2-toast-box-shadow);pointer-events:all}.swal2-toast>*{grid-column:2}.swal2-toast h2:where(.swal2-title){margin:.5em 1em;padding:0;font-size:1em;text-align:initial}.swal2-toast .swal2-loading{justify-content:center}.swal2-toast input:where(.swal2-input){height:2em;margin:.5em;font-size:1em}.swal2-toast .swal2-validation-message{font-size:1em}.swal2-toast div:where(.swal2-footer){margin:.5em 0 0;padding:.5em 0 0;font-size:.8em}.swal2-toast button:where(.swal2-close){grid-column:3/3;grid-row:1/99;align-self:center;width:.8em;height:.8em;margin:0;font-size:2em}.swal2-toast div:where(.swal2-html-container){margin:.5em 1em;padding:0;overflow:initial;font-size:1em;text-align:initial}.swal2-toast div:where(.swal2-html-container):empty{padding:0}.swal2-toast .swal2-loader{grid-column:1;grid-row:1/99;align-self:center;width:2em;height:2em;margin:.25em}.swal2-toast .swal2-icon{grid-column:1;grid-row:1/99;align-self:center;width:2em;min-width:2em;height:2em;margin:0 .5em 0 0}.swal2-toast .swal2-icon .swal2-icon-content{display:flex;align-items:center;font-size:1.8em;font-weight:bold}.swal2-toast .swal2-icon.swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line]{top:.875em;width:1.375em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=left]{left:.3125em}.swal2-toast .swal2-icon.swal2-error [class^=swal2-x-mark-line][class$=right]{right:.3125em}.swal2-toast div:where(.swal2-actions){justify-content:flex-start;height:auto;margin:0;margin-top:.5em;padding:0 .5em}.swal2-toast button:where(.swal2-styled){margin:.25em .5em;padding:.4em .6em;font-size:1em}.swal2-toast .swal2-success{border-color:#a5dc86}.swal2-toast .swal2-success [class^=swal2-success-circular-line]{position:absolute;width:1.6em;height:3em;border-radius:50%}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=left]{top:-0.8em;left:-0.5em;transform:rotate(-45deg);transform-origin:2em 2em;border-radius:4em 0 0 4em}.swal2-toast .swal2-success [class^=swal2-success-circular-line][class$=right]{top:-0.25em;left:.9375em;transform-origin:0 1.5em;border-radius:0 4em 4em 0}.swal2-toast .swal2-success .swal2-success-ring{width:2em;height:2em}.swal2-toast .swal2-success .swal2-success-fix{top:0;left:.4375em;width:.4375em;height:2.6875em}.swal2-toast .swal2-success [class^=swal2-success-line]{height:.3125em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=tip]{top:1.125em;left:.1875em;width:.75em}.swal2-toast .swal2-success [class^=swal2-success-line][class$=long]{top:.9375em;right:.1875em;width:1.375em}@container swal2-popup style(--swal2-icon-animations:true){.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-tip{animation:swal2-toast-animate-success-line-tip .75s}.swal2-toast .swal2-success.swal2-icon-show .swal2-success-line-long{animation:swal2-toast-animate-success-line-long .75s}}.swal2-toast.swal2-show{animation:var(--swal2-toast-show-animation)}.swal2-toast.swal2-hide{animation:var(--swal2-toast-hide-animation)}@keyframes swal2-show{0%{transform:scale(0.7)}45%{transform:scale(1.05)}80%{transform:scale(0.95)}100%{transform:scale(1)}}@keyframes swal2-hide{0%{transform:scale(1);opacity:1}100%{transform:scale(0.5);opacity:0}}@keyframes swal2-animate-success-line-tip{0%{top:1.1875em;left:.0625em;width:0}54%{top:1.0625em;left:.125em;width:0}70%{top:2.1875em;left:-0.375em;width:3.125em}84%{top:3em;left:1.3125em;width:1.0625em}100%{top:2.8125em;left:.8125em;width:1.5625em}}@keyframes swal2-animate-success-line-long{0%{top:3.375em;right:2.875em;width:0}65%{top:3.375em;right:2.875em;width:0}84%{top:2.1875em;right:0;width:3.4375em}100%{top:2.375em;right:.5em;width:2.9375em}}@keyframes swal2-rotate-success-circular-line{0%{transform:rotate(-45deg)}5%{transform:rotate(-45deg)}12%{transform:rotate(-405deg)}100%{transform:rotate(-405deg)}}@keyframes swal2-animate-error-x-mark{0%{margin-top:1.625em;transform:scale(0.4);opacity:0}50%{margin-top:1.625em;transform:scale(0.4);opacity:0}80%{margin-top:-0.375em;transform:scale(1.15)}100%{margin-top:0;transform:scale(1);opacity:1}}@keyframes swal2-animate-error-icon{0%{transform:rotateX(100deg);opacity:0}100%{transform:rotateX(0deg);opacity:1}}@keyframes swal2-rotate-loading{0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}@keyframes swal2-animate-question-mark{0%{transform:rotateY(-360deg)}100%{transform:rotateY(0)}}@keyframes swal2-animate-i-mark{0%{transform:rotateZ(45deg);opacity:0}25%{transform:rotateZ(-25deg);opacity:.4}50%{transform:rotateZ(15deg);opacity:.8}75%{transform:rotateZ(-5deg);opacity:1}100%{transform:rotateX(0);opacity:1}}@keyframes swal2-toast-show{0%{transform:translateY(-0.625em) rotateZ(2deg)}33%{transform:translateY(0) rotateZ(-2deg)}66%{transform:translateY(0.3125em) rotateZ(2deg)}100%{transform:translateY(0) rotateZ(0deg)}}@keyframes swal2-toast-hide{100%{transform:rotateZ(1deg);opacity:0}}@keyframes swal2-toast-animate-success-line-tip{0%{top:.5625em;left:.0625em;width:0}54%{top:.125em;left:.125em;width:0}70%{top:.625em;left:-0.25em;width:1.625em}84%{top:1.0625em;left:.75em;width:.5em}100%{top:1.125em;left:.1875em;width:.75em}}@keyframes swal2-toast-animate-success-line-long{0%{top:1.625em;right:1.375em;width:0}65%{top:1.25em;right:.9375em;width:0}84%{top:.9375em;right:0;width:1.125em}100%{top:.9375em;right:.1875em;width:1.375em}}');const ub={class:"p-4 md:p-6 lg:p-8"},db={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-6"},fb={class:"flex flex-col md:flex-row gap-4"},pb={class:"flex-grow"},hb={class:"relative"},mb={class:"flex flex-col sm:flex-row gap-4"},gb={class:"bg-white shadow rounded-lg overflow-hidden"},wb={class:"overflow-x-auto w-full"},bb={class:"min-w-full divide-y divide-gray-200 table-fixed md:table-auto"},vb={class:"bg-white divide-y divide-gray-200"},yb={key:0},xb={key:1},kb={colspan:"3",class:"px-4 sm:px-6 py-4 text-center text-red-500"},_b={key:2},Cb={class:"px-4 sm:px-6 py-4"},$b={class:"flex items-center"},Eb={class:"flex-shrink-0 h-8 w-8 sm:h-10 sm:w-10"},Sb={class:"h-8 w-8 sm:h-10 sm:w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold"},Ab={class:"ml-2 sm:ml-4 overflow-hidden"},Pb={class:"text-sm font-medium text-gray-900 truncate"},Tb={class:"px-4 sm:px-6 py-4"},jb={class:"px-4 sm:px-6 py-4 text-right text-xs sm:text-sm font-medium"},Rb=["onClick"],Mb={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},Bb={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},Ob={class:"flex w-full justify-between sm:hidden"},Ib=["disabled"],Db={class:"text-sm text-gray-700"},Lb=["disabled"],Fb={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Ub={class:"text-sm text-gray-700"},Nb={class:"font-medium"},Vb={class:"font-medium"},Hb={class:"font-medium"},zb={key:0},qb={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},Kb=["disabled"],Wb={key:0,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},Jb=["onClick"],Gb=["disabled"],Yb=Pe({__name:"Customers",setup(e){const t=Jn(),s=W(!1),o=W(!1),n=W(null),r=W(""),l=W(""),a=Y(()=>t.loading),f=Y(()=>t.error),u=W(10),c=W(0),d=Y(()=>t.total);Ve(async()=>{await p()});const p=async()=>{await t.fetchCustomers({$skip:c.value,$limit:u.value,$keywords:r.value||void 0,status:l.value||void 0})},h=Y(()=>t.customers);De([r,l],()=>{c.value=0,p()});const v=()=>{n.value=null,o.value=!1,s.value=!0},$=_=>{n.value=_,o.value=!0,s.value=!0},y=()=>{s.value=!1,n.value=null},x=async _=>{try{o.value&&n.value&&n.value._id!==void 0?await t.updateCustomer(n.value._id,_):await t.createCustomer(_),y(),ys.fire({title:"Success!",text:"Customer saved successfully",icon:"success",showConfirmButton:!1,timer:1e3}),await p()}catch(E){console.error("Error saving customer:",E),ys.fire({title:"Error!",text:"Failed to save customer",icon:"error",showConfirmButton:!1,timer:1e3})}},m=_=>{typeof _=="number"&&(c.value=(_-1)*u.value,p())},b=()=>{c.value+u.value<d.value&&(c.value+=u.value,p())},T=()=>{c.value-u.value>=0&&(c.value-=u.value,p())},L=Y(()=>Math.floor(c.value/u.value)+1),J=Y(()=>Math.ceil(d.value/u.value)),j=Y(()=>{const _=[];if(J.value<=5)for(let q=1;q<=J.value;q++)_.push(q);else{_.push(1);let q=Math.max(2,L.value-1),we=Math.min(J.value-1,q+5-3);q=Math.max(2,we-2),q>2&&_.push("...");for(let Te=q;Te<=we;Te++)_.push(Te);we<J.value-1&&_.push("..."),_.push(J.value)}return _});return(_,E)=>(C(),S("div",ub,[E[15]||(E[15]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Customers",-1)),i("div",db,[i("div",fb,[i("div",pb,[E[3]||(E[3]=i("label",{for:"search",class:"sr-only"},"Search customers",-1)),i("div",hb,[E[2]||(E[2]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"text",id:"search","onUpdate:modelValue":E[0]||(E[0]=q=>r.value=q),class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search customers..."},null,512),[[ye,r.value]])])]),i("div",mb,[oe(i("select",{"onUpdate:modelValue":E[1]||(E[1]=q=>l.value=q),class:"block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},E[4]||(E[4]=[i("option",{value:""},"All Status",-1),i("option",{value:"active"},"Active",-1),i("option",{value:"inactive"},"Inactive",-1)]),512),[[Ne,l.value]]),i("button",{onClick:v,class:"whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},E[5]||(E[5]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" Add Customer ")]))])])]),ge(yg,{show:s.value,customer:n.value,isEdit:o.value,onClose:y,onSave:x},null,8,["show","customer","isEdit"]),i("div",gb,[i("div",wb,[i("table",bb,[E[8]||(E[8]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-3/5"}," Customer Name "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5"}," Status "),i("th",{scope:"col",class:"relative px-4 sm:px-6 py-3 w-1/5"},[i("span",{class:"sr-only"},"Actions")])])],-1)),i("tbody",vb,[a.value?(C(),S("tr",yb,E[6]||(E[6]=[i("td",{colspan:"3",class:"px-4 sm:px-6 py-4 text-center"},[i("div",{class:"flex justify-center"},[i("svg",{class:"animate-spin h-5 w-5 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),i("span",{class:"ml-2"},"Loading customers...")])],-1)]))):f.value?(C(),S("tr",xb,[i("td",kb,R(f.value),1)])):h.value.length===0?(C(),S("tr",_b,E[7]||(E[7]=[i("td",{colspan:"3",class:"px-4 sm:px-6 py-4 text-center text-gray-500"}," No customers found. ",-1)]))):de("",!0),(C(!0),S(ie,null,xe(h.value,q=>(C(),S("tr",{key:q.id,class:"hover:bg-gray-50"},[i("td",Cb,[i("div",$b,[i("div",Eb,[i("div",Sb,R(q.name&&q.name.length>0?q.name.charAt(0).toUpperCase():"?"),1)]),i("div",Ab,[i("div",Pb,R(q.name||"Unnamed Company"),1)])])]),i("td",Tb,[i("span",{class:ne(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full",q.status==="active"?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"])},R(q.status?"Active":q.status||"Inactive"),3)]),i("td",jb,[i("button",{onClick:we=>$(q),class:"text-blue-600 hover:text-blue-900"}," Edit ",8,Rb)])]))),128))])]),i("div",Mb,[i("div",Bb,[i("div",Ob,[i("button",{onClick:T,disabled:c.value===0,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",c.value===0?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Previous ",10,Ib),i("span",Db," Page "+R(L.value)+" of "+R(J.value),1),i("button",{onClick:b,disabled:c.value+u.value>=d.value,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",c.value+u.value>=d.value?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Next ",10,Lb)]),i("div",Fb,[i("div",null,[i("p",Ub,[E[9]||(E[9]=ce(" Showing ")),i("span",Nb,R(h.value.length>0?c.value+1:0),1),E[10]||(E[10]=ce(" to ")),i("span",Vb,R(Math.min(c.value+h.value.length,d.value)),1),E[11]||(E[11]=ce(" of ")),i("span",Hb,R(d.value),1),E[12]||(E[12]=ce(" customers "))])]),d.value>0?(C(),S("div",zb,[i("nav",qb,[i("button",{onClick:T,disabled:c.value===0,class:ne(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium",c.value===0?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},E[13]||(E[13]=[i("span",{class:"sr-only"},"Previous",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,Kb),(C(!0),S(ie,null,xe(j.value,(q,we)=>(C(),S(ie,{key:we},[q==="..."?(C(),S("span",Wb," ... ")):(C(),S("button",{key:1,onClick:Te=>m(q),class:ne(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",L.value===q?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},R(q),11,Jb))],64))),128)),i("button",{onClick:b,disabled:c.value+u.value>=d.value,class:ne(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium",c.value+u.value>=d.value?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},E[14]||(E[14]=[i("span",{class:"sr-only"},"Next",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,Gb)])])):de("",!0)])])])])])]))}}),Zb={key:0,class:"block text-sm font-medium text-gray-700 mb-1"},Xb={key:0,class:"text-red-500"},Qb={class:"flex items-center"},ev={key:0,class:"block truncate"},tv={key:1,class:"block truncate text-gray-500"},sv={key:1,class:"text-red-500 text-xs mt-1"},ov={key:2,class:"absolute z-10 mt-1 w-full bg-white shadow-lg max-h-60 rounded-md py-1 text-base ring-1 ring-black ring-opacity-5 overflow-auto focus:outline-none sm:text-sm"},nv={key:0,class:"px-3 py-2 sticky top-0 bg-white border-b border-gray-200"},rv={key:1,class:"px-3 py-2 text-center text-gray-500"},iv={key:2,class:"px-3 py-2 text-center text-gray-500"},lv={key:3,class:"py-1"},av=["onClick"],cv=Pe({__name:"SearchableSelect",props:{modelValue:{type:[String,Number,Object],default:null},options:{type:Array,default:()=>[]},placeholder:{type:String,default:"Select an option"},label:{type:String,default:""},required:{type:Boolean,default:!1},disabled:{type:Boolean,default:!1},loading:{type:Boolean,default:!1},searchable:{type:Boolean,default:!0},valueKey:{type:String,default:"id"},labelKey:{type:String,default:"name"},clearable:{type:Boolean,default:!0},error:{type:String,default:""}},emits:["update:modelValue","change","search"],setup(e,{emit:t}){const s=e,o=t,n=W(!1),r=W(""),l=W(null),a=W(null),f=W(null),u=()=>{if(!s.modelValue){l.value=null;return}if(typeof s.modelValue=="object"){l.value=s.modelValue;return}const m=s.options.find(b=>b[s.valueKey]===s.modelValue);l.value=m||null};De(()=>s.modelValue,u,{immediate:!0}),De(()=>s.options,u);const c=Y(()=>{if(!r.value)return s.options;const m=r.value.toLowerCase();return s.options.filter(b=>b[s.labelKey].toLowerCase().includes(m))}),d=Y(()=>l.value?l.value[s.labelKey]:""),p=()=>{s.disabled||(n.value=!n.value,n.value&&(r.value="",setTimeout(()=>{f.value&&s.searchable&&f.value.focus()},100)))},h=m=>{l.value=m,o("update:modelValue",m[s.valueKey]),o("change",m),n.value=!1,r.value=""},v=m=>{m.stopPropagation(),l.value=null,o("update:modelValue",null),o("change",null)},$=m=>{const b=m.target;r.value=b.value,o("search",r.value)},y=m=>{a.value&&!a.value.contains(m.target)&&(n.value=!1)},x=m=>{if(n.value)switch(m.key){case"Escape":n.value=!1;break;case"Enter":c.value.length>0&&h(c.value[0]);break}};return Ve(()=>{document.addEventListener("click",y),document.addEventListener("keydown",x)}),qa(()=>{document.removeEventListener("click",y),document.removeEventListener("keydown",x)}),(m,b)=>(C(),S("div",{class:"relative w-full",ref_key:"dropdownRef",ref:a},[e.label?(C(),S("label",Zb,[ce(R(e.label)+" ",1),e.required?(C(),S("span",Xb,"*")):de("",!0)])):de("",!0),i("div",{onClick:p,class:ne(["mt-1 relative w-full cursor-pointer bg-white border rounded-md shadow-sm pl-3 pr-10 py-2 text-left focus:outline-none focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",{"border-red-500":e.error,"border-gray-300":!e.error,"bg-gray-100":e.disabled,"cursor-not-allowed":e.disabled}])},[i("div",Qb,[l.value?(C(),S("span",ev,R(d.value),1)):(C(),S("span",tv,R(e.placeholder),1)),l.value&&e.clearable&&!e.disabled?(C(),S("button",{key:2,type:"button",onClick:v,class:"absolute inset-y-0 right-8 flex items-center pr-2 text-gray-400 hover:text-gray-500"},b[2]||(b[2]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))):de("",!0),b[3]||(b[3]=i("span",{class:"absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z","clip-rule":"evenodd"})])],-1))])],2),e.error?(C(),S("div",sv,R(e.error),1)):de("",!0),n.value?(C(),S("div",ov,[e.searchable?(C(),S("div",nv,[oe(i("input",{ref_key:"inputRef",ref:f,type:"text",class:"block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search...","onUpdate:modelValue":b[0]||(b[0]=T=>r.value=T),onInput:$,onKeydown:b[1]||(b[1]=zt(()=>{},["stop"]))},null,544),[[ye,r.value]])])):de("",!0),e.loading?(C(),S("div",rv," Loading... ")):c.value.length===0?(C(),S("div",iv," No results found ")):(C(),S("ul",lv,[(C(!0),S(ie,null,xe(c.value,T=>(C(),S("li",{key:T[e.valueKey],onClick:L=>h(T),class:ne(["px-3 py-2 cursor-pointer hover:bg-gray-100",{"bg-blue-100":l.value&&l.value[e.valueKey]===T[e.valueKey]}])},R(T[e.labelKey]),11,av))),128))]))])):de("",!0)],512))}}),uv={key:0,class:"inline-flex items-center"},dv={key:1},Lo=Pe({__name:"CustomerName",props:{customerId:{type:String,default:null}},setup(e){const t=e,s=Jn(),o=W("No Customer"),n=W(!1),r=async()=>{if(!t.customerId){o.value="No Customer";return}n.value=!0;try{const l=await s.ensureCustomer(t.customerId);l&&l.name?o.value=l.name:o.value=`Customer #${t.customerId}`}catch(l){console.error(`Error loading customer with ID ${t.customerId}:`,l),o.value=`Customer #${t.customerId}`}finally{n.value=!1}};return Ve(r),De(()=>t.customerId,r),(l,a)=>n.value?(C(),S("span",uv,a[0]||(a[0]=[i("svg",{class:"animate-spin h-4 w-4 mr-1 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),ce(" Loading... ")]))):(C(),S("span",dv,R(o.value),1))}}),fv={key:0,class:"fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50"},pv={class:"bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto"},hv={class:"p-6"},mv={class:"flex justify-between items-center mb-4"},gv={class:"text-xl font-semibold text-gray-900"},wv={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},bv={key:0,class:"grid grid-cols-1 gap-4"},vv={class:"flex justify-end space-x-3 pt-4"},yv={type:"submit",class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},xv=Pe({__name:"ProjectForm",props:{show:{type:Boolean,required:!0},customers:{type:Array,default:()=>[]},project:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(e,{emit:t}){const s=e,o=t,n=W(!1),r=W({name:"",description:"",startDate:"",endDate:"",stage:"in-progress",salesperson:"",customer:"",type:"normal"});Ve(()=>{s.project&&l()}),De(()=>s.project,()=>{s.project&&l()},{deep:!0});const l=()=>{s.project&&(r.value={name:s.project.name||"",description:s.project.description||"",startDate:s.project.startDate||"",endDate:s.project.endDate||"",stage:s.project.stage||"planning",salesperson:s.project.salesperson||"",customer:s.project.customer||"",type:s.project.type||"normal"})},a=()=>{r.value={name:"",description:"",startDate:"",endDate:"",stage:"planning",salesperson:"",customer:"",type:"normal"}},f=()=>{s.isEdit||a(),o("close")},u=()=>{if(!r.value.name.trim()){alert("Project name is required");return}const p={...r.value};o("save",p),f()},c=p=>{console.log("Search query:",p)},d=p=>{console.log("Selected option:",p)};return(p,h)=>e.show?(C(),S("div",fv,[i("div",pv,[i("div",hv,[i("div",mv,[i("h2",gv,R(e.isEdit?"Edit Project":"Create New Project"),1),i("button",{onClick:f,class:"text-gray-400 hover:text-gray-500"},h[6]||(h[6]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("form",{onSubmit:zt(u,["prevent"]),class:"space-y-4"},[i("div",null,[h[8]||(h[8]=i("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Project Type",-1)),oe(i("select",{"onUpdate:modelValue":h[0]||(h[0]=v=>r.value.type=v),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},h[7]||(h[7]=[i("option",{value:"normal"},"Normal",-1),i("option",{value:"hifi"},"HiFi Infra Set Up",-1)]),512),[[Ne,r.value.type]])]),i("div",null,[h[9]||(h[9]=i("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Project Name",-1)),oe(i("input",{type:"text",id:"name","onUpdate:modelValue":h[1]||(h[1]=v=>r.value.name=v),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,r.value.name]])]),i("div",null,[h[10]||(h[10]=i("label",{for:"description",class:"block text-sm font-medium text-gray-700"},"Description",-1)),oe(i("textarea",{id:"description","onUpdate:modelValue":h[2]||(h[2]=v=>r.value.description=v),rows:"3",class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,r.value.description]])]),i("div",wv,[i("div",null,[h[12]||(h[12]=i("label",{for:"stage",class:"block text-sm font-medium text-gray-700"},"Stage",-1)),oe(i("select",{id:"stage","onUpdate:modelValue":h[3]||(h[3]=v=>r.value.stage=v),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},h[11]||(h[11]=[i("option",{value:"planning"},"Planning",-1),i("option",{value:"in-progress"},"In Progress",-1),i("option",{value:"completed"},"Completed",-1)]),512),[[Ne,r.value.stage]])])]),r.value.type==="normal"?(C(),S("div",bv,[i("div",null,[h[13]||(h[13]=i("label",{for:"customer",class:"block text-sm font-medium text-gray-700"},"Customer",-1)),e.project&&e.project.customer&&!n.value?(C(),zs(Lo,{key:0,customerId:e.project.customer},null,8,["customerId"])):de("",!0),n.value?(C(),zs(cv,{key:1,modelValue:r.value.customer,"onUpdate:modelValue":h[4]||(h[4]=v=>r.value.customer=v),options:e.customers,placeholder:"Choose a customer",required:"",onChange:d,onSearch:c},null,8,["modelValue","options"])):de("",!0),i("button",{onClick:h[5]||(h[5]=zt(v=>n.value=!n.value,["prevent"])),class:"text-xs ml-2 text-blue-600"},R(n.value?"Cancel":"Change Client"),1)])])):de("",!0),i("div",vv,[i("button",{type:"button",onClick:f,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),i("button",yv,R(e.isEdit?"Update":"Create")+" Project ",1)])],32)])])])):de("",!0)}}),kv={key:0,class:"fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50"},_v={class:"bg-white rounded-lg shadow-xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-y-auto"},Cv={class:"p-6"},$v={class:"flex justify-between items-start mb-4"},Ev={class:"flex items-center"},Sv={class:"text-2xl font-semibold text-gray-900"},Av={class:"space-y-6"},Pv={class:"text-gray-700"},Tv={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},jv={class:"space-y-2"},Rv={class:"flex items-center text-sm text-gray-600"},Mv={class:"flex items-center text-sm text-gray-600"},Bv={class:"flex items-center text-sm text-gray-600"},Ov={class:"space-y-2"},Iv={class:"flex justify-between mb-1"},Dv={class:"text-sm font-medium text-gray-700"},Lv={class:"w-full bg-gray-200 rounded-full h-2.5"},Fv={class:"flex items-center text-sm text-gray-600 mt-4"},Uv={class:"grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4"},Nv=["src","alt"],Vv={class:"text-sm font-medium text-gray-900"},Hv={class:"text-xs text-gray-500"},zv={class:"space-y-4"},qv={class:"text-sm text-gray-600"},Kv={class:"text-xs text-gray-500"},Wv=Pe({__name:"ProjectDetailsModal",props:{show:{type:Boolean,required:!0},project:{type:Object,default:null}},emits:["close","edit"],setup(e,{emit:t}){const s=e,o=t,n=()=>{o("close")},r=()=>{o("edit",s.project)};return(l,a)=>{var f,u,c,d,p,h,v,$,y,x,m,b;return e.show?(C(),S("div",kv,[i("div",_v,[i("div",Cv,[i("div",$v,[i("div",Ev,[i("h2",Sv,R((f=e.project)==null?void 0:f.name),1),i("span",{class:ne(`ml-4 px-2 py-1 text-xs font-medium rounded-full ${(u=e.project)==null?void 0:u.statusClass}`)},R((c=e.project)==null?void 0:c.stage),3)]),i("button",{onClick:n,class:"text-gray-400 hover:text-gray-500"},a[0]||(a[0]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("div",Av,[i("div",null,[a[1]||(a[1]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Description",-1)),i("p",Pv,R((d=e.project)==null?void 0:d.description),1)]),i("div",Tv,[i("div",null,[a[9]||(a[9]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Key Details",-1)),i("div",jv,[i("div",Rv,[a[3]||(a[3]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),i("span",null,[a[2]||(a[2]=i("strong",null,"Start Date:",-1)),ce(" "+R((p=e.project)==null?void 0:p.createdAt),1)])]),i("div",Mv,[a[5]||(a[5]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z"})],-1)),i("span",null,[a[4]||(a[4]=i("strong",null,"Sales Person:",-1)),ce(" "+R((h=e.project)==null?void 0:h.salesperson),1)])]),i("div",Bv,[a[8]||(a[8]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),i("span",null,[a[6]||(a[6]=i("strong",null,"Client:",-1)),a[7]||(a[7]=ce()),e.project&&e.project.customer?(C(),zs(Lo,{key:0,customerId:e.project.customer},null,8,["customerId"])):de("",!0)])])])]),i("div",null,[a[13]||(a[13]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Progress",-1)),i("div",Ov,[i("div",null,[i("div",Iv,[a[10]||(a[10]=i("span",{class:"text-sm font-medium text-gray-700"},"Overall Progress",-1)),i("span",Dv,R((v=e.project)==null?void 0:v.progress)+"%",1)]),i("div",Lv,[i("div",{class:"bg-blue-600 h-2.5 rounded-full",style:Sn(`width: ${($=e.project)==null?void 0:$.progress}%`)},null,4)])]),i("div",Fv,[a[12]||(a[12]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2 text-gray-400",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 5H7a2 2 0 00-2 2v12a2 2 0 002 2h10a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"})],-1)),i("span",null,[a[11]||(a[11]=i("strong",null,"Tasks:",-1)),ce(" "+R((y=e.project)==null?void 0:y.completedTasks)+" of "+R((x=e.project)==null?void 0:x.totalTasks)+" completed",1)])])])])]),i("div",null,[a[14]||(a[14]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Team Members",-1)),i("div",Uv,[(C(!0),S(ie,null,xe((m=e.project)==null?void 0:m.team,(T,L)=>(C(),S("div",{key:L,class:"flex items-center space-x-3 p-3 bg-gray-50 rounded-lg"},[i("img",{class:"h-10 w-10 rounded-full",src:T.avatar,alt:T.name},null,8,Nv),i("div",null,[i("p",Vv,R(T.name),1),i("p",Hv,R(T.role),1)])]))),128))])]),i("div",null,[a[16]||(a[16]=i("h3",{class:"text-lg font-medium text-gray-900 mb-2"},"Recent Activities",-1)),i("div",zv,[(C(!0),S(ie,null,xe((b=e.project)==null?void 0:b.activities,(T,L)=>(C(),S("div",{key:L,class:"flex space-x-3"},[a[15]||(a[15]=i("div",{class:"flex-shrink-0"},[i("div",{class:"h-8 w-8 rounded-full bg-blue-100 flex items-center justify-center"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-blue-600",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm1-12a1 1 0 10-2 0v4a1 1 0 00.293.707l2.828 2.829a1 1 0 101.415-1.415L11 9.586V6z","clip-rule":"evenodd"})])])],-1)),i("div",null,[i("p",qv,R(T.description),1),i("p",Kv,R(T.date)+" by "+R(T.user),1)])]))),128))])])]),i("div",{class:"mt-8 flex justify-end space-x-3"},[i("button",{onClick:n,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Close "),i("button",{onClick:r,class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Edit Project ")])])])])):de("",!0)}}}),Jv={class:"p-4 md:p-6 lg:p-8"},Gv={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-6"},Yv={class:"flex flex-col md:flex-row gap-4"},Zv={class:"flex-grow"},Xv={class:"relative"},Qv={class:"flex flex-col sm:flex-row gap-4"},e2={class:"bg-white shadow rounded-lg overflow-hidden"},t2={class:"overflow-x-auto w-full"},s2={class:"min-w-full divide-y divide-gray-200 table-fixed md:table-auto"},o2={class:"bg-white divide-y divide-gray-200"},n2={key:0},r2={key:1},i2={colspan:"4",class:"px-4 sm:px-6 py-4 text-center text-red-500"},l2={key:2},a2={class:"px-4 sm:px-6 py-4"},c2={class:"flex items-center"},u2={class:"overflow-hidden"},d2={class:"text-sm font-medium text-gray-900 truncate max-w-xs"},f2={class:"text-xs sm:text-sm text-gray-500 truncate max-w-xs"},p2={class:"px-4 sm:px-6 py-4"},h2={class:"text-xs sm:text-sm text-gray-900 truncate max-w-xs"},m2={class:"px-4 sm:px-6 py-4"},g2={class:"px-4 sm:px-6 py-4"},w2={class:"text-sm sm:text-sm text-gray-900 truncate max-w-xs"},b2={class:"px-4 sm:px-6 py-4 text-right text-xs sm:text-sm font-medium"},v2={class:"flex flex-col sm:flex-row sm:justify-end space-y-1 sm:space-y-0 sm:space-x-2"},y2=["onClick"],x2=["onClick"],k2=["onClick"],_2={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},C2={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},$2={class:"flex w-full justify-between sm:hidden"},E2=["disabled"],S2={class:"text-sm text-gray-700"},A2=["disabled"],P2={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},T2={class:"text-sm text-gray-700"},j2={class:"font-medium"},R2={class:"font-medium"},M2={class:"font-medium"},B2={key:0},O2={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},I2=["disabled"],D2={key:0,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},L2=["onClick"],F2=["disabled"],U2=Pe({__name:"Projects",setup(e){const t=Kn(),s=Jn(),o=W(!1),n=W(!1),r=W(null),l=W(!1),a=W(""),f=W(""),u=Y(()=>t.loading),c=Y(()=>t.error),d=W(10),p=W(0),h=Y(()=>t.total);Ve(async()=>{await v()});const v=async()=>{await t.fetchProjects({$skip:p.value,$limit:d.value,$keywords:a.value||void 0,stage:f.value||void 0})},$=Y(()=>s.customers),y=Y(()=>t.projects);De([a,f],()=>{p.value=0,v()});const x=async()=>{r.value=null,l.value=!1,await s.fetchCustomers({$limit:20,$skip:0}),o.value=!0},m=async X=>{r.value=X,l.value=!0,await s.fetchCustomers({$limit:20,$skip:0}),X.customer&&await s.ensureCustomer(X.customer),o.value=!0},b=()=>{o.value=!1,r.value=null},T=async X=>{try{if(l.value&&r.value){const F=r.value._id||r.value.id;F!==void 0&&await t.updateProject(F,X)}else await t.createProject(X);b(),ys.fire({title:"Success!",text:"Project saved successfully",icon:"success",showConfirmButton:!1,timer:1e3}),await v()}catch(F){console.error("Error saving project:",F),ys.fire({title:"Error!",text:"Failed to save project",icon:"error",showConfirmButton:!1,timer:1e3})}},L=X=>{r.value=X,n.value=!0},J=()=>{n.value=!1,r.value=null},j=X=>{J(),m(X)},_=async X=>{if(confirm("Are you sure you want to delete this project?"))try{const F={status:!1};await t.updateProject(X,F),await v()}catch(F){console.error("Error deleting project:",F)}},E=X=>{typeof X=="number"&&(p.value=(X-1)*d.value,v())},q=()=>{p.value+d.value<h.value&&(p.value+=d.value,v())},we=()=>{p.value-d.value>=0&&(p.value-=d.value,v())},Te=Y(()=>Math.floor(p.value/d.value)+1),je=Y(()=>Math.ceil(h.value/d.value)),Ot=Y(()=>{const X=[];if(je.value<=5)for(let G=1;G<=je.value;G++)X.push(G);else{X.push(1);let G=Math.max(2,Te.value-1),Fe=Math.min(je.value-1,G+5-3);G=Math.max(2,Fe-2),G>2&&X.push("...");for(let rt=G;rt<=Fe;rt++)X.push(rt);Fe<je.value-1&&X.push("..."),X.push(je.value)}return X});function xt(X){if(!X)return"bg-gray-100 text-gray-800";switch(X.toLowerCase()){case"completed":return"bg-green-100 text-green-800";case"in progress":return"bg-blue-100 text-blue-800";case"on hold":return"bg-yellow-100 text-yellow-800";default:return"bg-gray-100 text-gray-800"}}return(X,F)=>(C(),S("div",Jv,[F[16]||(F[16]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Projects",-1)),i("div",Gv,[i("div",Yv,[i("div",Zv,[F[4]||(F[4]=i("label",{for:"search",class:"sr-only"},"Search projects",-1)),i("div",Xv,[F[3]||(F[3]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"text",id:"search","onUpdate:modelValue":F[0]||(F[0]=G=>a.value=G),class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search projects..."},null,512),[[ye,a.value]])])]),i("div",Qv,[oe(i("select",{"onUpdate:modelValue":F[1]||(F[1]=G=>f.value=G),class:"block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},F[5]||(F[5]=[i("option",{value:""},"All Stage",-1),i("option",{value:"planning"},"Planning",-1),i("option",{value:"in progress"},"In Progress",-1),i("option",{value:"completed"},"Completed",-1)]),512),[[Ne,f.value]]),i("button",{onClick:F[2]||(F[2]=()=>x()),class:"whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},F[6]||(F[6]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" New Project ")]))])])]),i("div",e2,[i("div",t2,[i("table",s2,[F[9]||(F[9]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-2/5"}," Project Name "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5"}," Customer "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5"}," Stage "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/5"}," Salesperson "),i("th",{scope:"col",class:"relative px-4 sm:px-6 py-3 w-1/5"},[i("span",{class:"sr-only"},"Actions")])])],-1)),i("tbody",o2,[u.value?(C(),S("tr",n2,F[7]||(F[7]=[i("td",{colspan:"4",class:"px-4 sm:px-6 py-4 text-center"},[i("div",{class:"flex justify-center"},[i("svg",{class:"animate-spin h-5 w-5 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})]),i("span",{class:"ml-2"},"Loading projects...")])],-1)]))):c.value?(C(),S("tr",r2,[i("td",i2,R(c.value),1)])):y.value.length===0?(C(),S("tr",l2,F[8]||(F[8]=[i("td",{colspan:"4",class:"px-4 sm:px-6 py-4 text-center text-gray-500"}," No projects found. ",-1)]))):de("",!0),(C(!0),S(ie,null,xe(y.value,G=>(C(),S("tr",{key:G.id,class:"hover:bg-gray-50"},[i("td",a2,[i("div",c2,[i("div",u2,[i("div",d2,R(G.name||"Unnamed Project"),1),i("div",f2,R(G.description),1)])])]),i("td",p2,[i("div",h2,[ge(Lo,{customerId:G.customer},null,8,["customerId"])])]),i("td",m2,[i("span",{class:ne(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full capitalize whitespace-nowrap",xt(G.stage)])},R(G.stage||"No stage"),3)]),i("td",g2,[i("span",w2,R(G.salesperson),1)]),i("td",b2,[i("div",v2,[i("button",{onClick:()=>m(G),class:"text-blue-600 hover:text-blue-900"}," Edit ",8,y2),i("button",{onClick:Fe=>L(G),class:"text-gray-600 hover:text-gray-900"}," Details ",8,x2),i("button",{onClick:Fe=>_(String(G._id||G.id||0)),class:"text-red-600 hover:text-red-900"}," Delete ",8,k2)])])]))),128))])]),i("div",_2,[i("div",C2,[i("div",$2,[i("button",{onClick:we,disabled:p.value===0,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",p.value===0?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Previous ",10,E2),i("span",S2," Page "+R(Te.value)+" of "+R(je.value),1),i("button",{onClick:q,disabled:p.value+d.value>=h.value,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",p.value+d.value>=h.value?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Next ",10,A2)]),i("div",P2,[i("div",null,[i("p",T2,[F[10]||(F[10]=ce(" Showing ")),i("span",j2,R(y.value.length>0?p.value+1:0),1),F[11]||(F[11]=ce(" to ")),i("span",R2,R(Math.min(p.value+y.value.length,h.value)),1),F[12]||(F[12]=ce(" of ")),i("span",M2,R(h.value),1),F[13]||(F[13]=ce(" projects "))])]),h.value>0?(C(),S("div",B2,[i("nav",O2,[i("button",{onClick:we,disabled:p.value===0,class:ne(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium",p.value===0?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},F[14]||(F[14]=[i("span",{class:"sr-only"},"Previous",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,I2),(C(!0),S(ie,null,xe(Ot.value,(G,Fe)=>(C(),S(ie,{key:Fe},[G==="..."?(C(),S("span",D2," ... ")):(C(),S("button",{key:1,onClick:rt=>E(G),class:ne(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",Te.value===G?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},R(G),11,L2))],64))),128)),i("button",{onClick:q,disabled:p.value+d.value>=h.value,class:ne(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium",p.value+d.value>=h.value?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},F[15]||(F[15]=[i("span",{class:"sr-only"},"Next",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,F2)])])):de("",!0)])])])])]),ge(xv,{show:o.value,customers:$.value,project:r.value,isEdit:l.value,onClose:b,onSave:T},null,8,["show","customers","project","isEdit"]),ge(Wv,{show:n.value,project:r.value,onClose:J,onEdit:j},null,8,["show","project"])]))}}),N2={key:0,class:"fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50"},V2={class:"bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto"},H2={class:"p-6"},z2={class:"flex justify-between items-center mb-4"},q2={class:"text-xl font-semibold text-gray-900"},K2={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},W2=["value"],J2={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},G2=["value"],Y2={class:"flex justify-end space-x-3 pt-4"},Z2={type:"submit",class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},X2=Pe({__name:"TicketForm",props:{show:{type:Boolean,required:!0},projects:{type:Array,default:()=>[]},teamMembers:{type:Array,default:()=>[]},ticket:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(e,{emit:t}){const s=e,o=t,n=W({id:null,title:"",description:"",status:"open",priority:"medium",project:"",assignedTo:"",dueDate:"",createdBy:"",createdAt:"",updatedAt:""});De(()=>s.ticket,u=>{u&&r(u)},{immediate:!0}),De(()=>s.show,u=>{u&&s.ticket?r(s.ticket):!u&&!s.isEdit&&l()});const r=u=>{u&&(n.value={id:u.id||null,title:u.title||"",description:u.description||"",status:u.status||"open",priority:u.priority||"medium",project:u.project||"",assignedTo:u.assignedTo||"",dueDate:u.dueDate||"",createdBy:u.createdBy||"",createdAt:u.createdAt||"",updatedAt:u.updatedAt||""})};Ve(()=>{s.ticket&&r(s.ticket)});const l=()=>{n.value={id:null,title:"",description:"",status:"open",priority:"medium",project:"",assignedTo:"",dueDate:"",createdBy:"",createdAt:"",updatedAt:""}},a=()=>{s.isEdit||l(),o("close")},f=()=>{const u={...n.value};s.isEdit||(u.createdAt=new Date().toISOString()),u.updatedAt=new Date().toISOString(),o("save",u),a()};return(u,c)=>e.show?(C(),S("div",N2,[i("div",V2,[i("div",H2,[i("div",z2,[i("h2",q2,R(e.isEdit?"Edit Ticket":"Create New Ticket"),1),i("button",{onClick:a,class:"text-gray-400 hover:text-gray-500"},c[7]||(c[7]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("form",{onSubmit:zt(f,["prevent"]),class:"space-y-4"},[i("div",null,[c[8]||(c[8]=i("label",{for:"title",class:"block text-sm font-medium text-gray-700"},"Ticket Title",-1)),oe(i("input",{type:"text",id:"title","onUpdate:modelValue":c[0]||(c[0]=d=>n.value.title=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.title]])]),i("div",null,[c[9]||(c[9]=i("label",{for:"description",class:"block text-sm font-medium text-gray-700"},"Description",-1)),oe(i("textarea",{id:"description","onUpdate:modelValue":c[1]||(c[1]=d=>n.value.description=d),rows:"4",class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.description]])]),i("div",K2,[i("div",null,[c[11]||(c[11]=i("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Status",-1)),oe(i("select",{id:"status","onUpdate:modelValue":c[2]||(c[2]=d=>n.value.status=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},c[10]||(c[10]=[i("option",{value:"open"},"Open",-1),i("option",{value:"in-progress"},"In Progress",-1),i("option",{value:"resolved"},"Resolved",-1),i("option",{value:"closed"},"Closed",-1)]),512),[[Ne,n.value.status]])]),i("div",null,[c[13]||(c[13]=i("label",{for:"priority",class:"block text-sm font-medium text-gray-700"},"Priority",-1)),oe(i("select",{id:"priority","onUpdate:modelValue":c[3]||(c[3]=d=>n.value.priority=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},c[12]||(c[12]=[i("option",{value:"low"},"Low",-1),i("option",{value:"medium"},"Medium",-1),i("option",{value:"high"},"High",-1),i("option",{value:"urgent"},"Urgent",-1)]),512),[[Ne,n.value.priority]])])]),i("div",null,[c[15]||(c[15]=i("label",{for:"project",class:"block text-sm font-medium text-gray-700"},"Related Project",-1)),oe(i("select",{id:"project","onUpdate:modelValue":c[4]||(c[4]=d=>n.value.project=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},[c[14]||(c[14]=i("option",{value:""},"Select a project",-1)),(C(!0),S(ie,null,xe(e.projects,d=>(C(),S("option",{key:d.id,value:d.id},R(d.name),9,W2))),128))],512),[[Ne,n.value.project]])]),i("div",J2,[i("div",null,[c[17]||(c[17]=i("label",{for:"assignedTo",class:"block text-sm font-medium text-gray-700"},"Assign To",-1)),oe(i("select",{id:"assignedTo","onUpdate:modelValue":c[5]||(c[5]=d=>n.value.assignedTo=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},[c[16]||(c[16]=i("option",{value:""},"Unassigned",-1)),(C(!0),S(ie,null,xe(e.teamMembers,d=>(C(),S("option",{key:d.id,value:d.id},R(d.firstName)+" "+R(d.lastName),9,G2))),128))],512),[[Ne,n.value.assignedTo]])]),i("div",null,[c[18]||(c[18]=i("label",{for:"dueDate",class:"block text-sm font-medium text-gray-700"},"Due Date",-1)),oe(i("input",{type:"date",id:"dueDate","onUpdate:modelValue":c[6]||(c[6]=d=>n.value.dueDate=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.dueDate]])])]),i("div",Y2,[i("button",{type:"button",onClick:a,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),i("button",Z2,R(e.isEdit?"Update Ticket":"Create Ticket"),1)])],32)])])])):de("",!0)}}),Q2={key:0,class:"fixed inset-0 bg-gray-500/75 flex items-center justify-center z-50"},ey={class:"bg-white rounded-lg shadow-xl max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto"},ty={class:"p-6"},sy={class:"flex justify-between items-start mb-6"},oy={class:"text-xl font-bold text-gray-900 mb-2"},ny={class:"flex items-center space-x-3"},ry={class:"space-y-6"},iy={class:"bg-gray-50 rounded-lg p-4"},ly={class:"text-gray-700 whitespace-pre-wrap"},ay={class:"w-full"},cy={key:1,class:"text-gray-700 text-lg"},uy={class:"w-full"},dy={class:"text-lg text-gray-900"},fy={class:"border-t pt-6"},py={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},hy={key:0},my={class:"text-sm text-gray-600"},gy={class:"text-sm text-gray-600"},wy={class:"text-sm text-gray-600"},by=Pe({__name:"TicketDetailsModal",props:{show:{type:Boolean,required:!0},ticket:{type:Object,default:null},projects:{type:Array,default:()=>[]},teamMembers:{type:Array,default:()=>[]}},emits:["close"],setup(e,{emit:t}){const s=e,o=t,n=Y(()=>{var u;return{open:"bg-blue-100 text-blue-800",close:"bg-gray-100 text-gray-800"}[((u=s.ticket)==null?void 0:u.statustxt)||""]||"bg-gray-100 text-gray-800"}),r=f=>f?new Date(f).toLocaleDateString("en-US",{year:"numeric",month:"long",day:"numeric"}):"Not set",l=f=>f?new Date(f).toLocaleString("en-US",{year:"numeric",month:"long",day:"numeric",hour:"2-digit",minute:"2-digit"}):"Not set",a=()=>{o("close")};return(f,u)=>{var d,p,h,v,$,y,x,m,b,T;const c=li("UserName");return e.show?(C(),S("div",Q2,[i("div",ey,[i("div",ty,[i("div",sy,[i("div",null,[i("h2",oy,R(`${(d=e.ticket)==null?void 0:d.ticketno} - ${(p=e.ticket)==null?void 0:p.title}`),1),i("div",ny,[i("span",{class:ne([n.value,"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize"])},R(((h=e.ticket)==null?void 0:h.statustxt)||"Unknown"),3),i("span",{class:ne(["inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium capitalize",{"bg-green-100 text-green-800":e.ticket.level==="1","bg-yellow-100 text-yellow-800":e.ticket.level==="2","bg-red-100 text-red-800":e.ticket.level==="3"}])}," Level "+R(((v=e.ticket)==null?void 0:v.level)||"?"),3)])]),i("button",{onClick:a,class:"text-gray-400 hover:text-gray-500"},u[0]||(u[0]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("div",ry,[i("div",null,[u[1]||(u[1]=i("h3",{class:"text-lg font-medium text-gray-900 mb-3"},"Description",-1)),i("div",iy,[i("p",ly,R((($=e.ticket)==null?void 0:$.description)||"No description provided"),1)])]),i("div",ay,[u[2]||(u[2]=i("h4",{class:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-2"},"Assigned To",-1)),e.ticket&&e.ticket.assignee.length>0?(C(!0),S(ie,{key:0},xe(e.ticket.assignee,L=>(C(),S("div",{class:"text-gray-700 text-lg",key:L.id},[ge(c,{userId:L.id},null,8,["userId"])]))),128)):(C(),S("div",cy," No users assigned "))]),i("div",uy,[i("div",null,[u[3]||(u[3]=i("h4",{class:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-2"},"Created By",-1)),i("p",dy,R(((y=e.ticket)==null?void 0:y.user)||"Unknown"),1)])]),i("div",fy,[i("div",py,[((x=e.ticket)==null?void 0:x.statustxt)==="close"?(C(),S("div",hy,[u[4]||(u[4]=i("h4",{class:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-2"},"Closed Date",-1)),i("p",my,R(r((m=e.ticket)==null?void 0:m.closingdate)),1)])):de("",!0),i("div",null,[u[5]||(u[5]=i("h4",{class:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-2"},"Created At",-1)),i("p",gy,R(l((b=e.ticket)==null?void 0:b.createdAt)),1)]),i("div",null,[u[6]||(u[6]=i("h4",{class:"text-sm font-medium text-gray-500 uppercase tracking-wide mb-2"},"Last Updated",-1)),i("p",wy,R(l((T=e.ticket)==null?void 0:T.updatedAt)),1)])])])]),i("div",{class:"flex justify-end pt-6 border-t mt-6"},[i("button",{onClick:a,class:"px-6 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Close ")])])])])):de("",!0)}}}),vy={class:"p-4 md:p-6 lg:p-8"},yy={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-6"},xy={class:"flex flex-col md:flex-row gap-4"},ky={class:"flex-grow"},_y={class:"relative"},Cy={class:"flex flex-col sm:flex-row gap-4"},$y={key:0,class:"flex justify-center items-center py-12"},Ey={key:1,class:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative my-6"},Sy={class:"block sm:inline"},Ay={key:2,class:"bg-white rounded-lg shadow p-6 text-center my-6"},Py={key:3,class:"bg-white shadow rounded-lg overflow-hidden"},Ty={class:"overflow-x-auto"},jy={class:"min-w-full divide-y divide-gray-200"},Ry={class:"bg-white divide-y divide-gray-200"},My={class:"px-6 py-4"},By={class:"flex flex-col"},Oy=["onClick"],Iy={class:"text-xs text-gray-700 mt-1 whitespace-nowrap"},Dy={class:"px-6 py-4 truncate text-sm text-gray-500 max-w-xs"},Ly={class:"px-6 py-4 whitespace-nowrap text-sm text-gray-500"},Fy={key:1},Uy={class:"px-6 py-4 whitespace-nowrap"},Ny={class:"px-6 py-4 whitespace-nowrap"},Vy={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Hy={class:"px-6 py-4 whitespace-nowrap"},zy={class:"flex justify-end space-x-2"},qy=["onClick"],Ky=["onClick"],Wy=["onClick"],Jy={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},Gy={class:"flex items-center justify-between"},Yy={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Zy={class:"text-sm text-gray-700"},Xy={class:"font-medium"},Qy=Pe({__name:"Tickets",setup(e){const t=iu(),s=Kn(),o=Wn(),n=W(!1),r=W(!1),l=W(null),a=W(!1),f=W(""),u=W(""),c=W(""),d=Y(()=>t.loading),p=Y(()=>t.error);Ve(async()=>{await Promise.all([t.fetchTickets(),s.fetchProjects(),o.fetchUsers()])});const h=Y(()=>t.tickets.filter(J=>{const j=f.value.toLowerCase(),_=f.value===""||J.title.toLowerCase().includes(j)||J.description&&J.description.toLowerCase().includes(j),E=u.value===""||J.statustxt===u.value,q=c.value===""||J.priority===c.value;return _&&E&&q})),v=J=>{if(!J)return"";const j=new Date(J);return new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"short",day:"numeric"}).format(j)},$=()=>{l.value=null,r.value=!1,n.value=!0},y=J=>{l.value={...J},r.value=!0,n.value=!0,a.value=!1},x=()=>{n.value=!1,r.value&&(r.value=!1,l.value=null)},m=async J=>{try{r.value&&l.value?await t.updateTicket(l.value.id,J):await t.createTicket(J),x()}catch(j){console.error("Error saving ticket:",j)}},b=J=>{l.value={...J},a.value=!0},T=()=>{a.value=!1,l.value=null},L=async J=>{if(J&&confirm("Are you sure you want to delete this ticket?"))try{await t.deleteTicket(J)}catch(j){console.error("Error deleting ticket:",j)}};return(J,j)=>(C(),S("div",vy,[j[20]||(j[20]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Tickets",-1)),i("div",yy,[i("div",xy,[i("div",ky,[j[4]||(j[4]=i("label",{for:"search",class:"sr-only"},"Search tickets",-1)),i("div",_y,[j[3]||(j[3]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"text",id:"search","onUpdate:modelValue":j[0]||(j[0]=_=>f.value=_),class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search tickets..."},null,512),[[ye,f.value]])])]),i("div",Cy,[oe(i("select",{"onUpdate:modelValue":j[1]||(j[1]=_=>u.value=_),class:"block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},j[5]||(j[5]=[ws('<option value="">All Status</option><option value="open">Open</option><option value="in-progress">In Progress</option><option value="resolved">Resolved</option><option value="closed">Closed</option>',5)]),512),[[Ne,u.value]]),oe(i("select",{"onUpdate:modelValue":j[2]||(j[2]=_=>c.value=_),class:"block w-full py-2 px-3 border border-gray-300 bg-white rounded-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},j[6]||(j[6]=[ws('<option value="">All Priorities</option><option value="low">Low</option><option value="medium">Medium</option><option value="high">High</option><option value="urgent">Urgent</option>',5)]),512),[[Ne,c.value]]),i("button",{onClick:$,class:"whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},j[7]||(j[7]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" New Ticket ")]))])])]),ge(X2,{show:n.value,ticket:l.value,isEdit:r.value,onClose:x,onSave:m},null,8,["show","ticket","isEdit"]),ge(by,{show:a.value,ticket:l.value,onClose:T,onEdit:y},null,8,["show","ticket"]),d.value?(C(),S("div",$y,j[8]||(j[8]=[i("svg",{class:"animate-spin h-8 w-8 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),i("span",{class:"ml-3 text-lg text-gray-600"},"Loading tickets...",-1)]))):p.value?(C(),S("div",Ey,[j[9]||(j[9]=i("strong",{class:"font-bold"},"Error!",-1)),i("span",Sy,R(p.value),1)])):h.value.length===0?(C(),S("div",Ay,[j[11]||(j[11]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"})],-1)),j[12]||(j[12]=i("h3",{class:"text-lg font-medium text-gray-900"},"No tickets found",-1)),j[13]||(j[13]=i("p",{class:"mt-2 text-gray-500"},"Get started by creating a new ticket.",-1)),i("button",{onClick:$,class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},j[10]||(j[10]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" New Ticket ")]))])):(C(),S("div",Py,[i("div",Ty,[i("table",jy,[j[17]||(j[17]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Ticket "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Customer "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Assigned To "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Level "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Date "),i("th",{scope:"col",class:"relative px-6 py-3"},[i("span",{class:"sr-only"},"Actions")])])],-1)),i("tbody",Ry,[(C(!0),S(ie,null,xe(h.value,_=>(C(),S("tr",{key:_.id,class:"hover:bg-gray-50"},[i("td",My,[i("div",By,[i("a",{href:"#",onClick:zt(E=>b(_),["prevent"]),class:"text-sm font-medium text-blue-600 hover:text-blue-900 whitespace-nowrap"},R(_.ticketno),9,Oy),i("div",Iy,R(_.title),1)])]),i("td",Dy,[ge(Lo,{customerId:_.customer},null,8,["customerId"])]),i("td",Ly,[_&&_.assignee.length>0?(C(!0),S(ie,{key:0},xe(_.assignee,E=>(C(),S("div",{key:E.id},[ge(fn,{userId:E.id},null,8,["userId"])]))),128)):(C(),S("div",Fy," No users assigned "))]),i("td",Uy,[i("span",{class:ne(["px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full",{"bg-blue-100 text-blue-800":_.statustxt==="open","bg-gray-100 text-gray-800":_.statustxt==="close"}])},R(_.statustxt?_.statustxt==="in-progress"?"In Progress":_.statustxt.charAt(0).toUpperCase()+_.statustxt.slice(1):"Unknown"),3)]),i("td",Ny,[i("span",{class:ne(["px-2 py-1 inline-flex text-xs leading-5 font-semibold rounded-full",{"bg-green-100 text-green-800":_.level==="1","bg-yellow-100 text-yellow-800":_.level==="2","bg-red-100 text-red-800":_.level==="3"}])},R(_.level),3)]),i("td",Vy,R(v(_.createdAt)),1),i("td",Hy,[i("div",zy,[i("button",{onClick:E=>b(_),class:"text-blue-600 hover:text-blue-900",title:"View Details"},j[14]||(j[14]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{d:"M10 12a2 2 0 100-4 2 2 0 000 4z"}),i("path",{"fill-rule":"evenodd",d:"M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z","clip-rule":"evenodd"})],-1)]),8,qy),i("button",{onClick:E=>y(_),class:"text-indigo-600 hover:text-indigo-900",title:"Edit"},j[15]||(j[15]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,Ky),i("button",{onClick:E=>L(_.id),class:"text-red-600 hover:text-red-900",title:"Delete"},j[16]||(j[16]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)]),8,Wy)])])]))),128))])])]),i("div",Jy,[i("div",Gy,[i("div",Yy,[i("div",null,[i("p",Zy,[j[18]||(j[18]=ce(" Showing ")),i("span",Xy,R(h.value.length),1),j[19]||(j[19]=ce(" tickets "))])])])])])]))]))}}),td=os("department",{state:()=>({departments:[],selectedDepartment:null,loading:!1,error:null,total:0}),getters:{getDepartmentById:e=>t=>e.departments.find(s=>s.id===t)},actions:{async fetchDepartments(e){var t,s;this.loading=!0,this.error=null;try{const o=await Ms.getAll(e);this.departments=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch departments",console.error("Error fetching departments:",o)}finally{this.loading=!1}},async fetchDepartmentById(e){var t,s;this.loading=!0,this.error=null;try{this.selectedDepartment=await Ms.getById(e)}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch department",console.error(`Error fetching department with ID ${e}:`,o)}finally{this.loading=!1}},async createDepartment(e){var t,s;this.loading=!0,this.error=null;try{const o=await Ms.create(e);return this.departments.push(o),o}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create department",console.error("Error creating department:",o),o}finally{this.loading=!1}},async updateDepartment(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await Ms.update(e,t),r=this.departments.findIndex(l=>l.id===e);return r!==-1&&(this.departments[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update department",console.error(`Error updating department with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteDepartment(e){var t,s;this.loading=!0,this.error=null;try{await Ms.delete(e),this.departments=this.departments.filter(o=>o.id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete department",console.error(`Error deleting department with ID ${e}:`,o),o}finally{this.loading=!1}}}}),ex=os("departmentTask",{state:()=>({departmentTasks:[],selectedDepartmentTask:null,loading:!1,error:null,total:0}),getters:{getDepartmentTaskById:e=>t=>e.departmentTasks.find(s=>s.id===t)},actions:{async fetchDepartmentTasks(e){var t,s;this.loading=!0,this.error=null;try{const o=await Bs.getAll(e);this.departmentTasks=o.data||[],this.total=o.total||0}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch departmentTasks",console.error("Error fetching departmentTasks:",o)}finally{this.loading=!1}},async fetchDepartmentTaskById(e){var t,s;this.loading=!0,this.error=null;try{this.selectedDepartmentTask=await Bs.getById(e)}catch(o){this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to fetch departmentTask",console.error(`Error fetching departmentTask with ID ${e}:`,o)}finally{this.loading=!1}},async createDepartmentTask(e){var t,s;this.loading=!0,this.error=null;try{const o=await Bs.create(e);return this.departmentTasks.push(o),o}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to create departmentTask",console.error("Error creating departmentTask:",o),o}finally{this.loading=!1}},async updateDepartmentTask(e,t){var s,o;this.loading=!0,this.error=null;try{const n=await Bs.update(e,t),r=this.departmentTasks.findIndex(l=>l.id===e);return r!==-1&&(this.departmentTasks[r]=n),n}catch(n){throw this.error=((o=(s=n.response)==null?void 0:s.data)==null?void 0:o.message)||"Failed to update departmentTask",console.error(`Error updating departmentTask with ID ${e}:`,n),n}finally{this.loading=!1}},async deleteDepartmentTask(e){var t,s;this.loading=!0,this.error=null;try{await Bs.delete(e),this.departmentTasks=this.departmentTasks.filter(o=>o.id!==e)}catch(o){throw this.error=((s=(t=o.response)==null?void 0:t.data)==null?void 0:s.message)||"Failed to delete departmentTask",console.error(`Error deleting departmentTask with ID ${e}:`,o),o}finally{this.loading=!1}}}}),tx={class:"p-4 md:p-6 lg:p-8"},sx={class:"bg-white rounded-lg shadow p-6 mb-6"},ox={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},nx={for:"department-select",class:"block text-sm font-medium text-gray-700 mb-2"},rx=["value"],ix={for:"project-select",class:"block text-sm font-medium text-gray-700 mb-2"},lx=["disabled"],ax=["value"],cx={key:0,class:"bg-white rounded-lg shadow p-6 mb-6"},ux={class:"text-sm text-gray-700"},dx={class:"text-sm text-gray-700"},fx={class:"text-sm text-gray-700"},px={class:"text-sm text-gray-700 capitalize"},hx={key:1,class:"bg-white rounded-lg shadow p-6"},mx={class:"space-y-6"},gx={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},wx=["for"],bx=["id","value","required","disabled"],vx=["id","value","required","disabled"],yx=["id","value","required","disabled"],xx=["id","required","disabled"],kx=["value","selected"],_x={key:4,class:"space-y-2"},Cx=["id","value","checked","disabled"],$x=["for"],Ex=["id","value","required","disabled"],Sx=["id","value","required","disabled"],Ax=["id","value","required","disabled"],Px={class:"flex justify-end space-x-3 pt-6 border-t border-gray-200"},Tx={key:2,class:"bg-white rounded-lg shadow p-6"},jx=Pe({__name:"Tasks",setup(e){const t=td(),s=Kn(),o=ex(),n=W(""),r=W(null),l=W(!1),a=W(!1),f=W(!1),u=Y(()=>t.departments),c=Y(()=>s.projects),d=Y(()=>o.departmentTasks[0]),p=()=>{r.value=null},h=async()=>{l.value=!0;try{await t.fetchDepartments()}catch(x){console.error("Error loading departments:",x)}finally{l.value=!1}},v=async()=>{a.value=!0;try{await s.fetchProjects()}catch(x){console.error("Error loading projects:",x)}finally{a.value=!1}},$=async(x,m)=>{f.value=!0;try{await o.fetchDepartmentTasks({department:x,project:m._id})}catch(b){console.error("Error loading department tasks:",b)}finally{f.value=!1}},y=x=>{var m,b;return(b=(m=d.value)==null?void 0:m.editable)==null?void 0:b.includes(x._id)};return Ve(async()=>{h(),v()}),(x,m)=>(C(),S("div",tx,[m[16]||(m[16]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Tasks Management",-1)),i("div",sx,[m[7]||(m[7]=i("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Select Department and Project",-1)),i("div",ox,[i("div",null,[i("label",nx,R(l.value?"Loading departments...":"Choose Department"),1),oe(i("select",{id:"department-select","onUpdate:modelValue":m[0]||(m[0]=b=>n.value=b),onChange:m[1]||(m[1]=b=>p()),class:"w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"},[m[5]||(m[5]=i("option",{value:""},"Select a department...",-1)),(C(!0),S(ie,null,xe(u.value,b=>(C(),S("option",{key:b._id,value:b._id},R(b.name)+" ("+R(b.scope)+") ",9,rx))),128))],544),[[Ne,n.value]])]),i("div",null,[i("label",ix,R(a.value?"Loading projects...":"Choose Project"),1),oe(i("select",{onChange:m[2]||(m[2]=b=>$(n.value,r.value)),id:"project-select","onUpdate:modelValue":m[3]||(m[3]=b=>r.value=b),disabled:!n.value,class:"capitalize w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 disabled:bg-gray-100 disabled:cursor-not-allowed"},[m[6]||(m[6]=i("option",{value:"null"},"Select a project...",-1)),(C(!0),S(ie,null,xe(c.value,b=>(C(),S("option",{key:b._id,value:b},R(b.name)+" ("+R(b.stage)+") ",9,ax))),128))],40,lx),[[Ne,r.value]])])])]),r.value?(C(),S("div",cx,[i("div",ux,[m[8]||(m[8]=i("span",{class:"font-medium text-gray-800"},"Customer:",-1)),m[9]||(m[9]=ce()),ge(Lo,{customerId:r.value.customer},null,8,["customerId"])]),i("div",dx,[m[10]||(m[10]=i("span",{class:"font-medium text-gray-800"},"Salesperson:",-1)),ce(" "+R(r.value.salesperson),1)]),i("div",fx,[m[11]||(m[11]=i("span",{class:"font-medium text-gray-800"},"Description:",-1)),ce(" "+R(r.value.description),1)]),i("div",px,[m[12]||(m[12]=i("span",{class:"font-medium text-gray-800"},"Stage:",-1)),ce(" "+R(r.value.stage),1)])])):de("",!0),r.value&&d.value?(C(),S("div",hx,[m[14]||(m[14]=i("h2",{class:"text-lg font-medium text-gray-900 mb-4"},"Project Tasks",-1)),i("form",mx,[i("div",gx,[(C(!0),S(ie,null,xe(d.value.populatedTasks,b=>(C(),S("div",{key:b.name,class:ne({"md:col-span-2":b.type==="textarea"})},[i("label",{for:b.name,class:"block text-sm font-medium text-gray-700 mb-2"},R(b.name),9,wx),b.type==="string"?(C(),S("input",{key:0,id:b.name,type:"text",value:b.value,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,bx)):b.type==="number"?(C(),S("input",{key:1,id:b.name,type:"number",value:b.value,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,vx)):b.type==="date"?(C(),S("input",{key:2,id:b.name,type:"date",value:b.value,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,yx)):b.type==="select"?(C(),S("select",{key:3,id:b.name,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},[(C(!0),S(ie,null,xe(b.options,T=>(C(),S("option",{key:T,value:T,selected:b.value===T},R(T),9,kx))),128))],10,xx)):b.type==="checkbox"?(C(),S("div",_x,[(C(!0),S(ie,null,xe(b.options,T=>(C(),S("div",{key:T,class:"flex items-center"},[i("input",{id:`${b.name}-${T}`,type:"checkbox",value:T,checked:Array.isArray(b.value)&&b.value.includes(T),class:ne(["h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,Cx),i("label",{for:`${b.name}-${T}`,class:"ml-2 text-sm text-gray-700"},R(T),9,$x)]))),128))])):b.type==="textarea"?(C(),S("textarea",{key:5,id:b.name,rows:"3",value:b.value,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,Ex)):b.type==="jsonObject"?(C(!0),S(ie,{key:6},xe(b.value,(T,L)=>(C(),S("div",{key:L,class:"space-y-2 mb-1"},[i("input",{id:`${b.name}-${L}`,type:"text",value:T,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,Sx)]))),128)):b.type==="jsonDate"?(C(!0),S(ie,{key:7},xe(b.value,(T,L)=>(C(),S("div",{key:L,class:"space-y-2 mb-1"},[i("input",{id:`${b.name}-${L}`,type:"date",value:T,required:b.required,class:ne(["w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500",{"bg-gray-100":y(b)}]),disabled:y(b)},null,10,Ax)]))),128)):de("",!0)],2))),128))]),i("div",Px,[i("button",{onClick:m[4]||(m[4]=b=>p()),type:"button",class:"px-4 py-2 border border-gray-300 rounded-md text-sm font-medium text-gray-700 hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),m[13]||(m[13]=i("button",{type:"submit",class:"px-4 py-2 bg-blue-600 border border-transparent rounded-md text-sm font-medium text-white hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Save Project Tasks ",-1))])])])):r.value&&!d.value?(C(),S("div",Tx,m[15]||(m[15]=[ws('<div class="text-center py-8"><svg class="mx-auto h-12 w-12 text-gray-400" fill="none" viewBox="0 0 24 24" stroke="currentColor"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path></svg><h3 class="mt-2 text-sm font-medium text-gray-900">No tasks available</h3><p class="mt-1 text-sm text-gray-500">This department doesn&#39;t have task templates configured yet.</p></div>',1)]))):de("",!0)]))}}),Rx="/assets/avatar-88VTbnSV.jpg",Mx={class:"p-4 md:p-6 lg:p-8"},Bx={class:"bg-white rounded-lg shadow overflow-hidden"},Ox={class:"grid grid-cols-1 md:grid-cols-3"},Ix={class:"border-r border-gray-200"},Dx={class:"divide-y divide-gray-200 overflow-y-auto max-h-[calc(100vh-16rem)]"},Lx=["onClick"],Fx={class:"flex items-center space-x-4"},Ux={class:"flex-1 min-w-0"},Nx={class:"text-sm font-medium text-gray-900 truncate"},Vx={class:"text-sm text-gray-600 truncate"},Hx={class:"text-xs text-gray-400"},zx={class:"flex-shrink-0 flex flex-col items-end"},qx={key:0,class:"inline-flex items-center justify-center h-5 w-5 rounded-full bg-blue-600 text-xs font-medium text-white"},Kx={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},Wx={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},Jx={class:"flex w-full justify-between sm:hidden"},Gx=["disabled"],Yx={class:"text-sm text-gray-700"},Zx=["disabled"],Xx={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},Qx={class:"text-sm text-gray-700"},e5={class:"font-medium"},t5={class:"font-medium"},s5={class:"font-medium"},o5={key:0},n5={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},r5=["disabled"],i5={key:0,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},l5=["onClick"],a5=["disabled"],c5={key:0,class:"col-span-2"},u5={class:"p-4 shadow"},d5={class:"text-lg font-medium text-gray-900"},f5={class:"p-4"},p5={class:"bg-gray-50 p-4 rounded text-gray-800 text-md mt-2 whitespace-normal"},h5={class:"text-xs text-gray-400 mt-1"},m5={key:1,class:"p-4 text-gray-400 text-sm"},g5=Pe({__name:"Messages",setup(e){const t=ru(),s=Y(()=>t.notifications),o=W(null),n=W(10),r=W(0),l=Y(()=>t.total),a=async()=>{await t.fetchMyNotifications({$skip:r.value,$limit:n.value})},f=y=>{o.value=y,y.isRead||u(y._id)},u=async y=>{await t.markAsRead(y),await a()},c=y=>{typeof y=="number"&&(r.value=(y-1)*n.value,a())},d=()=>{r.value+n.value<l.value&&(r.value+=n.value,a())},p=()=>{r.value-n.value>=0&&(r.value-=n.value,a())},h=Y(()=>Math.floor(r.value/n.value)+1),v=Y(()=>Math.ceil(l.value/n.value)),$=Y(()=>{const y=[];if(v.value<=5)for(let m=1;m<=v.value;m++)y.push(m);else{y.push(1);let m=Math.max(2,h.value-1),b=Math.min(v.value-1,m+5-3);m=Math.max(2,b-2),m>2&&y.push("...");for(let T=m;T<=b;T++)y.push(T);b<v.value-1&&y.push("..."),y.push(v.value)}return y});return Ve(async()=>{a()}),(y,x)=>(C(),S("div",Mx,[x[8]||(x[8]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Messages",-1)),i("div",Bx,[i("div",Ox,[i("div",Ix,[x[7]||(x[7]=ws('<div class="p-4 border-b border-gray-200"><div class="relative"><div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"><svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-gray-400" viewBox="0 0 20 20" fill="currentColor"><path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd"></path></svg></div><input type="text" class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm" placeholder="Search contacts..."></div></div>',1)),i("ul",Dx,[(C(!0),S(ie,null,xe(s.value,m=>(C(),S("li",{key:m._id,class:ne(["p-4 hover:bg-blue-50 cursor-pointer",{"bg-blue-50":o.value===m}]),onClick:b=>f(m)},[i("div",Fx,[x[0]||(x[0]=i("div",{class:"flex-shrink-0 hidden xl:block"},[i("img",{class:"h-10 w-10 rounded-full",src:Rx,alt:"User avatar"})],-1)),i("div",Ux,[i("p",Nx,[ge(fn,{id:m.sender},null,8,["id"])]),i("p",Vx,R(m.message),1),i("p",Hx,R(m.createdAt?new Date(m.createdAt).toLocaleString():"No date"),1)]),i("div",zx,[m.isRead?de("",!0):(C(),S("span",qx,"•"))])])],10,Lx))),128))]),i("div",Kx,[i("div",Wx,[i("div",Jx,[i("button",{onClick:p,disabled:r.value===0,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",r.value===0?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Previous ",10,Gx),i("span",Yx," Page "+R(h.value)+" of "+R(v.value),1),i("button",{onClick:d,disabled:r.value+n.value>=l.value,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",r.value+n.value>=l.value?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Next ",10,Zx)]),i("div",Xx,[i("div",null,[i("p",Qx,[x[1]||(x[1]=ce(" Showing ")),i("span",e5,R(s.value.length>0?r.value+1:0),1),x[2]||(x[2]=ce(" to ")),i("span",t5,R(Math.min(r.value+s.value.length,l.value)),1),x[3]||(x[3]=ce(" of ")),i("span",s5,R(l.value),1),x[4]||(x[4]=ce(" Notifications "))])]),l.value>0?(C(),S("div",o5,[i("nav",n5,[i("button",{onClick:p,disabled:r.value===0,class:ne(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium",r.value===0?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},x[5]||(x[5]=[i("span",{class:"sr-only"},"Previous",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,r5),(C(!0),S(ie,null,xe($.value,(m,b)=>(C(),S(ie,{key:b},[m==="..."?(C(),S("span",i5," ... ")):(C(),S("button",{key:1,onClick:T=>c(m),class:ne(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",h.value===m?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},R(m),11,l5))],64))),128)),i("button",{onClick:d,disabled:r.value+n.value>=l.value,class:ne(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium",r.value+n.value>=l.value?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},x[6]||(x[6]=[i("span",{class:"sr-only"},"Next",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,a5)])])):de("",!0)])])])]),o.value?(C(),S("div",c5,[i("div",u5,[i("h2",d5,[ge(fn,{id:o.value.sender},null,8,["id"])])]),i("div",f5,[i("p",p5,R(o.value.message),1),i("p",h5,"Sent on: "+R(o.value.createdAt?new Date(o.value.createdAt).toLocaleString():"No date"),1)])])):(C(),S("div",m5,"Select a message to view its content."))])])]))}}),w5={key:0,class:"fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"},b5={class:"bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto"},v5={class:"p-6"},y5={class:"flex justify-between items-center mb-4"},x5={class:"text-xl font-semibold text-gray-900"},k5={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},_5={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},C5=["value"],$5=["value"],E5={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},S5={for:"password",class:"block text-sm font-medium text-gray-700"},A5=["required"],P5=["required"],T5={class:"flex justify-end space-x-3 pt-4"},j5={type:"submit",class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},R5=Pe({__name:"UserForm",props:{show:{type:Boolean,required:!0},user:{type:Object,default:()=>null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(e,{emit:t}){const s=e,o=t,n=W({firstName:"",lastName:"",email:"",phone:"",role:"user",status:"active",password:"",confirmPassword:"",department:"",avatar:""}),r=[{id:"admin",name:"Administrator"},{id:"manager",name:"Manager"},{id:"user",name:"User"}],l=[{id:"it",name:"IT"},{id:"hr",name:"Human Resources"},{id:"sales",name:"Sales"},{id:"marketing",name:"Marketing"},{id:"finance",name:"Finance"},{id:"operations",name:"Operations"}];Ve(()=>{s.user&&(n.value={...n.value,firstName:s.user.firstName||"",lastName:s.user.lastName||"",email:s.user.email||"",phone:s.user.phone||"",role:s.user.role||"user",status:s.user.status||"active",department:s.user.department||"",avatar:s.user.avatar||"",password:"",confirmPassword:""})});const a=()=>{n.value={firstName:"",lastName:"",email:"",phone:"",role:"user",status:"active",password:"",confirmPassword:"",department:"",avatar:""}},f=()=>{a(),o("close")},u=()=>{if(n.value.password!==n.value.confirmPassword){alert("Passwords do not match");return}const c={...n.value};delete c.confirmPassword,s.isEdit&&!c.password&&delete c.password,o("save",c),f()};return(c,d)=>e.show?(C(),S("div",w5,[i("div",b5,[i("div",v5,[i("div",y5,[i("h2",x5,R(e.isEdit?"Edit User":"Add New User"),1),i("button",{onClick:f,class:"text-gray-400 hover:text-gray-500"},d[10]||(d[10]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("form",{onSubmit:zt(u,["prevent"]),class:"space-y-4"},[i("div",k5,[i("div",null,[d[11]||(d[11]=i("label",{for:"firstName",class:"block text-sm font-medium text-gray-700"},"First Name",-1)),oe(i("input",{type:"text",id:"firstName","onUpdate:modelValue":d[0]||(d[0]=p=>n.value.firstName=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.firstName]])]),i("div",null,[d[12]||(d[12]=i("label",{for:"lastName",class:"block text-sm font-medium text-gray-700"},"Last Name",-1)),oe(i("input",{type:"text",id:"lastName","onUpdate:modelValue":d[1]||(d[1]=p=>n.value.lastName=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.lastName]])])]),i("div",null,[d[13]||(d[13]=i("label",{for:"email",class:"block text-sm font-medium text-gray-700"},"Email",-1)),oe(i("input",{type:"email",id:"email","onUpdate:modelValue":d[2]||(d[2]=p=>n.value.email=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.email]])]),i("div",null,[d[14]||(d[14]=i("label",{for:"phone",class:"block text-sm font-medium text-gray-700"},"Phone",-1)),oe(i("input",{type:"tel",id:"phone","onUpdate:modelValue":d[3]||(d[3]=p=>n.value.phone=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.phone]])]),i("div",_5,[i("div",null,[d[15]||(d[15]=i("label",{for:"role",class:"block text-sm font-medium text-gray-700"},"Role",-1)),oe(i("select",{id:"role","onUpdate:modelValue":d[4]||(d[4]=p=>n.value.role=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},[(C(),S(ie,null,xe(r,p=>i("option",{key:p.id,value:p.id},R(p.name),9,C5)),64))],512),[[Ne,n.value.role]])]),i("div",null,[d[17]||(d[17]=i("label",{for:"department",class:"block text-sm font-medium text-gray-700"},"Department",-1)),oe(i("select",{id:"department","onUpdate:modelValue":d[5]||(d[5]=p=>n.value.department=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},[d[16]||(d[16]=i("option",{value:""},"Select Department",-1)),(C(),S(ie,null,xe(l,p=>i("option",{key:p.id,value:p.id},R(p.name),9,$5)),64))],512),[[Ne,n.value.department]])])]),i("div",null,[d[19]||(d[19]=i("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Status",-1)),oe(i("select",{id:"status","onUpdate:modelValue":d[6]||(d[6]=p=>n.value.status=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},d[18]||(d[18]=[i("option",{value:"active"},"Active",-1),i("option",{value:"inactive"},"Inactive",-1)]),512),[[Ne,n.value.status]])]),i("div",null,[d[20]||(d[20]=i("label",{for:"avatar",class:"block text-sm font-medium text-gray-700"},"Avatar URL",-1)),oe(i("input",{type:"text",id:"avatar","onUpdate:modelValue":d[7]||(d[7]=p=>n.value.avatar=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"https://example.com/avatar.jpg"},null,512),[[ye,n.value.avatar]])]),i("div",E5,[i("div",null,[i("label",S5,R(e.isEdit?"New Password (leave blank to keep current)":"Password"),1),oe(i("input",{type:"password",id:"password","onUpdate:modelValue":d[8]||(d[8]=p=>n.value.password=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:!e.isEdit},null,8,A5),[[ye,n.value.password]])]),i("div",null,[d[21]||(d[21]=i("label",{for:"confirmPassword",class:"block text-sm font-medium text-gray-700"},"Confirm Password",-1)),oe(i("input",{type:"password",id:"confirmPassword","onUpdate:modelValue":d[9]||(d[9]=p=>n.value.confirmPassword=p),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:!e.isEdit},null,8,P5),[[ye,n.value.confirmPassword]])])]),i("div",T5,[i("button",{type:"button",onClick:f,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),i("button",j5,R(e.isEdit?"Update User":"Create User"),1)])],32)])])])):de("",!0)}}),M5={class:"p-4 md:p-6 lg:p-8"},B5={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-6"},O5={class:"flex flex-col md:flex-row gap-4"},I5={class:"flex-grow"},D5={class:"relative"},L5={class:"bg-white shadow rounded-lg overflow-hidden"},F5={class:"overflow-x-auto"},U5={class:"min-w-full divide-y divide-gray-200"},N5={class:"bg-white divide-y divide-gray-200"},V5={class:"px-6 py-4 whitespace-nowrap"},H5={class:"flex items-center"},z5={class:"flex-shrink-0 h-10 w-10"},q5=["src","alt"],K5={key:1,class:"h-10 w-10 rounded-full bg-blue-100 flex items-center justify-center text-blue-600 font-semibold"},W5={class:"ml-4"},J5={class:"text-sm font-medium text-gray-900"},G5={class:"text-sm text-gray-500"},Y5={class:"px-6 py-4 flex items-center gap-1"},Z5={class:"px-6 py-4 whitespace-nowrap"},X5={class:"px-6 py-4 whitespace-nowrap text-right text-sm font-medium"},Q5=["onClick"],e4=["onClick"],t4={key:0},s4={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},o4={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},n4={class:"flex w-full justify-between sm:hidden"},r4=["disabled"],i4={class:"text-sm text-gray-700"},l4=["disabled"],a4={class:"hidden sm:flex-1 sm:flex sm:items-center sm:justify-between"},c4={class:"text-sm text-gray-700"},u4={class:"font-medium"},d4={class:"font-medium"},f4={class:"font-medium"},p4={key:0},h4={class:"relative z-0 inline-flex rounded-md shadow-sm -space-x-px","aria-label":"Pagination"},m4=["disabled"],g4={key:0,class:"relative inline-flex items-center px-4 py-2 border border-gray-300 bg-white text-sm font-medium text-gray-700"},w4=["onClick"],b4=["disabled"],v4=Pe({__name:"Users",setup(e){const t=Wn(),s=W(!1),o=W(!1),n=W(null),r=W(""),l=W(10),a=W(0),f=Y(()=>t.total);Ve(async()=>{await u()});const u=async()=>{await t.fetchUsers({$skip:a.value,$limit:l.value,$keywords:r.value||void 0})};De(r,()=>{a.value=0,u()});const c=Y(()=>t.users),d=()=>{n.value=null,o.value=!1,s.value=!0},p=j=>{n.value={...j},o.value=!0,s.value=!0},h=()=>{s.value=!1,n.value=null},v=async j=>{try{o.value&&n.value?await t.updateUser(n.value.id||n.value._id,j):await t.createUser(j),h(),await u()}catch(_){console.error("Error saving user:",_)}},$=async j=>{if(confirm("Are you sure you want to delete this user?"))try{await t.deleteUser(j),await u()}catch(_){console.error("Error deleting user:",_)}},y=j=>{switch(j){case"dev":return"bg-red-100 text-red-800";case"admin":return"bg-yellow-100 text-yellow-800";case"user":return"bg-green-100 text-green-800";case"sales":return"bg-blue-100 text-blue-800";case"pm":return"bg-yellow-100 text-yellow-800";case"fin":return"bg-indigo-100 text-indigo-800";case"tech":return"bg-violet-100 text-violet-800";case"pur":return"bg-rose-100 text-rose-800";case"overview":return"bg-lime-100 text-lime-800";default:return"bg-gray-100 text-gray-800"}},x=j=>{typeof j=="number"&&(a.value=(j-1)*l.value,u())},m=()=>{a.value+l.value<f.value&&(a.value+=l.value,u())},b=()=>{a.value-l.value>=0&&(a.value-=l.value,u())},T=Y(()=>Math.floor(a.value/l.value)+1),L=Y(()=>Math.ceil(f.value/l.value)),J=Y(()=>{const j=[];if(L.value<=5)for(let E=1;E<=L.value;E++)j.push(E);else{j.push(1);let E=Math.max(2,T.value-1),q=Math.min(L.value-1,E+5-3);E=Math.max(2,q-2),E>2&&j.push("...");for(let we=E;we<=q;we++)j.push(we);q<L.value-1&&j.push("..."),j.push(L.value)}return j});return(j,_)=>(C(),S(ie,null,[i("div",M5,[_[12]||(_[12]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Users",-1)),i("div",B5,[i("div",O5,[i("div",I5,[_[2]||(_[2]=i("label",{for:"search",class:"sr-only"},"Search users",-1)),i("div",D5,[_[1]||(_[1]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"text",id:"search","onUpdate:modelValue":_[0]||(_[0]=E=>r.value=E),class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search users..."},null,512),[[ye,r.value]])])]),i("div",{class:"flex flex-col sm:flex-row gap-4"},[i("button",{onClick:d,class:"whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},_[3]||(_[3]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" Add User ")]))])])]),i("div",L5,[i("div",F5,[i("table",U5,[_[5]||(_[5]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," User "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Role "),i("th",{scope:"col",class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," Status "),i("th",{scope:"col",class:"relative px-6 py-3"},[i("span",{class:"sr-only"},"Actions")])])],-1)),i("tbody",N5,[(C(!0),S(ie,null,xe(c.value,E=>(C(),S("tr",{key:E.id||E._id,class:"hover:bg-gray-50"},[i("td",V5,[i("div",H5,[i("div",z5,[E.avatar?(C(),S("img",{key:0,class:"h-10 w-10 rounded-full object-cover",src:E.avatar,alt:`${E.name}`},null,8,q5)):(C(),S("div",K5,R(E.name.charAt(0)),1))]),i("div",W5,[i("div",J5,R(E.name),1),i("div",G5,R(E.email),1)])])]),i("td",Y5,[(C(!0),S(ie,null,xe(E.scopes||[E.role],q=>(C(),S("div",{class:ne(["text-sm capitalize rounded px-1 py-0.5",y(q)])},R(q),3))),256))]),i("td",Z5,[i("span",{class:ne(["px-2 inline-flex text-xs leading-5 font-semibold rounded-full",E.status?"bg-green-100 text-green-800":"bg-gray-100 text-gray-800"])},R(E.status?"Active":"Inactive"),3)]),i("td",X5,[i("button",{onClick:q=>p(E),class:"text-blue-600 hover:text-blue-900 mr-3"}," Edit ",8,Q5),i("button",{onClick:q=>$(E.id||E._id),class:"text-red-600 hover:text-red-900"}," Delete ",8,e4)])]))),128)),c.value.length===0?(C(),S("tr",t4,_[4]||(_[4]=[i("td",{colspan:"7",class:"px-6 py-4 text-center text-sm text-gray-500"}," No users found matching your search criteria. ",-1)]))):de("",!0)])])]),i("div",s4,[i("div",o4,[i("div",n4,[i("button",{onClick:b,disabled:a.value===0,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",a.value===0?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Previous ",10,r4),i("span",i4," Page "+R(T.value)+" of "+R(L.value),1),i("button",{onClick:m,disabled:a.value+l.value>=f.value,class:ne(["relative inline-flex items-center px-3 py-1.5 border border-gray-300 text-sm font-medium rounded-md text-gray-700 bg-white",a.value+l.value>=f.value?"opacity-50 cursor-not-allowed":"hover:bg-gray-50"])}," Next ",10,l4)]),i("div",a4,[i("div",null,[i("p",c4,[_[6]||(_[6]=ce(" Showing ")),i("span",u4,R(c.value.length>0?a.value+1:0),1),_[7]||(_[7]=ce(" to ")),i("span",d4,R(Math.min(a.value+c.value.length,f.value)),1),_[8]||(_[8]=ce(" of ")),i("span",f4,R(f.value),1),_[9]||(_[9]=ce(" users "))])]),f.value>0?(C(),S("div",p4,[i("nav",h4,[i("button",{onClick:b,disabled:a.value===0,class:ne(["relative inline-flex items-center px-2 py-2 rounded-l-md border border-gray-300 bg-white text-sm font-medium",a.value===0?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},_[10]||(_[10]=[i("span",{class:"sr-only"},"Previous",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M12.707 5.293a1 1 0 010 1.414L9.414 10l3.293 3.293a1 1 0 01-1.414 1.414l-4-4a1 1 0 010-1.414l4-4a1 1 0 011.414 0z","clip-rule":"evenodd"})],-1)]),10,m4),(C(!0),S(ie,null,xe(J.value,(E,q)=>(C(),S(ie,{key:q},[E==="..."?(C(),S("span",g4," ... ")):(C(),S("button",{key:1,onClick:we=>x(E),class:ne(["relative inline-flex items-center px-4 py-2 border text-sm font-medium",T.value===E?"z-10 bg-blue-50 border-blue-500 text-blue-600":"bg-white border-gray-300 text-gray-500 hover:bg-gray-50"])},R(E),11,w4))],64))),128)),i("button",{onClick:m,disabled:a.value+l.value>=f.value,class:ne(["relative inline-flex items-center px-2 py-2 rounded-r-md border border-gray-300 bg-white text-sm font-medium",a.value+l.value>=f.value?"text-gray-300 cursor-not-allowed":"text-gray-500 hover:bg-gray-50"])},_[11]||(_[11]=[i("span",{class:"sr-only"},"Next",-1),i("svg",{class:"h-5 w-5",xmlns:"http://www.w3.org/2000/svg",viewBox:"0 0 20 20",fill:"currentColor","aria-hidden":"true"},[i("path",{"fill-rule":"evenodd",d:"M7.293 14.707a1 1 0 010-1.414L10.586 10 7.293 6.707a1 1 0 011.414-1.414l4 4a1 1 0 010 1.414l-4 4a1 1 0 01-1.414 0z","clip-rule":"evenodd"})],-1)]),10,b4)])])):de("",!0)])])])])]),ge(R5,{show:s.value,user:n.value,isEdit:o.value,onClose:h,onSave:v},null,8,["show","user","isEdit"])],64))}}),y4={key:0,class:"fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50"},x4={class:"bg-white rounded-lg shadow-xl max-w-lg w-full mx-4 max-h-[90vh] overflow-y-auto"},k4={class:"p-6"},_4={class:"flex justify-between items-center mb-4"},C4={class:"text-xl font-semibold text-gray-900"},$4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},E4={class:"mt-4"},S4={class:"mt-4"},A4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},P4={class:"mt-4"},T4={class:"mt-1 relative rounded-md shadow-sm"},j4={class:"grid grid-cols-1 md:grid-cols-2 gap-4"},R4={class:"mt-4"},M4={class:"flex justify-end space-x-3 pt-4"},B4={type:"submit",class:"px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},O4=Pe({__name:"DepartmentForm",props:{show:{type:Boolean,required:!0},department:{type:Object,default:null},isEdit:{type:Boolean,default:!1}},emits:["close","save"],setup(e,{emit:t}){const s=e,o=t,n=W({name:"",code:"",description:"",manager:"",parentDepartment:"",status:"active",budget:"",location:"",email:"",phone:""});De(()=>s.department,u=>{u&&r(u)},{immediate:!0}),De(()=>s.show,u=>{u&&s.department?r(s.department):!u&&!s.isEdit&&l()});const r=u=>{u&&(n.value={name:u.name||"",code:u.code||"",description:u.description||"",manager:u.manager||"",parentDepartment:u.parentDepartment||"",status:u.status||"active",budget:u.budget||"",location:u.location||"",email:u.email||"",phone:u.phone||""})};Ve(()=>{s.department&&r(s.department)});const l=()=>{n.value={name:"",code:"",description:"",manager:"",parentDepartment:"",status:"active",budget:"",location:"",email:"",phone:""}},a=()=>{s.isEdit||l(),o("close")},f=()=>{if(!n.value.name){alert("Department name is required");return}if(!n.value.code){alert("Department code is required");return}const u={...n.value};o("save",u),a()};return(u,c)=>e.show?(C(),S("div",y4,[i("div",x4,[i("div",k4,[i("div",_4,[i("h2",C4,R(e.isEdit?"Edit Department":"Add New Department"),1),i("button",{onClick:a,class:"text-gray-400 hover:text-gray-500"},c[10]||(c[10]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("form",{onSubmit:zt(f,["prevent"]),class:"space-y-4"},[i("div",null,[c[16]||(c[16]=i("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Basic Information",-1)),i("div",$4,[i("div",null,[c[11]||(c[11]=i("label",{for:"name",class:"block text-sm font-medium text-gray-700"},"Department Name",-1)),oe(i("input",{type:"text",id:"name","onUpdate:modelValue":c[0]||(c[0]=d=>n.value.name=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.name]])]),i("div",null,[c[12]||(c[12]=i("label",{for:"code",class:"block text-sm font-medium text-gray-700"},"Department Code",-1)),oe(i("input",{type:"text",id:"code","onUpdate:modelValue":c[1]||(c[1]=d=>n.value.code=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",required:""},null,512),[[ye,n.value.code]])])]),i("div",E4,[c[13]||(c[13]=i("label",{for:"description",class:"block text-sm font-medium text-gray-700"},"Description",-1)),oe(i("textarea",{id:"description","onUpdate:modelValue":c[2]||(c[2]=d=>n.value.description=d),rows:"2",class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.description]])]),i("div",S4,[c[15]||(c[15]=i("label",{for:"status",class:"block text-sm font-medium text-gray-700"},"Status",-1)),oe(i("select",{id:"status","onUpdate:modelValue":c[3]||(c[3]=d=>n.value.status=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},c[14]||(c[14]=[i("option",{value:"active"},"Active",-1),i("option",{value:"inactive"},"Inactive",-1)]),512),[[Ne,n.value.status]])])]),i("div",null,[c[21]||(c[21]=i("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Organization",-1)),i("div",A4,[i("div",null,[c[17]||(c[17]=i("label",{for:"manager",class:"block text-sm font-medium text-gray-700"},"Department Manager",-1)),oe(i("input",{type:"text",id:"manager","onUpdate:modelValue":c[4]||(c[4]=d=>n.value.manager=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.manager]])]),i("div",null,[c[18]||(c[18]=i("label",{for:"parentDepartment",class:"block text-sm font-medium text-gray-700"},"Parent Department",-1)),oe(i("input",{type:"text",id:"parentDepartment","onUpdate:modelValue":c[5]||(c[5]=d=>n.value.parentDepartment=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.parentDepartment]])])]),i("div",P4,[c[20]||(c[20]=i("label",{for:"budget",class:"block text-sm font-medium text-gray-700"},"Annual Budget",-1)),i("div",T4,[c[19]||(c[19]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("span",{class:"text-gray-500 sm:text-sm"},"$")],-1)),oe(i("input",{type:"number",id:"budget","onUpdate:modelValue":c[6]||(c[6]=d=>n.value.budget=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 pl-7 pr-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"0.00",min:"0",step:"0.01"},null,512),[[ye,n.value.budget]])])])]),i("div",null,[c[25]||(c[25]=i("h3",{class:"text-lg font-medium text-gray-900 mb-4"},"Contact Information",-1)),i("div",j4,[i("div",null,[c[22]||(c[22]=i("label",{for:"location",class:"block text-sm font-medium text-gray-700"},"Location",-1)),oe(i("input",{type:"text",id:"location","onUpdate:modelValue":c[7]||(c[7]=d=>n.value.location=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.location]])]),i("div",null,[c[23]||(c[23]=i("label",{for:"email",class:"block text-sm font-medium text-gray-700"},"Email",-1)),oe(i("input",{type:"email",id:"email","onUpdate:modelValue":c[8]||(c[8]=d=>n.value.email=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.email]])])]),i("div",R4,[c[24]||(c[24]=i("label",{for:"phone",class:"block text-sm font-medium text-gray-700"},"Phone",-1)),oe(i("input",{type:"tel",id:"phone","onUpdate:modelValue":c[9]||(c[9]=d=>n.value.phone=d),class:"mt-1 block w-full border border-gray-300 rounded-md shadow-sm py-2 px-3 focus:outline-none focus:ring-blue-500 focus:border-blue-500 sm:text-sm"},null,512),[[ye,n.value.phone]])])]),i("div",M4,[i("button",{type:"button",onClick:a,class:"px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"}," Cancel "),i("button",B4,R(e.isEdit?"Update Department":"Save Department"),1)])],32)])])])):de("",!0)}}),I4={class:"p-4 md:p-6 lg:p-8"},D4={class:"bg-white rounded-lg shadow p-4 md:p-6 mb-6"},L4={class:"flex flex-col md:flex-row gap-4"},F4={class:"flex-grow"},U4={class:"relative"},N4={key:0,class:"flex justify-center items-center py-12"},V4={key:1,class:"bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative my-6"},H4={class:"block sm:inline"},z4={key:2,class:"bg-white rounded-lg shadow p-6 text-center my-6"},q4={key:3,class:"bg-white rounded-lg shadow p-6 text-center my-6"},K4={key:4,class:"bg-white shadow rounded-lg overflow-hidden"},W4={class:"overflow-x-auto w-full"},J4={class:"min-w-full divide-y divide-gray-200 table-fixed md:table-auto"},G4={class:"bg-white divide-y divide-gray-200"},Y4={class:"px-4 sm:px-6 py-4"},Z4={class:"flex flex-col"},X4={class:"text-sm font-medium text-gray-900 truncate"},Q4={class:"px-4 sm:px-6 py-4 text-xs sm:text-sm text-gray-500"},e3={class:"px-4 sm:px-6 py-4 text-xs sm:text-sm text-gray-500"},t3={class:"px-4 sm:px-6 py-4 text-right text-xs sm:text-sm font-medium"},s3={class:"flex justify-end space-x-1 sm:space-x-2"},o3=["onClick"],n3=["onClick","title"],r3={key:0,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor"},i3={key:1,xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor"},l3=["onClick"],a3={class:"bg-white px-4 py-3 border-t border-gray-200 sm:px-6"},c3={class:"flex flex-col sm:flex-row items-center justify-between gap-4"},u3={class:"w-full sm:flex-1 sm:flex sm:items-center sm:justify-between"},d3={class:"text-sm text-gray-700"},f3={class:"font-medium"},p3=Pe({__name:"Departments",setup(e){const t=td(),s=W(!1),o=W(!1),n=W(null),r=W(""),l=W(""),a=Y(()=>t.loading),f=Y(()=>t.error);Ve(async()=>{try{await t.fetchDepartments()}catch(x){console.error("Error loading departments:",x)}});const u=Y(()=>t.departments.filter(x=>{const m=r.value.toLowerCase(),b=r.value===""||x.name&&x.name.toLowerCase().includes(m)||x.code&&x.code.toLowerCase().includes(m)||x.description&&x.description.toLowerCase().includes(m),T=l.value===""||x.status===l.value;return b&&T})),c=x=>{if(!x)return"";const m=new Date(x);return new Intl.DateTimeFormat("en-GB",{year:"numeric",month:"short",day:"numeric"}).format(m)},d=()=>{n.value=null,o.value=!1,s.value=!0},p=x=>{n.value={...x},o.value=!0,s.value=!0},h=()=>{s.value=!1,n.value=null},v=async x=>{var m;try{o.value&&((m=n.value)!=null&&m.id)?await t.updateDepartment(n.value.id,x):await t.createDepartment(x),h()}catch(b){console.error("Error saving department:",b)}},$=async x=>{if(x&&confirm("Are you sure you want to delete this department?"))try{await t.deleteDepartment(x)}catch(m){console.error("Error deleting department:",m)}},y=async x=>{if(x.id)try{const m=x.status==="active"?"inactive":"active";await t.updateDepartment(x.id,{...x,status:m})}catch(m){console.error("Error updating department status:",m)}};return(x,m)=>(C(),S(ie,null,[i("div",I4,[m[22]||(m[22]=i("h1",{class:"text-xl md:text-2xl font-bold mb-4 md:mb-6"},"Departments",-1)),i("div",D4,[i("div",L4,[i("div",F4,[m[3]||(m[3]=i("label",{for:"search",class:"sr-only"},"Search departments",-1)),i("div",U4,[m[2]||(m[2]=i("div",{class:"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none"},[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 text-gray-400",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z","clip-rule":"evenodd"})])],-1)),oe(i("input",{type:"text",id:"search","onUpdate:modelValue":m[0]||(m[0]=b=>r.value=b),class:"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm",placeholder:"Search departments..."},null,512),[[ye,r.value]])])]),i("div",{class:"flex flex-col sm:flex-row gap-4"},[i("button",{onClick:d,class:"whitespace-nowrap inline-flex justify-center py-2 px-4 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},m[4]||(m[4]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" Add Department ")]))])])]),a.value?(C(),S("div",N4,m[5]||(m[5]=[i("svg",{class:"animate-spin h-8 w-8 text-blue-500",xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24"},[i("circle",{class:"opacity-25",cx:"12",cy:"12",r:"10",stroke:"currentColor","stroke-width":"4"}),i("path",{class:"opacity-75",fill:"currentColor",d:"M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"})],-1),i("span",{class:"ml-3 text-lg text-gray-600"},"Loading departments...",-1)]))):f.value?(C(),S("div",V4,[m[6]||(m[6]=i("strong",{class:"font-bold"},"Error!",-1)),i("span",H4,R(f.value),1)])):u.value.length===0&&!r.value&&!l.value?(C(),S("div",z4,[m[8]||(m[8]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4"})],-1)),m[9]||(m[9]=i("h3",{class:"text-lg font-medium text-gray-900"},"No departments found",-1)),m[10]||(m[10]=i("p",{class:"mt-2 text-gray-500"},"Get started by creating a new department.",-1)),i("button",{onClick:d,class:"mt-4 inline-flex items-center px-4 py-2 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-blue-600 hover:bg-blue-700 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},m[7]||(m[7]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z","clip-rule":"evenodd"})],-1),ce(" New Department ")]))])):u.value.length===0?(C(),S("div",q4,[m[12]||(m[12]=i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-12 w-12 text-gray-400 mx-auto mb-4",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"})],-1)),m[13]||(m[13]=i("h3",{class:"text-lg font-medium text-gray-900"},"No departments found",-1)),m[14]||(m[14]=i("p",{class:"mt-2 text-gray-500"},"No departments match your search criteria. Try adjusting your filters.",-1)),i("button",{onClick:m[1]||(m[1]=b=>{r.value="",l.value=""}),class:"mt-4 inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"},m[11]||(m[11]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-5 w-5 mr-2",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z","clip-rule":"evenodd"})],-1),ce(" Reset Filters ")]))])):(C(),S("div",K4,[i("div",W4,[i("table",J4,[m[19]||(m[19]=i("thead",{class:"bg-gray-50"},[i("tr",null,[i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/4"}," Department "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"}," Role "),i("th",{scope:"col",class:"px-4 sm:px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider w-1/8"}," Created At "),i("th",{scope:"col",class:"relative px-4 sm:px-6 py-3 w-1/8"},[i("span",{class:"sr-only"},"Actions")])])],-1)),i("tbody",G4,[(C(!0),S(ie,null,xe(u.value,b=>(C(),S("tr",{key:b.id,class:"hover:bg-gray-50"},[i("td",Y4,[i("div",Z4,[i("div",X4,R(b.name),1)])]),i("td",Q4,R(b.scope),1),i("td",e3,R(c(b.createdAt)),1),i("td",t3,[i("div",s3,[i("button",{onClick:T=>p(b),class:"text-indigo-600 hover:text-indigo-900",title:"Edit"},m[15]||(m[15]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{d:"M13.586 3.586a2 2 0 112.828 2.828l-.793.793-2.828-2.828.793-.793zM11.379 5.793L3 14.172V17h2.828l8.38-8.379-2.83-2.828z"})],-1)]),8,o3),i("button",{onClick:T=>y(b),class:ne(b.status==="active"?"text-yellow-600 hover:text-yellow-900":"text-green-600 hover:text-green-900"),title:b.status==="active"?"Deactivate":"Activate"},[b.status==="active"?(C(),S("svg",r3,m[16]||(m[16]=[i("path",{"fill-rule":"evenodd",d:"M13.477 14.89A6 6 0 015.11 6.524l8.367 8.368zm1.414-1.414L6.524 5.11a6 6 0 018.367 8.367zM18 10a8 8 0 11-16 0 8 8 0 0116 0z","clip-rule":"evenodd"},null,-1)]))):(C(),S("svg",i3,m[17]||(m[17]=[i("path",{"fill-rule":"evenodd",d:"M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z","clip-rule":"evenodd"},null,-1)])))],10,n3),i("button",{onClick:T=>$(b.id),class:"text-red-600 hover:text-red-900",title:"Delete"},m[18]||(m[18]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-4 w-4 sm:h-5 sm:w-5",viewBox:"0 0 20 20",fill:"currentColor"},[i("path",{"fill-rule":"evenodd",d:"M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z","clip-rule":"evenodd"})],-1)]),8,l3)])])]))),128))])])]),i("div",a3,[i("div",c3,[i("div",u3,[i("div",null,[i("p",d3,[m[20]||(m[20]=ce(" Showing ")),i("span",f3,R(u.value.length),1),m[21]||(m[21]=ce(" departments "))])])])])])]))]),ge(O4,{show:s.value,department:n.value,isEdit:o.value,onClose:h,onSave:v},null,8,["show","department","isEdit"])],64))}}),cs=Op({history:up(),routes:[{path:"/",redirect:"/dashboard"},{path:"/login",name:"Login",component:Om},{path:"/dashboard",name:"Dashboard",component:fg,meta:{requiresAuth:!0}},{path:"/customers",name:"Customers",component:Yb,meta:{requiresAuth:!0}},{path:"/projects",name:"Projects",component:U2,meta:{requiresAuth:!0}},{path:"/tickets",name:"Tickets",component:Qy,meta:{requiresAuth:!0}},{path:"/tasks",name:"Tasks",component:jx,meta:{requiresAuth:!0}},{path:"/messages",name:"Messages",component:g5,meta:{requiresAuth:!0}},{path:"/users",name:"Users",component:v4,meta:{requiresAuth:!0}},{path:"/departments",name:"Departments",component:p3,meta:{requiresAuth:!0}}]});cs.beforeEach((e,t,s)=>{const o=mi();e.meta.requiresAuth?o.isAuthenticated?s():s("/login"):e.path==="/login"&&o.isAuthenticated?s("/dashboard"):s()});const h3={class:"h-full flex flex-col"},m3={class:"flex-grow py-4 overflow-y-auto"},g3={class:"space-y-1"},w3=["innerHTML"],b3={class:"mt-auto"},v3={class:"border-t border-gray-200 p-4"},y3={class:"flex items-center justify-between"},x3={class:"flex items-center"},k3={key:0,class:"h-10 w-10 rounded-full overflow-hidden"},_3=["src"],C3={key:1,class:"h-10 w-10 rounded-full bg-blue-600 text-white flex items-center justify-center text-base font-medium"},$3={class:"ml-3"},E3={class:"text-sm font-medium text-gray-800"},S3={class:"text-xs text-gray-500"},A3=Pe({__name:"Sidebar",emits:["close-menu"],setup(e,{emit:t}){const s=Mc(),o=mi(),n=t,r=Y(()=>o.currentUser||{id:0,name:"Guest User",email:"",role:"Guest",avatar:null}),l=d=>{var p;return d&&((p=d.trim()[0])==null?void 0:p.toUpperCase())||""},a=[{name:"Dashboard",path:"/dashboard",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M2 10a8 8 0 018-8v8h8a8 8 0 11-16 0z" />
            <path d="M12 2.252A8.014 8.014 0 0117.748 8H12V2.252z" />
          </svg>`},{name:"Customers",path:"/customers",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z" />
          </svg>`},{name:"Projects",path:"/projects",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M7 3a1 1 0 000 2h6a1 1 0 100-2H7zM4 7a1 1 0 011-1h10a1 1 0 110 2H5a1 1 0 01-1-1zM2 11a2 2 0 012-2h12a2 2 0 012 2v4a2 2 0 01-2 2H4a2 2 0 01-2-2v-4z" />
          </svg>`},{name:"Tickets",path:"/tickets",icon:`<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="w-5 h-5">
            <path fill-rule="evenodd" d="M1.5 6.375c0-1.036.84-1.875 1.875-1.875h17.25c1.035 0 1.875.84 1.875 1.875v3.026a.75.75 0 0 1-.375.65 2.249 2.249 0 0 0 0 3.898.75.75 0 0 1 .375.65v3.026c0 1.035-.84 1.875-1.875 1.875H3.375A1.875 1.875 0 0 1 1.5 17.625v-3.026a.75.75 0 0 1 .374-.65 2.249 2.249 0 0 0 0-3.898.75.75 0 0 1-.374-.65V6.375Zm15-1.125a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-1.5 0V6a.75.75 0 0 1 .75-.75Zm.75 4.5a.75.75 0 0 0-1.5 0v.75a.75.75 0 0 0 1.5 0v-.75Zm-.75 3a.75.75 0 0 1 .75.75v.75a.75.75 0 0 1-1.5 0v-.75a.75.75 0 0 1 .75-.75Zm.75 4.5a.75.75 0 0 0-1.5 0V18a.75.75 0 0 0 1.5 0v-.75ZM6 12a.75.75 0 0 1 .75-.75H12a.75.75 0 0 1 0 1.5H6.75A.75.75 0 0 1 6 12Zm.75 2.25a.75.75 0 0 0 0 1.5h3a.75.75 0 0 0 0-1.5h-3Z" clip-rule="evenodd" />
          </svg>`},{name:"Tasks",path:"/tasks",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
            <path fill-rule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd" />
          </svg>`},{name:"Messages",path:"/messages",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M18 10c0 3.866-3.582 7-8 7a8.841 8.841 0 01-4.083-.98L2 17l1.338-3.123C2.493 12.767 2 11.434 2 10c0-3.866 3.582-7 8-7s8 3.134 8 7zM7 9H5v2h2V9zm8 0h-2v2h2V9zM9 9h2v2H9V9z" clip-rule="evenodd" />
          </svg>`},{name:"Users",path:"/users",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M9 6a3 3 0 11-6 0 3 3 0 016 0zM17 6a3 3 0 11-6 0 3 3 0 016 0zM12.93 17c.046-.327.07-.66.07-1a6.97 6.97 0 00-1.5-4.33A5 5 0 0119 16v1h-6.07zM6 11a5 5 0 015 5v1H1v-1a5 5 0 015-5z" />
          </svg>`},{name:"Departments",path:"/departments",icon:`<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path d="M10.394 2.08a1 1 0 00-.788 0l-7 3a1 1 0 000 1.84L5.25 8.051a.999.999 0 01.356-.257l4-1.714a1 1 0 11.788 1.838L7.667 9.088l1.94.831a1 1 0 00.787 0l7-3a1 1 0 000-1.838l-7-3zM3.31 9.397L5 10.12v4.102a8.969 8.969 0 00-1.05-.174 1 1 0 01-.89-.89 11.115 11.115 0 01.25-3.762zM9.3 16.573A9.026 9.026 0 007 14.935v-3.957l1.818.78a3 3 0 002.364 0l5.508-2.361a11.026 11.026 0 01.25 3.762 1 1 0 01-.89.89 8.968 8.968 0 00-5.35 2.524 1 1 0 01-1.4 0zM6 18a1 1 0 001-1v-2.065a8.935 8.935 0 00-2-.712V17a1 1 0 001 1z" />
          </svg>`}],f=()=>{o.logout(),s.push("/login")},u=()=>{n("close-menu")},c=()=>{u()};return(d,p)=>{const h=li("router-link");return C(),S("aside",h3,[i("div",{class:"p-4 border-b border-gray-200 flex justify-between items-center"},[p[1]||(p[1]=ws('<div class="flex items-center space-x-2" data-v-60b9a1c5><h1 class="text-2xl font-bold text-gray-800" data-v-60b9a1c5>JNX</h1><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="currentColor" class="text-blue-500 w-5 h-5" data-v-60b9a1c5><path fill-rule="evenodd" d="M15.75 1.5a6.75 6.75 0 0 0-6.651 7.906c.067.39-.032.717-.221.906l-6.5 6.499a3 3 0 0 0-.878 2.121v2.818c0 .414.336.75.75.75H6a.75.75 0 0 0 .75-.75v-1.5h1.5A.75.75 0 0 0 9 19.5V18h1.5a.75.75 0 0 0 .53-.22l2.658-2.658c.19-.189.517-.288.906-.22A6.75 6.75 0 1 0 15.75 1.5Zm0 3a.75.75 0 0 0 0 1.5A2.25 2.25 0 0 1 18 8.25a.75.75 0 0 0 1.5 0 3.75 3.75 0 0 0-3.75-3.75Z" clip-rule="evenodd" data-v-60b9a1c5></path></svg><h1 class="text-2xl font-bold text-gray-800" data-v-60b9a1c5>Task</h1></div>',1)),i("button",{onClick:u,class:"md:hidden text-gray-600 hover:text-gray-800","aria-label":"Close menu"},p[0]||(p[0]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"})],-1)]))]),i("nav",m3,[i("ul",g3,[(C(),S(ie,null,xe(a,v=>i("li",{key:v.name},[ge(h,{to:v.path,class:"flex items-center px-4 py-2 text-gray-700 hover:bg-gray-100 hover:text-gray-900","active-class":"bg-blue-50 text-blue-700 font-medium",onClick:c},{default:Na(()=>[i("span",{class:"mr-3",innerHTML:v.icon},null,8,w3),ce(" "+R(v.name),1)]),_:2},1032,["to"])])),64))])]),i("div",b3,[i("div",v3,[i("div",y3,[i("div",x3,[r.value.avatar?(C(),S("div",k3,[i("img",{src:r.value.avatar,alt:"User avatar",class:"h-full w-full object-cover"},null,8,_3)])):(C(),S("div",C3,R(l(r.value.name)),1)),i("div",$3,[i("p",E3,R(r.value.name),1),i("p",S3,R(r.value.role),1)])]),i("button",{onClick:f,class:"p-2 text-gray-600 hover:text-gray-900 rounded-full hover:bg-gray-100",title:"Logout"},p[2]||(p[2]=[i("svg",{xmlns:"http://www.w3.org/2000/svg",fill:"none",viewBox:"0 0 24 24","stroke-width":"1.5",stroke:"currentColor",class:"w-5 h-5"},[i("path",{"stroke-linecap":"round","stroke-linejoin":"round",d:"M15.75 9V5.25A2.25 2.25 0 0 0 13.5 3h-6a2.25 2.25 0 0 0-2.25 2.25v13.5A2.25 2.25 0 0 0 7.5 21h6a2.25 2.25 0 0 0 2.25-2.25V15m3 0 3-3m0 0-3-3m3 3H9"})],-1)]))])])])])}}}),la=gi(A3,[["__scopeId","data-v-60b9a1c5"]]),P3={class:"app-container"},T3={xmlns:"http://www.w3.org/2000/svg",class:"h-6 w-6",fill:"none",viewBox:"0 0 24 24",stroke:"currentColor"},j3={key:0,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M6 18L18 6M6 6l12 12"},R3={key:1,"stroke-linecap":"round","stroke-linejoin":"round","stroke-width":"2",d:"M4 6h16M4 12h16M4 18h16"},M3=Pe({__name:"App",setup(e){const t=Dp(),s=Y(()=>t.path!=="/login"),o=Y(()=>t.path==="/login"),n=W(!1),r=()=>{n.value=!n.value};return(l,a)=>{const f=li("router-view");return C(),S("div",P3,[s.value?(C(),S("button",{key:0,onClick:r,class:"md:hidden fixed top-4 left-4 z-20 bg-slate-800 text-white p-2 rounded-md","aria-label":"Toggle menu"},[(C(),S("svg",T3,[n.value?(C(),S("path",j3)):(C(),S("path",R3))]))])):de("",!0),s.value&&n.value?(C(),zs(la,{key:1,class:"sidebar-mobile",onCloseMenu:a[0]||(a[0]=u=>n.value=!1)})):de("",!0),s.value?(C(),zs(la,{key:2,class:"sidebar-desktop"})):de("",!0),i("main",{class:ne(["main-content",{"no-padding":o.value}])},[ge(f)],2)])}}}),B3=gi(M3,[["__scopeId","data-v-77664bd6"]]),Ri=$0(B3);Ri.use(A0());Ri.use(cs);Ri.mount("#app");
